<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		
		<!-- 内联脚本：在DOM渲染前隐藏指定元素 -->
		<script>
		(function() {
			try {
				// 从localStorage读取需要隐藏的元素ID
				var hiddenElementsStr = localStorage.getItem('hiddenElements');
				if (hiddenElementsStr) {
					var hiddenElements = JSON.parse(hiddenElementsStr);
					if (hiddenElements && hiddenElements.length) {
						// 创建样式标签
						var style = document.createElement('style');
						style.type = 'text/css';
						
						// 为每个元素生成CSS规则
						var css = hiddenElements.map(function(id) {
							return '#' + id + ' { display: none !important; }';
						}).join('\n');
						
						// 添加CSS到样式标签
						if (style.styleSheet) {
							style.styleSheet.cssText = css; // 兼容IE
						} else {
							style.appendChild(document.createTextNode(css));
						}
						
						// 将样式标签添加到head
						document.head.appendChild(style);
						console.log('页面加载前已隐藏元素:', hiddenElements);
					}
				}
			} catch (e) {
				console.error('预加载隐藏元素时出错:', e);
			}
		})();
		</script>
		
		<link rel="stylesheet" href="./css/jquery.mobile.css" />
		<link rel="stylesheet" href="./css/jquery.extra-themes.css" />
		<!--link rel="stylesheet" href="./css/common.css"-->
		<script src="./js/jquery.min.js"></script>
		<script src="./js/jquery.mobile.js"></script>
		<script src="./js/webapp-language.js"></script>
		<script src="./js/login.js"></script>
		<script> 
		// 页面加载时应用保存的颜色主题
		$(document).ready(function() {
			// 从localStorage获取保存的颜色
			var savedColor = window.localStorage.getItem(window.location.host + "-theme-color");
			if (savedColor) {
				// 应用保存的颜色设置
				document.body.style.setProperty('--sk_color', savedColor, 'important');
				console.log("配置页面应用保存的颜色设置:", savedColor);
				
				// 应用到其他可能使用该颜色的元素
				$('.ui-btn-active').css('background-color', savedColor);
				$('.ui-page-theme-b a.ui-btn').css('color', savedColor);
			}
			
			// 根据用户类型决定是否隐藏网络配置选项卡
			const userInfoStr = localStorage.getItem('currentUser');
			if (userInfoStr) {
				const userInfo = JSON.parse(userInfoStr);
				// 只对Install用户特殊处理
				if (userInfo.username === 'Install' && 
					userInfo.restrictedElements && 
					userInfo.restrictedElements.includes("#networkConfig")) {
					// 初始隐藏网络配置面板
					$("#networkConfig").css("display", "none");
					
					// 添加点击事件处理
					$(document).on('click', "[data-goto-id='networkConfig'], #network", function(e) {
						// 让原始事件执行完成后立即隐藏网络配置面板
						setTimeout(function() {
							$("#networkConfig").hide();
						}, 0);
					});
				} else {
					// 非Install用户显示网络配置
					$("#networkConfig").css("display", "");
				}
			} else {
				// 默认显示网络配置
				$("#networkConfig").css("display", "");
			}
		});
		</script>
		<title data-desc="config">Configure</title>
		<style>
			/* 移除默认的网络配置隐藏样式 */
			/* #networkConfig {
				display: none;
			} */
			#sysConfig {
				display: none;
			}
			#popup-show-info {
				min-width: 200px;
				max-width: 400px;
			}
			#popup-ask-for-sure {
				min-width: 200px;
				max-width: 400px;
			}
			#popup-form-input2 {
				min-width: 200px;
				max-width: 400px;
			}
			#popup-form-input3 {
				min-width: 200px;
				max-width: 400px;
			}
			#tab-chn-1 {
				display: none;
			}
			#tab-chn-2 {
				display: none;
			}
			.cfg-chn-1 {
				display: none;
			}
			.cfg-chn-2 {
				display: none;
			}
			.cfg-chn-3 {
				display: none;
			}
			.alg-chn-1 {
				display: none;
			}
			.alg-chn-2 {
				display: none;
			}
			#goto-popup {
				display: none;
			}
			#popup {
				min-width: 200px;
				max-width: 400px;
			}
			.hide-anchor {
				display: none;
			}
			.ui-icon-global:after {
				background-image: url("./css/images/icons-png/global-white.png");
				background-size: 14px 14px;
			}
			.ui-icon-slider:after {
				background-image: url("./css/images/icons-png/slider-white.png");
				background-size: 14px 14px;
			}
			.scroll-downwarp {
				position: relative;
				width: 100%;
				height: 0;
				overflow: hidden;
				text-align: center;
			}
			.scroll-hardware {
				-webkit-transform: translateZ(0);
				transform: translateZ(0);
			}
			.scroll-downwarp-reset {
				-webkit-transition: height 300ms;
				transition: height 300ms;
			}
			.downwarp-content {
				position: absolute;
				left: 0;
				bottom: 0;
				width: 100%;
				min-height: 30px;
				padding: 10px 0;
			}
			.downwarp-tip {
				display: inline-block;
				font-size: 12px;
				color: gray;
				vertical-align: middle;
			}
			.downwarp-progress {
				display: inline-block;
				width: 16px;
				height: 16px;
				border-radius: 50%;
				border: 1px solid gray;
				margin-right: 8px;
				border-bottom-color: transparent;
				vertical-align: middle;
			}
			.ui-selectmenu.ui-popup .ui-input-search {
				margin-left: 0.5em;
				margin-right: 0.5em;
			}
			.ui-selectmenu.ui-dialog .ui-content {
				padding-top: 0;
			}
			.ui-selectmenu.ui-dialog .ui-selectmenu-list {
				margin-top: 0;
			}
			.ui-selectmenu.ui-popup .ui-selectmenu-list li.ui-first-child .ui-btn {
				border-top-width: 1px;
				-webkit-border-radius: 0;
				border-radius: 0;
			}
			.ui-selectmenu.ui-dialog .ui-header {
				border-bottom-width: 1px;
			}
			#TestConfig {
				height: 100%;
				width: 20%;
				position: absolute;
				left: 40%;
				top: -18px;
				font-size: 18px;
				line-height: 52px;
				text-align: center;
				font-weight: var(--theme_title_font_weight);
				text-transform: var(--theme_title_text_transform);
				color: var(--theme_title_color);
				text-decoration: none;
			}
			input[type="number"]::-webkit-inner-spin-button,
			input[type="number"]::-webkit-outer-spin-button {
				-webkit-appearance: none;
				margin: 0;
			}
			input[type="number"] {
				-moz-appearance: textfield;
			}

			#change_ch_select {
				z-index: 1501;
				position: absolute;
				left: 48px;
				top: 13px;
			}
		</style>
	</head>

	<body>
		<div
			id="page-config"
			data-role="none"
			style="overflow-x: hidden; background: #fff; min-height: 887px"
		>
			<div class="title index_hide_group">
				<a class="title_back" href="./index.html" data-ajax="false"></a>
				<p id="TestConfig" data-desc="config">Configure</p>
				<select id="change_ch_select" data-role="none" placeholder="undefind">
					<option value="ch0">CH1</option>
					<option value="ch1">CH2</option>
				</select>
				<a class="btn-refresh title_refresh"></a>
			</div>

			<div class="title_hide index_hide_group"></div>

			<div
				id="main"
				role="main"
				class="ui-content"
				style="min-height: 810px; overflow: hidden"
			></div>
			<div data-role="footer" data-position="fixed" data-fullscreen="false">
				<a style="visibility: hidden"></a>
				<a
					href="#"
					id="btn-submit"
					data-role="button"
					class="href-btn"
					data-desc="confirm"
					>确定</a
				>
				<a
					href="#"
					id="btn-cancel"
					data-role="button"
					class="href-btn"
					data-desc="cancel"
					>取消</a
				>
			</div>
			<a
				id="show-info"
				href="#popup-show-info"
				data-rel="popup"
				data-position-to="window"
				class="hide-anchor"
			></a>
			<div data-role="popup" id="popup-show-info" data-dismissible="false">
				<div data-role="header">
					<h1></h1>
				</div>
				<div role="main" class="ui-content">
					<h3></h3>
					<p></p>
					<a
						href="#"
						class="ui-btn ui-corner-all ui-shadow ui-btn-inline"
						data-rel="back"
						data-transition="flow"
						data-desc="confirm"
						>确定</a
					>
				</div>
			</div>
			<a
				id="ask-for-sure"
				href="#popup-ask-for-sure"
				data-rel="popup"
				data-position-to="window"
				class="hide-anchor"
			></a>
			<div
				data-role="popup"
				id="popup-ask-for-sure"
				data-dismissible="false"
				data-popup-title=""
			>
				<div data-role="header">
					<h1></h1>
				</div>
				<div role="main" class="ui-content">
					<h3></h3>
					<p></p>
					<a
						href="#"
						class="ui-btn ui-corner-all ui-shadow ui-btn-inline"
						data-rel="back"
						data-desc="cancel"
						>取消</a
					>
					<a
						id="btn-ask-for-sure"
						href="#"
						class="ui-btn ui-corner-all ui-shadow ui-btn-inline"
						data-rel="back"
						data-transition="flow"
						data-desc="confirm"
						>确定</a
					>
				</div>
			</div>
			<a
				id="form-input2"
				href="#popup-form-input2"
				data-rel="popup"
				data-position-to="window"
				class="hide-anchor"
			></a>
			<div
				data-role="popup"
				id="popup-form-input2"
				data-dismissible="false"
				data-popup-title=""
			>
				<div data-role="header">
					<h1></h1>
				</div>
				<div role="main" class="ui-content">
					<h3></h3>
					<form>
						<div>
							<label for="form-input2-1" class="ui-hidden-accessible"></label>
							<input id="form-input2-1" value="" placeholder="" type="text" />
							<label for="form-input2-2" class="ui-hidden-accessible"></label>
							<input id="form-input2-2" value="" placeholder="" type="text" />
							<p class="form-input-note">&nbsp;</p>
							<a
								href="#"
								class="ui-btn ui-corner-all ui-shadow ui-btn-inline"
								data-rel="back"
								data-desc="cancel"
								>取消</a
							>
							<a
								id="btn-form-input2"
								href="#"
								class="ui-btn ui-corner-all ui-shadow ui-btn-inline"
								data-desc="confirm"
								>确定</a
							>
						</div>
					</form>
				</div>
			</div>
			<a
				id="form-input3"
				href="#popup-form-input3"
				data-rel="popup"
				data-position-to="window"
				class="hide-anchor"
			></a>
			<div
				data-role="popup"
				id="popup-form-input3"
				data-dismissible="false"
				data-popup-title=""
			>
				<div data-role="header">
					<h1></h1>
				</div>
				<div role="main" class="ui-content">
					<h3></h3>
					<form>
						<div>
							<label for="form-input3-1" class="ui-hidden-accessible"></label>
							<input id="form-input3-1" value="" placeholder="" type="text" />
							<label for="form-input3-2" class="ui-hidden-accessible"></label>
							<input id="form-input3-2" value="" placeholder="" type="text" />
							<label for="form-input3-3" class="ui-hidden-accessible"></label>
							<input id="form-input3-3" value="" placeholder="" type="text" />
							<p class="form-input-note">&nbsp;</p>
							<a
								href="#"
								class="ui-btn ui-corner-all ui-shadow ui-btn-inline"
								data-rel="back"
								data-desc="cancel"
								>取消</a
							>
							<a
								id="btn-form-input3"
								href="#"
								class="ui-btn ui-corner-all ui-shadow ui-btn-inline"
								data-desc="confirm"
								>确定</a
							>
						</div>
					</form>
				</div>
			</div>
			<script id="ssid-list-template" type="text/x-handlebars-template">
				<ul data-role="listview" data-split-icon="delete" data-inset="true">
					<li data-role="list-divider">SSID<i>/Password</i><span
							data-desc="list"
						>列表</span></li>
					{{#each this}}
						<li><a
								href="#"
								class="btn-popup"
								data-popup-type="form-input2"
								data-popup-title="edit-ssid"
								data-list-index="{{@index}}"
							><img
									src="../css/images/icons-png/edit-white.png"
									alt="Edit"
									class="ui-li-icon ui-corner-all"
								/><h2
									id="networkConfig-wifi-staMode-apList-{{@index}}-ssid"
								>{{ssid}}</h2><i
									id="networkConfig-wifi-staMode-apList-{{@index}}-password"
								>{{password}}</i></a><a
								href="#"
								class="btn-popup"
								data-popup-type="ask-for-sure"
								data-popup-title="delete-ssid"
								data-list-index="{{@index}}"
							></a></li>
					{{/each}}
					<li data-icon="plus"><a
							href="#"
							class="btn-popup"
							data-popup-type="form-input2"
							data-popup-title="add-ssid"
						>&nbsp;</a></li>
				</ul>
			</script>
			<script id="configs-template" type="text/x-handlebars-template">
				<div class="index_hide_group">
					<table class="menu" rules="none" cellspacing="5%">
				          	<td><li><a href="#" class="xbtn-nav menu_checked" data-goto-id="mediaConfig" data-desc="media-config" id="media">媒体配置</a></li></td>
						<td ><li><a href="#" class="xbtn-nav" data-goto-id="algConfig" data-desc="alg-config" id="alg">算法配置</a></li></td>
						<td ><li><a href="#" class="xbtn-nav" data-goto-id="networkConfig" data-desc="network-config" id="network">网络配置</a></li></td>
						<td ><li><a href="#" class="xbtn-nav" data-goto-id="sysConfig" data-desc="sys-config" id="sys">系统配置</a></li></td>
				  		</table>
				</div>
				<div id="mediaConfig" class="tab-configs">
					<div class="configmenu">

						<div id="cfg-chn-0" class="media-chn-conf" {{{hideHardware "WFTR20S3"}}}>
						<div class="item">
							<h1><p data-desc="image-process">图像处理</p></h1>

							<div class="config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-mirrorEnable" {{#if mediaConfig.mirrorEnable}}checked="checked"{{/if}}/>
								<label data-role="none" for="mediaConfig-mirrorEnable"></label>
								<p data-desc="image-mirror">画面镜像</p>
							</div>

							<div class="config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-flipEnable" {{#if mediaConfig.flipEnable}}checked="checked"{{/if}}/>
								<label data-role="none" for="mediaConfig-flipEnable"></label>
								<p data-desc="image-flip">画面翻转</p>
							</div>

						</div>

					    <div class="item" >
							<div style="margin-top:30px;width:100%;">
								<table class="menu menu_a" rules="none" cellspacing="5%">
									<td class="sel-td 1106-td" ><li>
										<a id="sel-ch4"  class="xsel-chn menu_checked" value="ch4" data-tabchn-id="tab-chn-3" data-desc="vo-stream">AHD Stream</a>
									</li></td>
					            	<td class="sel-td 1106-td" ><li>
					            		<a id="sel-ch1" class="xsel-chn" value="ch1" data-tabchn-id="tab-chn-0" data-desc="main-stream">主码流</a>
					            	</li></td>
									<td class="sel-td 1106-td"><li>
										<a id="sel-ch2" class="xsel-chn" value="ch2" data-tabchn-id="tab-chn-1" data-desc="sub-stream">子码流</a>
									</li></td>

									<td class="sel-td sel-ada32ir-td"><li>
										<a id="sel-ch3" class="xsel-chn" value="ch3" data-tabchn-id="tab-chn-2" data-desc="pic-stream">图片流</a>
									</li></td>
					    		</table>
				    		</div>

				    		<div id="tab-chn-0" class="tab-chn-conf">
				    			<div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-mainEncode" data-desc="enEncode">编码格式</label>
						    		<div><select class="custom-select" id="mediaConfig-mainEncode" data-role="none" value="{{mediaConfig.mainEncode}}">
								        <option value="H264" {{#equal mediaConfig.mainStream.enEncode "H264"}}selected="selected"{{/equal}}>H264</option>
										<option value="H265" {{#equal mediaConfig.mainStream.enEncode "H265"}}selected="selected"{{/equal}}>H265</option>
								    </select></div>
							    </div>

							    <div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-mainResolution" data-desc="resolution">分辨率</label>
						    		<div><select class="custom-select" id="mediaConfig-mainResolution" data-role="none" value="{{mediaConfig.mainResolution}}">
										<option  value="CIF_N" {{#equal mediaConfig.mainResolution "CIF_N"}}selected="selected"{{/equal}}>CIF_N</option>
										<option  value="CIF_P" {{#equal mediaConfig.mainResolution "CIF_P"}}selected="selected"{{/equal}}>CIF_P</option>
										<option value="D1_N" {{#equal mediaConfig.mainResolution "D1_N"}}selected="selected"{{/equal}}>D1_N</option>
										<option  value="D1_P" {{#equal mediaConfig.mainResolution "D1_P"}}selected="selected"{{/equal}}>D1_P</option>
										<option  value="720P" {{#equal mediaConfig.mainResolution "720P"}}selected="selected"{{/equal}}>720P</option>
										<option  value="1080P" {{#equal mediaConfig.mainResolution "1080P"}}selected="selected"{{/equal}}>1080P</option>
								    </select></div>
							    </div>

							    <div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-mainFramerate" data-desc="framerate">帧率</label>
						    		<div><select class="custom-select" id="mediaConfig-mainFramerate" data-role="none" value="{{mediaConfig.mainFramerate}}">
								       	<option value="2" {{#equal mediaConfig.mainFramerate 2}}selected="selected"{{/equal}}>2fps</option>
										<option value="5" {{#equal mediaConfig.mainFramerate 5}}selected="selected"{{/equal}}>5fps</option>
										<option value="6" {{#equal mediaConfig.mainFramerate 6}}selected="selected"{{/equal}}>6fps</option>
										<option value="10" {{#equal mediaConfig.mainFramerate 10}}selected="selected"{{/equal}}>10fps</option>
										<option value="12" {{#equal mediaConfig.mainFramerate 12}}selected="selected"{{/equal}}>12fps</option>
										<option value="20" {{#equal mediaConfig.mainFramerate 20}}selected="selected"{{/equal}}>20fps</option>
										<option value="24" {{#equal mediaConfig.mainFramerate 24}}selected="selected"{{/equal}}>24fps</option>
										<option value="25" {{#equal mediaConfig.mainFramerate 25}}selected="selected"{{/equal}}>25fps</option>
										<option value="30" {{#equal mediaConfig.mainFramerate 30}}selected="selected"{{/equal}}>30fps</option>
								    </select></div>
							    </div>

							    <div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-mainBitrate" data-desc="bitrate">码率</label>
						    		<div><select class="custom-select" id="mediaConfig-mainBitrate" data-role="none" value="{{mediaConfig.mainBitrate}}">
										<option value="1024" {{#equal mediaConfig.mainBitrate 1024}}selected="selected"{{/equal}}>1Mbps</option>
										<option value="2048" {{#equal mediaConfig.mainBitrate 2048}}selected="selected"{{/equal}}>2Mbps</option>
										<option value="3072" {{#equal mediaConfig.mainBitrate 3072}}selected="selected"{{/equal}}>3Mbps</option>
										<option value="4096" {{#equal mediaConfig.mainBitrate 4096}}selected="selected"{{/equal}}>4Mbps</option>
										<option value="5120" {{#equal mediaConfig.mainBitrate 5120}}selected="selected"{{/equal}}>5Mbps</option>
										<option value="6144" {{#equal mediaConfig.mainBitrate 6144}}selected="selected"{{/equal}}>6Mbps</option>
										<option value="8192" {{#equal mediaConfig.mainBitrate 8192}}selected="selected"{{/equal}}>8Mbps</option>
										<option value="10240" {{#equal mediaConfig.mainBitrate 10240}}selected="selected"{{/equal}}>10Mbps</option>
										<option value="15360" {{#equal mediaConfig.mainBitrate 15360}}selected="selected"{{/equal}}>15Mbps</option>
										<option value="20480" {{#equal mediaConfig.mainBitrate 20480}}selected="selected"{{/equal}}>20Mbps</option>
								   </select></div>
							    </div>

								<div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-mainRcMode" data-desc="rc-mode">码率控制模式</label>
						    		<div><select class="custom-select" id="mediaConfig-mainRcMode" data-role="none" value="{{mediaConfig.mainRcMode}}">
								        <option class="mainstream_rc1" value="VBR" {{#equal mediaConfig.mainRcMode "VBR"}}selected="selected"{{/equal}}>VBR</option>
										<option value="CBR" {{#equal mediaConfig.mainRcMode "CBR"}}selected="selected"{{/equal}}>CBR</option>
										<option class="mainstream_rc1" value="ABR" {{#equal mediaConfig.mainRcMode "ABR"}}selected="selected"{{/equal}}>ABR</option>
								    </select></div>
								</div>

								<div data-role="none" class="input-text-box">
									<label data-role="none" id="tab-mainifrm-interval" for="mediaConfig-mainIframeInt" data-desc="ifrm-interval">I帧间隔</label>
									<input data-role="none" type="number" id="mediaConfig-mainIframeInt" class="ifrm-limit" value="{{mediaConfig.mainIframeInt}}">
								</div>

							</div>

							<div id="tab-chn-1" class="tab-chn-conf">
								<div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-subEncode" data-desc="enEncode">编码格式</label>
						    		<div><select class="custom-select" id="mediaConfig-subEncode" data-role="none" value="{{mediaConfig.subEncode}}">
								       <option value="H264" {{#equal mediaConfig.subEncode "H264"}}selected="selected"{{/equal}}>H264</option>
										<option value="H265" {{#equal mediaConfig.subEncode "H265"}}selected="selected"{{/equal}}>H265</option>
								    </select></div>
							    </div>
							    <div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-subResolution" data-desc="resolution">分辨率</label>
						    		<div><select class="custom-select" id="mediaConfig-subResolution" data-role="none" value="{{mediaConfig.subResolution}}">
										<option value="CIF_N" {{#equal mediaConfig.subResolution "CIF_N"}}selected="selected"{{/equal}}>CIF_N</option>
										<option value="CIF_P" {{#equal mediaConfig.subResolution "CIF_P"}}selected="selected"{{/equal}}>CIF_P</option>
										<option value="D1_N" {{#equal mediaConfig.subResolution "D1_N"}}selected="selected"{{/equal}}>D1_N</option>
										<option value="D1_P" {{#equal mediaConfig.subResolution "D1_P"}}selected="selected"{{/equal}}>D1_P</option>
								    </select></div>
							    </div>
								<div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-subFramerate" data-desc="framerate">帧率</label>
						    		<div><select class="custom-select" id="mediaConfig-subFramerate" data-role="none" value="{{mediaConfig.subFramerate}}">
								        <option value="2" {{#equal mediaConfig.subFramerate 2}}selected="selected"{{/equal}}>2fps</option>
										<option value="6" {{#equal mediaConfig.subFramerate 6}}selected="selected"{{/equal}}>6fps</option>
										<option value="12" {{#equal mediaConfig.subFramerate 12}}selected="selected"{{/equal}}>12fps</option>
										<option value="20" {{#equal mediaConfig.subFramerate 20}}selected="selected"{{/equal}}>20fps</option>
										<option value="25" {{#equal mediaConfig.subFramerate 25}}selected="selected"{{/equal}}>25fps</option>
										<option {{#isCustomer "000025"}}style="display:none;"{{/isCustomer}} value="30" {{#equal mediaConfig.subFramerate 30}}selected="selected"{{/equal}}>30fps</option>

								    </select></div>
							    </div>
							    <div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-subBitrate" data-desc="bitrate">码率</label>
						    		<div><select class="custom-select" id="mediaConfig-subBitrate" data-role="none" value="{{mediaConfig.subBitrate}}">
										<option value="128" {{#equal mediaConfig.subBitrate 128}}selected="selected"{{/equal}}>128Kbps</option>
										<option value="256" {{#equal mediaConfig.subBitrate 256}}selected="selected"{{/equal}}>256Kbps</option>
										<option value="512" {{#equal mediaConfig.subBitrate 512}}selected="selected"{{/equal}}>512Kbps</option>
										<option value="1024" {{#equal mediaConfig.subBitrate 1024}}selected="selected"{{/equal}}>1Mbps</option>
										<option value="2048" {{#equal mediaConfig.subBitrate 2048}}selected="selected"{{/equal}}>2Mbps</option>

								    </select></div>
							    </div>
								<div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-subRcMode" data-desc="rc-mode">码率控制模式</label>
						    		<div><select class="custom-select" id="mediaConfig-subRcMode" data-role="none" value="{{mediaConfig.subRcMode}}">
								       <option value="VBR" {{#equal mediaConfig.subRcMode "VBR"}}selected="selected"{{/equal}}>VBR</option>
										<option value="CBR" {{#equal mediaConfig.subRcMode "CBR"}}selected="selected"{{/equal}}>CBR</option>
										<option value="ABR" {{#equal mediaConfig.subRcMode "ABR"}}selected="selected"{{/equal}}>ABR</option>

								    </select></div>
							    </div>
								<div data-role="none" class="input-text-box">
									<label data-role="none" id="tab-subifrm-interval" for="mediaConfig-subIframeInt" data-desc="ifrm-interval">I帧间隔</label>
									<input data-role="none" type="number" id="mediaConfig-subIframeInt" class="ifrm-limit" value="{{mediaConfig.subIframeInt}}">
							    </div>
							</div>

							<div id="tab-chn-2" class="tab-chn-conf">
								<!-- <div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-jpegResolution" data-desc="resolution">分辨率</label>
						    		<div><select class="custom-select" id="mediaConfig-jpegResolution" data-role="none" value="{{mediaConfig.jpegResolution}}">
											<option value="D1_N" {{#equal mediaConfig.jpegResolution "D1_N"}}selected="selected"{{/equal}}>D1_N</option>
										<option  value="D1_P" {{#equal mediaConfig.jpegResolution "D1_P"}}selected="selected"{{/equal}}>D1_P</option>
										<option value="720P" {{#equal mediaConfig.jpegResolution "720P"}}selected="selected"{{/equal}}>720P</option>
										<option  value="1080P" {{#equal mediaConfig.jpegResolution "1080P"}}selected="selected"{{/equal}}>1080P</option>
								    </select></div>
							    </div> -->
							</div>

							<div id="tab-chn-3" class="tab-chn-conf" style="display: none;" {{{hideNotHardware "ADA32V2 ADA32V3 ADA900V1 ADA32IR DMS31V2 DMS885N ADA42PTZV1 ADA47V1 HDW845V1"}}}>
								<div class="custom-select-box" {{{hideNotCustomer "200001"}}}>
									<label class="single_option_text" for="mediaConfig-voFormat" data-desc="videoFormat">视频格式</label>
						    		<div><select class="custom-select" id="mediaConfig-voFormat" data-role="none" value="{{mediaConfig.voFormat}}">
										<option value="PAL" {{#equal mediaConfig.voFormat "PAL"}}selected="selected"{{/equal}}>PAL</option>
										<option value="NTSC" {{#equal mediaConfig.voFormat "NTSC"}}selected="selected"{{/equal}}>NTSC</option>
										<option value="1080P30" {{#equal mediaConfig.voFormat "1080P30"}}selected="selected"{{/equal}}>1080P30</option>
									</select></div>
							    </div>
								<!-- <div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-voStream-resolution" data-desc="resolution">分辨率</label>
						    		<div><select class="custom-select" id="mediaConfig-voStream-resolution" data-role="none" value="{{mediaConfig.voStream.resolution}}">
										<option value="720P" {{#equal mediaConfig.voStream.resolution "720P"}}selected="selected"{{/equal}}>720P{{{display720PScale}}}</option>
										<option value="1080P" {{#equal mediaConfig.voStream.resolution "1080P"}}selected="selected"{{/equal}} {{{hideHardware "ADA32V3"}}}>1080P</option>

								    </select></div>
							    </div> -->
								<!-- <div class="custom-select-box">
									<label class="single_option_text" for="mediaConfig-voStream-framerate" data-desc="framerate">帧率</label>
						    		<div><select class="custom-select" id="mediaConfig-voStream-framerate" data-role="none" value="{{mediaConfig.voStream.framerate}}">
										<option value="25" {{#equal mediaConfig.voStream.framerate 25}}selected="selected"{{/equal}}>25fps</option>
										<option value="30" {{#equal mediaConfig.voStream.framerate 30}}selected="selected"{{/equal}}>30fps</option>

								    </select></div>
								</div> -->
								<!-- <div class="custom-select-box" {{{hideNotBoard "25"}}}>
									<label class="single_option_text" for="mediaConfig-voStream-split" data-desc="split">显示模式</label>
									<div><select class="custom-select" id="mediaConfig-voStream-split" data-role="none" value="{{mediaConfig.voStream.split}}">
										<option value="0" {{#equal mediaConfig.voStream.split 0}}selected="selected"{{/equal}}>VISCAM</option>
										<option value="1" {{#equal mediaConfig.voStream.split 1}}selected="selected"{{/equal}}>DIV2</option>
										<option value="2" {{#isIRCam ipcIdentification.board}}style="display: none"{{/isIRCam}}{{#equal mediaConfig.voStream.split 2}}selected="selected"{{/equal}}>DIV4</option>
										<option value="5" {{#equal mediaConfig.voStream.split 5}}selected="selected"{{/equal}}>IRCAM</option>
										<option value="6" {{#equal mediaConfig.voStream.split 6}}selected="selected"{{/equal}}>IRCAM_OSD</option>
										<option value="7" {{#equal mediaConfig.voStream.split 7}}selected="selected"{{/equal}}>IRCAM_OSD2</option>
										<option value="8" {{#equal mediaConfig.voStream.split 8}}selected="selected"{{/equal}}>IRCAM_VIS</option>
										<option value="9" {{#equal mediaConfig.voStream.split 9}}selected="selected"{{/equal}}>IRCAM_IR</option>
								    </select></div>
							    </div> -->
								<!-- <div class="custom-select-box" {{{hideNotHardware "ADA32V2 ADA32C4 RCT16947V2 ADA32IR"}}}>
									<label class="single_option_text" for="mediaConfig-extscreenStream-mode" data-desc="CVBSStrem-mode">模式</label>
									<div><select class="custom-select" id="mediaConfig-extscreenStream-mode" data-role="none" value="{{mediaConfig.extscreenStream.mode}}">
									   <option value="NTSC" {{#equal mediaConfig.extscreenStream.mode "NTSC"}}selected="selected"{{/equal}}>NTSC</option>
									   <option value="PAL"  {{#equal mediaConfig.extscreenStream.mode "PAL"}}selected="selected"{{/equal}}>PAL</option>
									</select></div>
								</div> -->
							</div>
				   		</div>
					</div>
					</div>
				 <div class="configmenu">
						<h1><p>{{getKeyLang "audio"}}</p></h1>
						<div class="item" {{#NotAudioSetting ipcIdentification.hardware}}style="display: none;"{{/NotAudioSetting}}>
							<div data-role="none" class="rangeinput">
								<p class="rangeinput_title" data-desc="volume">音量<p>
								<label data-role="none" for="mediaConfig-volume"></label>
								<input data-role="none"  class="rangeinput_input" type="range" id="mediaConfig-volume" min="0" max="50" value="{{mediaConfig.volume}}">
								<p class="rangeinput_value">{{mediaConfig.volume}}<p>
							</div>
						</div>
					</div>
					<div class="configmenu">
						<h1><p>{{getKeyLang "osd display"}}</p></h1>
						<div class="item">
							<h1><p data-desc="switch">Switch</p></h1>

							<div class="sel-td 1106-td config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-showGuiMask-3" {{#mask mediaConfig.showGuiMask 3}}checked="checked"{{/mask}}/>
								<label data-role="none" for="mediaConfig-showGuiMask-3"></label>
								<p data-desc="vo-stream">AHD Stream</p>
							</div>

							<div class="sel-td 1106-td config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-showGuiMask-0" {{#mask mediaConfig.showGuiMask 0}}checked="checked"{{/mask}}/>
								<label data-role="none" for="mediaConfig-showGuiMask-0"></label>
								<p data-desc="main-stream">主码流</p>
							</div>
							<div class="sel-td 1106-td config_checkbox">
								<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-showGuiMask-1" {{#mask mediaConfig.showGuiMask 1}}checked="checked"{{/mask}}/>
								<label data-role="none" for="mediaConfig-showGuiMask-1"></label>
								<p data-desc="sub-stream">子码流</p>
							</div>
							<div class="sel-td config_checkbox sel-ada32ir-td" {{{hideNotWebuiFull "ADA32V2  ADA32IR"}}} {{{hideHardware "ADA42PTZV1 ADA32V3"}}}>
								<input data-role="none" class="checkBtn custom" type="checkbox" id="mediaConfig-showGuiMask-2" {{#mask mediaConfig.showGuiMask 2}}checked="checked"{{/mask}}/>
								<label data-role="none" for="mediaConfig-showGuiMask-2"></label>
								<p data-desc="pic-stream">图片流</p>
							</div>
						</div>
					</div>
				</div>
				<div id="algConfig" class="tab-configs">
					<div class="configmenu">
						<h1><p>{{getKeyLang "audio-settings"}}</p></h1>
					</div>

					<div class="configmenu alg-conf alg-audio-conf" >
						<div class="alg-conf">
								<div class="custom-select-box">
									<label class="single_option_text" for="algConfig-pd-audioType" data-desc="audio-type">audio</label>
									<div><select class="custom-select" id="algConfig-pd-audioType" data-role="none" value="{{algConfig.pd.audioType}}">
										<option value="1" {{#equal algConfig.pd.audioType 1}}selected="selected"{{/equal}} data-desc="DULU">DULU</option>
										<option value="2" {{#equal algConfig.pd.audioType 2}}selected="selected"{{/equal}} data-desc="TRAIN">TRAIN</option>
										<option value="3" {{#equal algConfig.pd.audioType 3}}selected="selected"{{/equal}} data-desc="DO">DO</option>
										<option value="4" {{#equal algConfig.pd.audioType 4}}selected="selected"{{/equal}} data-desc="PHONE">PHONE</option>
										<option value="5" {{#equal algConfig.pd.audioType 5}}selected="selected"{{/equal}} data-desc="DIDU">DIDU</option>
										<option value="6" {{#equal algConfig.pd.audioType 6}}selected="selected"{{/equal}} data-desc="DING">DING</option>
									</select></div>
								</div>
						</div>
					</div>

						<div class="alg-conf configmenu">
							<h1><p>{{getKeyLang "pd-conf"}}</p></h1>
							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-pd-pdsModel" data-desc="pd-Model">检测模型</label>
								<div><select class="custom-select" id="algConfig-pd-pdsModel" data-role="none" value="{{algConfig.pd.pdsModel}}">
									<option value="0" {{#equal algConfig.pd.pdsModel 0}}selected="selected"{{/equal}}>{{getKeyLang "pd-Model-Person"}}</option>
									<option value="1" {{#equal algConfig.pd.pdsModel 1}}selected="selected"{{/equal}} >{{getKeyLang "pd-Model-Car"}}</option>
									<option value="2" {{#equal algConfig.pd.pdsModel 2}}selected="selected"{{/equal}}>{{getKeyLang "pd-Model-PersonCar"}}</option>
								</select></div>
							</div>
							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-pd-pdSensitivity" data-desc="pd-Sensitivity">PDS灵敏度</label>
								<div><select class="custom-select" id="algConfig-pd-pdSensitivity" data-role="none" value="{{algConfig.pd.pdSensitivity}}">
									<option value="0" {{#equal algConfig.pd.pdSensitivity 0}}selected="selected"{{/equal}} data-desc="sensitivity-low">Low</option>
									<option value="1" {{#equal algConfig.pd.pdSensitivity 1}}selected="selected"{{/equal}} data-desc="sensitivity-medium">Medium</option>
									<option value="2" {{#equal algConfig.pd.pdSensitivity 2}}selected="selected"{{/equal}} data-desc="sensitivity-high">High</option>
								</select></div>
							</div>
							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-pd-pdOsdFontSize" data-desc="pd-OsdFontSize">置信值字体</label>
								<div><select class="custom-select" id="algConfig-pd-pdOsdFontSize" data-role="none" value="{{algConfig.pd.pdOsdFontSize}}">
									<option value="0" {{#equal algConfig.pd.pdOsdFontSize 0}}selected="selected" {{/equal}} data-desc="off">OFF</option>
									<option value="1" {{#equal algConfig.pd.pdOsdFontSize 1}}selected="selected"{{/equal}}>1X</option>
									<option value="2" {{#equal algConfig.pd.pdOsdFontSize 2}}selected="selected"{{/equal}}>2X</option>
									<option value="3" {{#equal algConfig.pd.pdOsdFontSize 3}}selected="selected"{{/equal}}>3X</option>
								</select></div>
							</div>
							<div data-role="none" class="input-switch-box" {{{hideHardware "ADA32E1 HDW845V1 ADA32C4"}}}>
								<p data-desc="pd-Alarm-In">触发输入</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pd-pdAlarmIn" {{#if algConfig.pd.pdAlarmIn}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pd-pdAlarmIn"></label>
							</div>
							<div data-role="none" class="input-switch-box" {{{hideHardware "HDW845V1"}}}>
								<p data-desc="pd-test-mode">测试模式</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pd-pdTestMode" {{#if algConfig.pd.pdTestMode}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pd-pdTestMode"></label>
							</div>
							<div data-role="none" class="input-switch-box">
								<p data-desc="display-pdRectPerson">行人检测框</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pd-pdRectPerson" {{#if algConfig.pd.pdRectPerson}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pd-pdRectPerson"></label>
							</div>


							<div data-role="none" class="input-switch-box">
								<p data-desc="mosaicEnable">Face Mosaic</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pd-bMosaic" {{#if algConfig.pd.bMosaic}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pd-bMosaic"></label>
							</div>

							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-pd-pdBlkSize" data-desc="mosaicSize">Mosaic Size</label>
								<div><select class="custom-select" id="algConfig-pd-pdBlkSize" data-role="none" value="{{algConfig.pd.pdBlkSize}}">
									<option value="25" {{#equal algConfig.pd.pdBlkSize 25}}selected="selected"{{/equal}}>25</option>
									<option value="33" {{#equal algConfig.pd.pdBlkSize 33}}selected="selected"{{/equal}}>33</option>
									<option value="50" {{#equal algConfig.pd.pdBlkSize 50}}selected="selected"{{/equal}}>50</option>
								</select></div>
							</div>

							<div class="custom-select-box" >
								<label class="single_option_text" for="algConfig-pd-pdInterval-red" data-desc="pdRed-interval">红区检测间隔</label>
								<div><select class="custom-select" id="algConfig-pd-pdInterval-red" data-role="none" value="{{algConfig.pd.pdInterval.red}}">
									<option value="-1" {{#equal algConfig.pd.pdInterval.red -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
									<option value="0" {{#equal algConfig.pd.pdInterval.red 0}}selected="selected"{{/equal}} data-desc="0s">0s</option>
									<option value="2" {{#equal algConfig.pd.pdInterval.red 2}}selected="selected"{{/equal}} data-desc="2s">2s</option>
									<option value="3" {{#equal algConfig.pd.pdInterval.red 3}}selected="selected"{{/equal}} data-desc="3s">3s</option>
									<option value="4" {{#equal algConfig.pd.pdInterval.red 4}}selected="selected"{{/equal}} data-desc="4s">4s</option>
									<option value="5" {{#equal algConfig.pd.pdInterval.red 5}}selected="selected"{{/equal}} data-desc="5s">5s</option>
									<option value="10" {{#equal algConfig.pd.pdInterval.red 10}}selected="selected"{{/equal}} data-desc="10s">10s</option>
									<option value="30" {{#equal algConfig.pd.pdInterval.red 30}}selected="selected"{{/equal}} data-desc="30s">30s</option>
									<option value="60" {{#equal algConfig.pd.pdInterval.red 60}}selected="selected"{{/equal}} data-desc="60s">60s</option>
									<option value="90" {{#equal algConfig.pd.pdInterval.red 90}}selected="selected"{{/equal}} data-desc="90s">90s</option>
									<option value="120" {{#equal algConfig.pd.pdInterval.red 120}}selected="selected"{{/equal}} data-desc="120s">120s</option>
									<option value="180" {{#equal algConfig.pd.pdInterval.red 180}}selected="selected"{{/equal}} data-desc="180s">180s</option>
									<option value="300" {{#equal algConfig.pd.pdInterval.red 300}}selected="selected"{{/equal}} data-desc="300s">300s</option>
								</select></div>
							</div>
							<div class="custom-select-box pds-det-interval" >
								<label class="single_option_text" for="algConfig-pd-pdInterval-yellow" data-desc="pdYellow-interval">黄区检测间隔</label>
								<div><select class="custom-select" id="algConfig-pd-pdInterval-yellow" data-role="none" value="{{algConfig.pd.pdInterval.yellow}}">
									<option value="-1" {{#equal algConfig.pd.pdInterval.yellow -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
									<option value="0" {{#equal algConfig.pd.pdInterval.yellow 0}}selected="selected"{{/equal}} data-desc="0s">0s</option>
									<option value="2" {{#equal algConfig.pd.pdInterval.yellow 2}}selected="selected"{{/equal}} data-desc="2s">2s</option>
									<option value="3" {{#equal algConfig.pd.pdInterval.yellow 3}}selected="selected"{{/equal}} data-desc="3s">3s</option>
									<option value="4" {{#equal algConfig.pd.pdInterval.yellow 4}}selected="selected"{{/equal}} data-desc="4s">4s</option>
									<option value="5" {{#equal algConfig.pd.pdInterval.yellow 5}}selected="selected"{{/equal}} data-desc="5s">5s</option>
									<option value="10" {{#equal algConfig.pd.pdInterval.yellow 10}}selected="selected"{{/equal}} data-desc="10s">10s</option>
									<option value="30" {{#equal algConfig.pd.pdInterval.yellow 30}}selected="selected"{{/equal}} data-desc="30s">30s</option>
									<option value="60" {{#equal algConfig.pd.pdInterval.yellow 60}}selected="selected"{{/equal}} data-desc="60s">60s</option>
									<option value="90" {{#equal algConfig.pd.pdInterval.yellow 90}}selected="selected"{{/equal}} data-desc="90s">90s</option>
									<option value="120" {{#equal algConfig.pd.pdInterval.yellow 120}}selected="selected"{{/equal}} data-desc="120s">120s</option>
									<option value="180" {{#equal algConfig.pd.pdInterval.yellow 180}}selected="selected"{{/equal}} data-desc="180s">180s</option>
									<option value="300" {{#equal algConfig.pd.pdInterval.yellow 300}}selected="selected"{{/equal}} data-desc="300s">300s</option>
								</select></div>
							</div>
							<div class="custom-select-box pds-det-interval" >
								<label class="single_option_text" for="algConfig-pd-pdInterval-green" data-desc="pdGreen-interval">绿区检测间隔</label>
								<div><select class="custom-select" id="algConfig-pd-pdInterval-green" data-role="none" value="{{algConfig.pd.pdInterval.green}}">
									<option value="-1" {{#equal algConfig.pd.pdInterval.green -1}}selected="selected"{{/equal}} data-desc="off">OFF</option>
									<option value="0" {{#equal algConfig.pd.pdInterval.green 0}}selected="selected"{{/equal}} data-desc="0s">0s</option>
									<option value="2" {{#equal algConfig.pd.pdInterval.green 2}}selected="selected"{{/equal}} data-desc="2s">2s</option>
									<option value="3" {{#equal algConfig.pd.pdInterval.green 3}}selected="selected"{{/equal}} data-desc="3s">3s</option>
									<option value="4" {{#equal algConfig.pd.pdInterval.green 4}}selected="selected"{{/equal}} data-desc="4s">4s</option>
									<option value="5" {{#equal algConfig.pd.pdInterval.green 5}}selected="selected"{{/equal}} data-desc="5s">5s</option>
									<option value="10" {{#equal algConfig.pd.pdInterval.green 10}}selected="selected"{{/equal}} data-desc="10s">10s</option>
									<option value="30" {{#equal algConfig.pd.pdInterval.green 30}}selected="selected"{{/equal}} data-desc="30s">30s</option>
									<option value="60" {{#equal algConfig.pd.pdInterval.green 60}}selected="selected"{{/equal}} data-desc="60s">60s</option>
									<option value="90" {{#equal algConfig.pd.pdInterval.green 90}}selected="selected"{{/equal}} data-desc="90s">90s</option>
									<option value="120" {{#equal algConfig.pd.pdInterval.green 120}}selected="selected"{{/equal}} data-desc="120s">120s</option>
									<option value="180" {{#equal algConfig.pd.pdInterval.green 180}}selected="selected"{{/equal}} data-desc="180s">180s</option>
									<option value="300" {{#equal algConfig.pd.pdInterval.green 300}}selected="selected"{{/equal}} data-desc="300s">300s</option>
								</select></div>
							</div>
							<div data-role="none" class="rangeinput">
								<p class="rangeinput_title">{{getKeyLang "pdAlarmOut-Interval"}}<p>
								<div class="config_checkbox2">
									<p data-desc="auto">AUTO</p>
									<input data-role="none" class="checkBtn custom" id="pdalarmout-auto-enable" type="checkbox" />
									<label data-role="none" for="pdalarmout-auto-enable"></label>
								</div>
								<label data-role="none" for="algConfig-pd-pdAlarmOutInterval"></label>
								<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pd-pdAlarmOutInterval" min="0" max="10000" step="50" value="{{algConfig.pd.pdAlarmOutInterval}}">
								<p  id="pdalarm_interval_value" class="rangeinput_value">{{algConfig.pd.pdAlarmOutInterval}}<p>
							</div>
							<div data-role="none" class="multi_selectbox">
								<p class="multi_selectbox_title">{{getKeyLang "pdAlarmOut-Switch"}}</p>
								<table rules="none" class="multi_selectbox_table">
									<td>
										<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pd-pdAlarmOutEnable-red"{{#if algConfig.pd.pdAlarmOutEnable.red}}checked="checked"{{/if}}>
										<label for="algConfig-pd-pdAlarmOutEnable-red"></label>
									</td>
									<td>
										<input data-role="none" class="checkBtnYellow" type="checkbox" id="algConfig-pd-pdAlarmOutEnable-yellow"{{#if algConfig.pd.pdAlarmOutEnable.yellow}}checked="checked"{{/if}}>
										<label for="algConfig-pd-pdAlarmOutEnable-yellow"></label>
									</td>
									<td>
										<input data-role="none" class="checkBtnGreen" type="checkbox" id="algConfig-pd-pdAlarmOutEnable-green"{{#if algConfig.pd.pdAlarmOutEnable.green}}checked="checked"{{/if}}>
										<label for="algConfig-pd-pdAlarmOutEnable-green"></label>
									</td>
								</table>

							</div>
							<div data-role="none" class="multi_selectbox">
								<p class="multi_selectbox_title">{{getKeyLang "pdRoi-Switch"}}</p>
								<table rules="none" class="multi_selectbox_table">
									<td>
										<input data-role="none" class="checkBtnRed" type="checkbox" id="algConfig-pd-pdRoiEnable-red" {{#if algConfig.pd.pdRoiEnable.red}}checked="checked"{{/if}}>
										<label for="algConfig-pd-pdRoiEnable-red"></label>
									</td>

									<td>
										<input data-role="none" class="checkBtnYellow" type="checkbox" id="algConfig-pd-pdRoiEnable-yellow" {{#if algConfig.pd.pdRoiEnable.yellow}}checked="checked"{{/if}}>
										<label for="algConfig-pd-pdRoiEnable-yellow"></label>

									</td>
									<td>
										<input data-role="none" class="checkBtnGreen" type="checkbox" id="algConfig-pd-pdRoiEnable-green" {{#if algConfig.pd.pdRoiEnable.green}}checked="checked"{{/if}}>
										<label for="algConfig-pd-pdRoiEnable-green"></label>
									</td>
								</table>
							</div>
							<div class="custom-select-box" >
								<label class="single_option_text" for="algConfig-pd-pdRoiGui" data-desc="pdroigui">GUI绘制模式</label>
								<div><select class="custom-select" id="algConfig-pd-pdRoiGui" data-role="none" value="{{algConfig.pd.pdRoiGui}}">
									<option value="0" {{#equal algConfig.pd.pdRoiGui 0}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-hide"}}</option>
									<option value="1" {{#equal algConfig.pd.pdRoiGui 1}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-line"}}</option>
									<option value="2" {{#equal algConfig.pd.pdRoiGui 2}}selected="selected"{{/equal}}>{{getKeyLang "pdroigui-fill"}}</option>
								</select></div>
							</div>
						</div>

						<div class="configmenu">
							<h1 ><p>{{getKeyLang "advanced-settings"}}</p></h1>
							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-pd-pdAlarmOutTrigger" data-desc="pdAlarmOut-Trigger">报警输出方式</label>
								<div><select class="custom-select" id="algConfig-pd-pdAlarmOutTrigger" data-role="none" value="{{algConfig.pd.pdAlarmOutTrigger}}">
									<option value="0" {{#equal algConfig.pd.pdAlarmOutTrigger 0}}selected="selected"{{/equal}} data-desc="High-Level">高电平</option>
									<option value="1"  {{#equal algConfig.pd.pdAlarmOutTrigger 1}} selected="selected"{{/equal}} data-desc="Low-Level">低电平</option>
								</select></div>
							</div>
							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-pd-pdAlarmInTrigger" data-desc="pdAlarmIn-Trigger">报警输入方式</label>
								<div><select class="custom-select" id="algConfig-pd-pdAlarmInTrigger" data-role="none" value="{{algConfig.pd.pdAlarmInTrigger}}">
									<option value="0" {{#equal algConfig.pd.pdAlarmInTrigger 0}}selected="selected"{{/equal}} data-desc="High-Level">高电平</option>
									<option value="1"  {{#equal algConfig.pd.pdAlarmInTrigger 1}} selected="selected"{{/equal}} data-desc="Low-Level">低电平</option>
								</select></div>
							</div>
							<div data-role="none" class="input-switch-box">  
								<p data-desc="shelterEnable">Blockage Alarm</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pd-shelterEnable" {{#if algConfig.pd.shelterEnable}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pd-shelterEnable"></label>
							</div>  
							<div data-role="none" class="rangeinput">   
								<p class="rangeinput_title">{{getKeyLang "pdAlarm-blockage-duration"}}<p>
								<label data-role="none" for="algConfig-pd-ShelterTimelimit"></label>
								<input data-role="none"  class="rangeinput_input" type="range" id="algConfig-pd-ShelterTimelimit" min="0" max="100" step="1" value="{{algConfig.pd.ShelterTimelimit}}">
								<p  id="pdShelterTimelimit" class="rangeinput_value">{{algConfig.pd.ShelterTimelimit}}<p>
							</div>  
							<div data-role="none" class="input-switch-box">   
								<p data-desc="shelterAudio">Blockage Alarm Audio</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pd-shelterAudio" {{#if algConfig.pd.shelterAudio}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pd-shelterAudio"></label>
							</div>
							<div data-role="none" class="input-switch-box">   
								<p data-desc="alarmOutShelter">Alarm Out Shelter</p>
								<input data-role="none" class="switchBtn custom" type="checkbox" id="algConfig-pd-pdAlarmOutShelter" {{#if algConfig.pd.pdAlarmOutShelter}}checked="checked"{{/if}}>
								<label data-role="none" for="algConfig-pd-pdAlarmOutShelter"></label>
							</div>
							<div>
								<div data-role="none" class="input-text-box">
									<label data-role="none" data-desc="RedSensitivity">RedSensitivity</label>
									<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
										<td><li>
											<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pd-pdRedSensitivity-0" class="mult_input1 char-normal" value="{{algConfig.pd.pdRedSensitivity.[0]}}">
										</li></td>
										<td><li>
											<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pd-pdRedSensitivity-1" class="mult_input2 char-normal" value="{{algConfig.pd.pdRedSensitivity.[1]}}">
										</li></td>
										<td><li>
											<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pd-pdRedSensitivity-2" class="mult_input3 char-normal" value="{{algConfig.pd.pdRedSensitivity.[2]}}">
										</li></td>
									</table>
								</div>
								<div data-role="none" class="input-text-box">
									<label data-role="none" data-desc="pdYellowSensitivity">pdYellowSensitivity</label>
									<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
										<td><li>
											<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pd-pdYellowSensitivity-0" class="mult_input1 char-normal" value="{{algConfig.pd.pdYellowSensitivity.[0]}}">
										</li></td>
										<td><li>
											<input data-role="none"  type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pd-pdYellowSensitivity-1" class="mult_input2 char-normal" value="{{algConfig.pd.pdYellowSensitivity.[1]}}">
										</li></td>
										<td><li>
											<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pd-pdYellowSensitivity-2" class="mult_input3 char-normal" value="{{algConfig.pd.pdYellowSensitivity.[2]}}">
										</li></td>
									</table>
								</div>
								<div data-role="none" class="input-text-box">
									<label data-role="none" data-desc="pdGreenSensitivity">pdGreenSensitivity</label>
									<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
										<td><li>
											<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pd-pdGreenSensitivity-0" class="mult_input1 char-normal" value="{{algConfig.pd.pdGreenSensitivity.[0]}}">
										</li></td>
										<td><li>
											<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pd-pdGreenSensitivity-1" class="mult_input2 char-normal" value="{{algConfig.pd.pdGreenSensitivity.[1]}}">
										</li></td>
										<td><li>
											<input data-role="none" type="text" oninput="if(value>1)value=1;if(value.length>6)value=value.slice(0,6);if(value<0)value=0" id="algConfig-pd-pdGreenSensitivity-2" class="mult_input3 char-normal" value="{{algConfig.pd.pdGreenSensitivity.[2]}}">
										</li></td>
									</table>
								</div>
							</div>
							<div class="custom-select-box">
								<label class="single_option_text" for="algConfig-pd-detectPart" data-desc="pdDetectPart">GUI绘制模式</label>
								<div><select class="custom-select" id="algConfig-pd-detectPart" data-role="none" value="{{algConfig.pd.detectPart}}">
									<option value="0" {{#equal algConfig.pd.detectPart 0}}selected="selected"{{/equal}}>{{getKeyLang "all"}}</option>
									<option value="1" {{#equal algConfig.pd.detectPart 1}}selected="selected"{{/equal}}>{{getKeyLang "bottom"}}</option>
								</select></div>
							</div>

						</div>
					</div>
				</div>
				<div id="networkConfig" class="tab-configs">

					<div class="configmenu">
						<h1><p>{{getKeyLang "ethernet"}}</p></h1>
						<div class="config_checkbox">
							<input data-role="none" class="checkBtn custom" type="checkbox" id="networkConfig-ethernet-enableDHCP" {{#if networkConfig.ethernet.enableDHCP}}checked="checked"{{/if}}>
							<label data-role="none" for="networkConfig-ethernet-enableDHCP"></label>
							<p data-desc="dhcp">DHCP</p>
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" for="networkConfig-ethernet-dhcpTimeout" data-desc="dhcp-timeout">DHCP Timeout</label>
							<input data-role="none" type="number" id="networkConfig-ethernet-dhcpTimeout" value="{{networkConfig.ethernet.dhcpTimeout}}">
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" id="tab-ipaddr" for="networkConfig-ethernet-ipAddress" data-desc="ipaddr">IP Address</label>
							<input data-role="none" type="text" id="networkConfig-ethernet-ipAddress" class="empty-limit ipaddr-limit char-normal" value="{{networkConfig.ethernet.ipAddress}}"{{#if networkConfig.ethernet.enableDHCP}}readonly="true" style="color:#aaa;" {{/if}}>
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" id="tab-subnet-mask" for="networkConfig-ethernet-subnetMask" data-desc="subnet-mask">Subnet Mask</label>
							<input data-role="none" type="text" id="networkConfig-ethernet-subnetMask" class="empty-limit subnetmask-limit char-normal" value="{{networkConfig.ethernet.subnetMask}}"{{#if networkConfig.ethernet.enableDHCP}}readonly="true" style="color:#aaa;" {{/if}}>
						</div>
						<div data-role="none" class="input-text-box">
							<label data-role="none" id="tab-gateway" for="networkConfig-ethernet-gateway" data-desc="gateway">Gateways</label>
							<input data-role="none" type="text" id="networkConfig-ethernet-gateway" class="empty-limit gateway-limit char-normal" value="{{networkConfig.ethernet.gateway}}"{{#if networkConfig.ethernet.enableDHCP}}readonly="true" style="color:#aaa;" {{/if}}>
						</div>

					</div>
				</div>
				<div style="height: 200px;"></div>
			</script>
		</div>
		<script src="./js/handlebars.js"></script>
		<script src="./js/webapp-common.js"></script>
		<script src="./js/webapp-model.js"></script>
		<script src="./js/webapp-view.js"></script>
		<script src="./js/webapp-ctrller.js"></script>
		<script type="text/javascript">
			$(document).ready(function () {
				var customer;
				var itemCustomer = window.location.host + "-customer";
				window.localStorage &&
					(customer = window.localStorage.getItem(itemCustomer));
				console.log(customer);
				if (customer == "200032") {
					if (
						navigator.userAgent.match(
							/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
						)
					) {
						$("<link>")
							.attr({
								rel: "stylesheet",
								type: "text/css",
								href: "./css/luis-mobilecommon.css",
							})
							.appendTo("head");
					} else {
						$("<link>")
							.attr({
								rel: "stylesheet",
								type: "text/css",
								href: "./css/luis-common.css",
							})
							.appendTo("head");
					}
				} else {
					if (
						navigator.userAgent.match(
							/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
						)
					) {
						$("<link>")
							.attr({
								rel: "stylesheet",
								type: "text/css",
								href: "./css/mobilecommon.css",
							})
							.appendTo("head");
					} else {
						$("<link>")
							.attr({
								rel: "stylesheet",
								type: "text/css",
								href: "./css/common.css",
							})
							.appendTo("head");
					}
				}
				switchCssStyle(customer);

				var model = new WebappModel();//初始化模型
				var view = new WebappView(model, "config");//初始化视图
				var controller = new WebappController(model, view);//初始化控制器
				controller.refreshConfigs();//刷新配置
				let ch = localStorage.getItem("ch");//获取通道
				ch = window.ch = ch ? ch : "ch0";//设置通道

				const changeChSelect = document.getElementById("change_ch_select");//获取通道选择器
				changeChSelect.value = ch;//设置通道选择器值
				changeChSelect.addEventListener("change", function () {
					window.ch = this.value;//设置通道
					localStorage.setItem("ch", this.value);//设置本地存储通道
					view.refreshConfigsEvent.notify();//刷新配置
					// 在通道切换后应用权限控制
					if (typeof clearAllRestrictedStates === 'function' && typeof hideRestrictedElements === 'function') {
						setTimeout(function() {
							clearAllRestrictedStates();
							setTimeout(hideRestrictedElements, 100);
						}, 500);
					}
				});
			});
		</script>
	</body>
</html>
