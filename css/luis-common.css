@import"url.css";

body.dynamic_theme{
    background:var(--theme_background_color);
}

.ui-page.ui-page-theme-a.ui-page-active,#page-system,#page-query,#page-home{
    background: #1f2a44!important;
}

div#main {
    background: #1f2a44;
}
/*标题栏属性*/
div.title{
    width: 100%;
    height:52px;
    background:#1f2a44;
    vertical-align:top;
    position: fixed;
    top:0;
    left:0;
    z-index:1500;
}

.ui-footer.ui-bar-inherit.ui-footer-fixed.slideup{
    background-color: rgb(31, 42, 68);
}

#btn-submit,#btn-cancel,#btn_calibrationboard_submit,#btn_calibration_start_confirm,#btn_calibration_start_cancel{
    background-color: rgb(31, 42, 68);
    color: #fff;
    text-shadow: rgb(17, 17, 17) 0px 1px 0px;
    border-color:#fff;
}

#btn-submit:hover,#btn-cancel:hover,#btn_calibrationboard_submit:hover,#btn_calibration_start_confirm:hover,#btn_calibration_start_cancel:hover{
    color: #ff7500;
    border-color:#ff7500;
}

/*用来空占位置的*/
div.title_hide{
    width: 100%;
    height:52px;
    vertical-align:top;
    visibility:hidden;
}

div.title p{
    height:100%;
    width:100%;
    position:absolute;
    left:0px; top:-18px;
    font-size:18px;
    line-height:52px;
    text-align:center;
    font-weight:var(--theme_title_font_weight);
    text-transform:var(--theme_title_text_transform);
    color:#fff;
}

/*返回按钮属性，在左边*/
a.title_back{
    z-index:1501;
    position:absolute;
    left:0px;
    top:0px;
    height:52px;
    width:52px;
    background: transparent url("./images/<EMAIL>") no-repeat;
    display: inline-block;
    background-size: cover;
    outline:none;
    blr:expression(this.onFocus=this.blur());
    cursor: pointer;
}

/*刷新按钮属性，在右边*/
a.title_refresh{
    z-index:1501;
    position:absolute;
    right:5px;
    top:0px;
    height:52px;
    width:52px;
    background: transparent url("./images/<EMAIL>") no-repeat;
    display: inline-block;
    background-size: cover;
    outline:none;
    blr:expression(this.onFocus=this.blur());
    cursor: pointer;
}

.title_right{
    z-index:1501;
    position:absolute;
    right:10px;
}

/*登陆界面框*/
#login-form {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    -webkit-transform: translate(-50%,-50%);
    -moz-transform: translate(-50%,-50%);
    min-width: 400px;
    max-width: 600px;
    padding: 40px;
    border-width: 1px;
    border-color: #343434;
    border-radius: 10px;
}

input#username,input#password{
    float: left;
    width: 70%;
    border-width: 1px;
    border-style: solid;
    text-align: left;
    margin: 0;
    min-height: 1.6em;
    border: 0;
    border-color:#1d1d1d;
    background: transparent none;
    -webkit-appearance: none;
    -webkit-border-radius: inherit;
    border-radius: 5px;
	border-width: 1px;
    border-style: solid;
}

.login_form h2{
    padding-bottom: 20px;
    font-size: 35px;
}

.btn-calibration{
    /*background-image:var(--home_calibration);*/
      background-image: url("./images/calibration.png");
}
.btn-register{
      /*background-image:var(--home_face);*/
      background-image: url("./images/register.png");
}
.btn-imageparam{
      /*background-image:var(--home_imageparam);*/
      background-image: url("./images/imageparam.png");
}
.btn-fullscreen{
      /*background-image:var(--home_fullscreen);*/
      background-image: url("./images/fullscreen.png");
}

#calibration-info{
    background-color: #1f2a44;
}
.btn-calibration, .btn-register, .btn-imageparam, .btn-fullscreen{
	width: 44px !important;
	height: 44px !important;
	background-size:contain;
	transform: translate(-50%,0%);
	-webkit-transform: translate(-50%,0%);
	-moz-transform: translate(-50%,0%);
	display: inline-block;
	cursor:pointer;
	z-index: 1000;
}

/*菜单栏样式*/
.menu{
	table-layout:fixed;
	width:100%;
    background:#1f2a44;
    border-color: #fff;
}

.menu td li {
    list-style:none; /* 将默认的列表符号去掉 */
    padding:0; /* 将默认的内边距去掉 */
    margin:0; /* 将默认的外边距去掉 */
    width:100%; /* 设置宽度 */
    text-align:center; /* 居中对齐文字 */
    background:#1f2a44;
}
.menu td li a {
	position: relative;
    display:inline-block; /* 将链接设为块级元素 */
    width:100%;
    height:40px;
    line-height:40px; /* 设置行高，将行高和高度设置同一个值，可以让单行文本垂直居中 */
    text-align:center; /* 居中对齐文字 */
    text-decoration:none; /* 去掉下划线 */
    text-shadow:none;
    cursor:pointer;
    white-space: nowrap;/*规定段落中的文本不进行换行*/
 	overflow: hidden;/*将多出来的字体隐藏；  */
 	text-overflow: ellipsis;
   	color:#fff!important; /*这个会因为jquery mobile而不生效,所以加个important*/
}

.menu td li a:hover {
    color:#ff7500!important;
}

/*menu选中的样式，搞这么长是因为加了!important来抵消jquery mobile的影响，这里用具体的标签路径可以提高覆盖的优先级*/
.menu td li a.menu_checked {
	color:#ff7500!important;
}

.menu td li a.menu_checked:after {
    content: '';
	width:50%;
	left:25%;
	border-bottom: 5px solid #ff7500;
	position: absolute;
	bottom: 2px;
}


.menu_a td li a {
	position: relative;
    display:inline-block; /* 将链接设为块级元素 */
    width:100%;
    height:40px;
    line-height:40px; /* 设置行高，将行高和高度设置同一个值，可以让单行文本垂直居中 */
    text-align:center; /* 居中对齐文字 */
    text-decoration:none; /* 去掉下划线 */
    text-shadow:none;
    cursor:pointer;
   	color:#fff!important; /*这个会因为jquery mobile而不生效,所以加个important*/
   	background:#1f2a44;
   	border:solid 0.1em #fff;
}

.menu_a td:first-child li a{
    border-radius: 6px 0px 0 6px;    
}

.menu_a td:last-child li a{
    border-radius: 0 6px 6px 0;    
}

.menu_a td li a:hover {
    color:#ff7500!important;
}

.menu_a td li a.menu_checked {
	color:#fff!important;
	background:#1f2a44;
}

.menu_a td li a.menu_checked:after {
    content: '';
}


a.my_cancel{
    position:absolute;
    right:0px;
    height:66px;
    width:66px;
    background: transparent var(--my_cancel) no-repeat;
    display: inline-block;
    background-size: cover;
}

a.my_reset{
    position:absolute;
    right:0px;
    height:75px;
    width:75px;
    background: transparent var(--my_reset) no-repeat;
    display: inline-block;
    background-size: cover;
}

div.logo{
    display:var(--logo_display);
    text-align:center;
}

input.normal_input {
	border: solid transparent;
	border-width: 1px;
	border-color: #1d1d1d;
}

lan.checkbox_text{
    font-weight:var(--theme_icon_text_font_weight);
    text-transform:var(--theme_option_text_text_transform);
    color:var(--theme_icon_text_color);
}
div.div_warn{
    position:absolute;
    top:1px;
    left:10px;
    font-size:20px;
    color:#ff0000;
}

/*单行控件容器的属性*/
div.single_option{
    position:relative;
    width: 100%;
    height:25px;
    margin-top: 0.5em;
}

div.single_option p{
    position:absolute;
    left:0px;
    top:-15px;
    font-size:18px;
    height:25px;
    line-height:25px;
}

div.single_option div{
    position:absolute;
    right:10px;
    top:0px;
}

.single_option_text{
    font-weight:var(--theme_icon_text_font_weight);
    text-transform:var(--theme_option_text_text_transform);
    /*color:var(--theme_icon_text_color);*/
}

/*信息窗口样式*/
div.infomenu{
	margin-top: 0.5em;
	width:100%;
}

div.infomenu div{
    position:relative;
    background:#1f2a44;
    width:100%;
    height:40px;    
    border-bottom: 1px solid var(--theme_disenable_text_color);
}

div.infomenu div p{
	position:absolute; 
    left:10px;
    color:var(--theme_text_color);
    font-size:15px;
    height:20px;
    line-height:15px;
    text-decoration:none;
    font-weight:bold;
    text-shadow:none;
}

div.infomenu div p1,div.infomenu div p2{
    position:absolute; 
    top:10px;
    font-size:18px;
    height:20px;
    line-height:20px;
    text-decoration:none;
    font-weight:var(--theme_item_font_weight);
    text-transform:var(--theme_item_text_transform);
    text-shadow:none;
}

div.infomenu div p1{left:10px;color:var(--theme_text_color);}
div.infomenu div p2{right:10px;color:var(--theme_disenable_text_color);}


/*竖的*/
div.infomenu table{
	margin-left:10px;
    background:var(--theme_background_color);
    width:100%;  
    border-bottom: 1px solid var(--theme_disenable_text_color);
}


div.infomenu table tr{
	color:var(--theme_text_color);
    font-size:18px;
    height:20px;
    line-height:20px;
    text-decoration:none;
    font-weight:var(--theme_item_font_weight);
    text-transform:var(--theme_item_text_transform);
    text-shadow:none;
}

div.infomenu table tr:first-child{
    font-weight:bold;
}

div.log_search{
	position:relative;
}

div.log_search i{
	background: var(--my_search) no-repeat!important;
	width: 25px;
   	height: 25px;
    position: absolute;
    top: 7px;
    left: 5px;
}

div.log_search input{
	padding-left: 30px;
	height: 25px;
	color:var(--theme_btn_text_color)!important;
	background-color:var(--theme_btn_background_color)!important;
	border-color:var(--theme_btn_border_color);
}

ol.log li{
	color:var(--theme_text_color)!important;
}


/*设置窗口样式*/
div.configmenu{
	margin-top: 0.5em;
	width:100%;
	background:#1f2a44;
}

div.configmenu h1{
    position:relative;
    width:100%;
    height:40px;    
    border-bottom: 1px solid #fff;
}

div.configmenu h1   p{
	position:absolute; 
    left:0px;
    color:#fff;
    font-size:15px;
    height:20px;
    line-height:15px;
    text-decoration:none;
    font-weight:bold;
    text-shadow:none;
}

div.configmenu div.item{
    position:relative;
    margin-left:10px;
    width:100%;
    border-bottom: 1px solid #fff;
}

div.configmenu div.item h1{
    position:relative;
    width:100%;
    height:30px;
    border-bottom:none;
}

div.configmenu div.item h1    p{
	position:absolute; 
    left:0px;
    color:#fff;
    font-size:15px;
    height:20px;
    line-height:5px;
    text-decoration:none;
    font-weight:bold;
    text-shadow:none;
}

div.configmenu .normal_option_text_{
    font-weight:var(--theme_icon_text_font_weight);
    text-transform:var(--theme_option_text_text_transform);
    color:#fff;
}


div.config_checkbox{
	position:relative;
    width:100%;
    height:40px;   
    margin-top:15px;
}

div.config_checkbox label{
	position:absolute;
    left:0px;
    top:0px;
}

div.config_checkbox p{
	position:absolute;
	left:32px;
    color:#fff;
    font-size:18px;
    height:20px;
    /*line-height:3px;*/
    text-shadow:none;
    margin-top: 0px;
}

div.config_checkbox2{
	position:relative;
    width:100%;
    height:40px;   
    margin-top:15px;
    right:50%;
}

div.config_checkbox2 label{
	position:absolute;
    right:0px;
    top:0px;
}

div.config_checkbox2 p{
	position:absolute;
	right:32px;
    color:var(--theme_text_color);
    font-size:18px;
    height:20px;
    /*line-height:3px;*/
    text-shadow:none;
    margin-top: 0px;
}

div.input-text-box{
	position:relative;
	width:100%;
	height:40px;	
    margin-top: 10px;
    margin-bottom: 10px;
}

div.input-text-box label{
	position: absolute;
	top:4px;
	left:0px;
	font-size:16px;
	/*color: var(--theme_text_color);*/
	text-shadow:0 1px 0 #111;;
}

div.input-text-box input{
	position: absolute;
	top:4px;
	right:50%;
	width:380px;
	font-size:18px;
	color: #fff;
	border: 0.1em solid #fff;
	text-shadow:none;
    text-align: right;
    background-color: #1f2a44;
}
div.input-text-box .normal_input{
	position: absolute;
	top:4px;
	right: 10px;;
	width:130px;
	font-size:18px;
	color: #fff;
	border: 0.1em solid #fff;
	text-shadow:none;
	text-align: right;
}

div.input-text-box .mult_input1{
	position: absolute;
	top:2px;
	right:64%;
	width:5%;
	font-size:18px;
	color: var(--theme_disenable_text_color);
	border: 0.1em solid #fff;
	text-shadow:none;
	text-align: right;
}
div.input-text-box .mult_input2{
	position: absolute;
	top:2px;
	right:57%;
	width:5%;
	font-size:18px;
	color: var(--theme_disenable_text_color);
	border: 0.1em solid #fff;
	text-shadow:none;
	text-align: right;
}
div.input-text-box .mult_input3{
	position: absolute;
	top:2px;
	right:50%;
	width:5%;
	font-size:18px;
	color: var(--theme_disenable_text_color);
	border: 0.1em solid #fff;
	text-shadow:none;
	text-align: right;
}
div.input-switch-box{
	position:relative;
	width:100%;
    height:40px;	
    margin-top: 15px;
}

div.input-switch-box p{
	position: absolute;
	left:0px;
    color:#fff;
    font-size:18px;
    height:20px;
    line-height:3px;
    text-shadow:none;
}

div.input-switch-box label{
	position: absolute;
	top:5px;
    right:50%;
}

/******************************************************************************************/
div.username{
    position:relative;
    background: var(--theme_item_background_color);
    width:100%;
    height:50px;
    border-radius: 6px;    
    margin-top: 0.5em;
}
a.username{
        position:absolute; 
        left:50px;
        top:15px;
        color: var(--theme_item_text_color);
        font-weight:var(--theme_item_font_weight);
        text-transform:var(--theme_item_text_transform);
        font-size:15px;
        height:20px;
        line-height:20px;
        text-decoration:none;
}
a.username_user_img{
	position:absolute;
	left:5px;
	top:5px;
	height:40px;
	width:40px;
	display: inline-block;
	border-radius: 20px; 
}

a.username_edit_img{
    position:absolute;
    right:25px;
    top:15px;
    height:15px;
    width:15px;
    background: transparent var(--edit) no-repeat;
    display: inline-block;
    background-size: cover;
    cursor: pointer;
}

a.username_delete_img{
    position:absolute;
    right:5px;
    top:15px;
    height:15px;
    width:15px;
    background: transparent var(--delete) no-repeat;
    display: inline-block;
    background-size: cover;
    cursor: pointer;
}

label.username{
        position:absolute; 
        right:10px;
        top:12px;
}

.userlist{
    position:absolute;
    left:0px;
    top:0px;
    color:var(--theme_color);
    font-weight:800;
    font-size: 20px;
}

a.filelist_item_img{
    position:absolute;
    left:10px;
    top:0px;
    height:15px;
    width:15px;
    background: transparent var(--my_img) no-repeat;
    display: inline-block;
    background-size: cover;
    cursor: pointer;
}


div.filelist_img_out{
    position:relative;
}

div.filelist_item{
    position:relative;
    background: var(--theme_item_background_color);
    width:100%;
    height:50px;
    border-radius: 6px;    
    margin-top: 0.5em;
    cursor: pointer;
}

a.filelist_item_out{
    position:absolute;
    left:0px;
    top:0px;
    width:100%;
    height:100%;
}

a.filelist_item_left{
    position:absolute; 
    left:10px;
    top:15px;
    color:var(--theme_item_text_color);
    font-weight:var(--theme_item_font_weight);
    text-transform:var(--theme_item_text_transform);
    font-size:18px;
    height:20px;
    line-height:20px;
    text-decoration:none;
}
a.filelist_item_right{
    position:absolute; 
    right:10px;
    top:15px;
    color: var(--theme_item_text_color);
    font-weight:var(--theme_item_font_weight);
    text-transform:var(--theme_item_text_transform);
    font-size:18px;
    height:20px;
    line-height:20px;
    text-decoration:none;
}

p.filelist_item_left{
    position:absolute; 
    left:10px;
    top:3px;
    color:var(--theme_item_text_color);
    font-weight:var(--theme_item_font_weight);
    text-transform:var(--theme_item_text_transform);
    font-size:18px;
    height:20px;
    line-height:20px;
    text-decoration:none;
}
p.filelist_item_right{
    position:absolute; 
    right:10px;
    top:3px;
    color: var(--theme_item_text_color);
    font-weight:var(--theme_item_font_weight);
    text-transform:var(--theme_item_text_transform);
    font-size:18px;
    height:20px;
    line-height:20px;
    text-decoration:none;
}

div.changelistBtn{
    background: #dff7fb;
    position:relative;
    width:100%;
    height:50px;   
    margin-top: 0.5em;
}
 
a.changelistBtn_l,a.changelistBtn_r,a.changelistBtn_l_checked,a.changelistBtn_r_checked {
    position:absolute;
    height:100%;
    width:50%;
    top:0px;
    font-size:15px;
    line-height:50px;
    text-align: center;		
}

a.changelistBtn_l,a.changelistBtn_l_checked{
    left:0px;
    border-radius: 5px 0px 0px 5px;	
}

a.changelistBtn_r,a.changelistBtn_r_checked{
    left:50%;
    border-radius: 0px 5px 5px 0px;
}

a.changelistBtn_l,a.changelistBtn_r{
    background:#eaeaea;
    color:#9b9f9f;
}

a.changelistBtn_l_checked,a.changelistBtn_r_checked{
    background: var(--theme_color);
    color: #fff; 
}

input[type="radio"]{
    display: none;
	-webkit-appearance: none;/*去除系统默认appearance的样式,常用于IOS下移除原生样式*/
	margin: 0;
}

input[type="checkbox"] {
    display: none;
    -webkit-appearance: none;/*去除系统默认appearance的样式,常用于IOS下移除原生样式*/
    margin: 0;
}

.switchBtn+label {
    width: 50px;
    height: 20px;
    display: inline-block;
    background: transparent url("./images/<EMAIL>") no-repeat;
    background-size: contain;
    cursor: pointer;
} 
.switchBtn:checked+label {
    background: transparent url("./images/<EMAIL>") no-repeat;
    background-size: contain;
}

.checkBtn+label {
    width: 25px;
    height: 25px;
    display: inline-block;
    background: transparent url("./images/<EMAIL>") no-repeat;
    background-size: contain;
    cursor: pointer;
    border-color: #fff;
    border-radius: 4px;
} 
.checkBtn:checked+label {
    background: transparent url("./images/<EMAIL>") no-repeat;
    background-size: contain;
}

/*通用按钮*/
.btn{
    font-size: 20px;
    font-weight:800;
    text-align:center;
    padding: 0.65em 1em;
    box-shadow: inset 0 -4px 0 0 rgba(0,0,0,0.2);
    border-radius: 4px;
    cursor: pointer;
    border:solid 0.1em; 
    background-color: #1f2a44;
    border-color: #ff7500;
    color: #ff7500!important;
    text-shadow: 0 1px 0 #111;
}
a.popupbtn{
    background-color: var(--theme_btn_background_color);
	padding: 5px 15px;
	color: white;
	padding: 6px 0px;
    font-size: 14px;
	font-weight: bold;
    text-align: center;
	cursor: pointer;
	border-radius: 2px;
	position: relative;
	overflow: hidden;
	display: block;
	margin: 10px auto 20px auto;
}

a.popupbtn:hover{
    color: #ff7500;
    border-color:#ff7500;
}

.cancelbtn{
    background:var(--theme_cancelbtn_background_color);
    color: var(--theme_cancelbtn_text_color);
    font-size: 20px;
    font-weight:800;
    text-align:center;
    padding: 0.65em 1em;
    border-radius: 4px;
    box-shadow: inset 0 -4px 0 0 rgba(0,0,0,0.2);
    cursor: pointer;
    border:solid 0.1em;
	border-color:var(--theme_cancelbtn_border_color);
}
.btn:hover,.cancelbtn:hover{
    box-shadow: inset 0 -4px 0 0 rgba(0,0,0,0.6), 0 0 8px 0 rgba(0,0,0,0.5);
}
.btn:active,.cancelbtn:active{
    position: relative;
    top: 4px;
    box-shadow: inset 0 3px 5px 0 rgba(0,0,0,0.2);
}
div.uploadBtn
{
    width: 100%;
    cursor: pointer;
    border-color: #ff7500!important;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}

div.uploadBtn  div.drag {
    padding: 60px 0px 60px 0px;
    border: 2px dotted var(--theme_item_background_color);
    color: var(--theme_item_text_color);
    font-size: x-large;
    text-align: center;
}
div.uploadBtn div.or {
    padding: 10px;
}
.input-label{
	padding: 5px 15px;
	color: white;
	padding: 6px 0px;
    font-size: 14px;
	font-weight: bold;
    text-align: center;
	cursor: pointer;
	border-radius: 2px;
	position: relative;
	overflow: hidden;
	display: block;
    margin: 10px auto 20px auto;
    background-color: rgb(31, 42, 68);
    color: rgb(255, 117, 0);
    text-shadow: rgb(17, 17, 17) 0px 1px 0px;
    border-color: rgb(255, 117, 0);
}

.input-label span{
	cursor: pointer;
}


.input-label input{
	position: absolute;
	top: 0;
	right: 0;
	margin: 0;
	border: solid transparent;
	border-width: 0 0 100px 200px;
	opacity: .0;
	filter: alpha(opacity= 0);
	-o-transform: translate(250px,-50px) scale(1);
	-moz-transform: translate(-300px,0) scale(4);
	direction: ltr;
	cursor: pointer;
}

.uploadBtn label:hover {
	background-color:var(--theme_color);
}

input[type="range"] {
    -webkit-appearance: none; /*去除默认样式*/
   /* border-radius: 10px; /*将轨道设为圆角的*/
    /*margin-top: 12px;*/
    /*background-color: #ebeff4;*/
    background: -webkit-linear-gradient(#ff7500, #ff7500) no-repeat #ececec; background-size: 15% 100%;
       /*border-radius: 15px;*/
       width:45% !important;
       -webkit-appearance: none;
       height:4px;
      padding: 0;
      border: none;
    /*box-shadow: 0 1px 1px #def3f8, inset 0 .125em .125em #0d1112; /*轨道内置阴影效果*/

}

input[type=range]:focus{/*去除焦点虚线框*/
    border:0;
    outline:0;
}

input[type=range]::-webkit-slider-thumb{
    -webkit-appearance: none;
    height: 15px;
    width: 15px;
    margin-top: -2px; /*使滑块超出轨道部分的偏移量相等*/
    background: #ff7500;
    border-radius: 50%; /*外观设置为圆形*/
    border: solid 0.2em #fff; /*设置边框*/
    /*box-shadow: 0 .125em .125em #3b4547; /*添加底部阴影*/
}

input[type=range]::-moz-focus-outer{/*兼容firefox的去除焦点虚线框*/
    border: 0;
}

input[type=range]::-moz-range-thumb{/*兼容firefox的改样式*/
    -webkit-appearance: none;
    height: 15px;
    width: 15px;
    margin-top: -2px; /*使滑块超出轨道部分的偏移量相等*/
    background: #fff; 
    border-radius: 50%; /*外观设置为圆形*/
    border: solid 0.2em #fff; /*设置边框*/
    /*box-shadow: 0 .125em .125em #3b4547; /*添加底部阴影*/
}

div.rangeinput{
	position:relative;
	width:100%;
	height:60px;
	margin-top: 0.5em;
}

p.rangeinput_title{
	position:absolute; 
	left:0px;
	color:#fff;
	font-size:18px;
	top: -10px;
	text-decoration:none;
	text-shadow:none;
}

input.rangeinput_input{
	position:absolute; 
	left:0px;
	top: 35px;
    margin-top: 10px;
    margin-bottom: 10px;
}

p.rangeinput_value{
	position:absolute;
	color: #fff;
	font-size:18px; 
	right:50%;
	top: 7px;
	text-decoration:none;
	text-shadow:none;
}

div.custom-select-box{
	position:relative;
	width:100%;
	height:40px;	
    margin-top: 10px;
}

div.custom-select-box label{
	position: absolute;
	top:10px;
	left:0px;
	font-size:18px;
	color: #fff;
	text-shadow:none;
}

div.custom-select-box div{
	position: absolute;
	right:50%;
}

div.custom-select-box span{
	width:200px;
    color: #fff;
    background: #1f2a44;
    border: 0.1em solid #fff;
    text-shadow:none;
    min-height: 33.6px;
}

div.custom-select-box span:after{
    border-bottom: 1px solid #fff;
    border-right: 1px solid #fff;
}

.custom-select-wrapper {
    position: relative;
    display: inline-block;
    user-select: none;
}

/*select控件属性*/
.custom-select-wrapper select {
    display: none;
}
.custom-select {
    position: relative;
    display: inline-block;
}

.custom-select-trigger {
    position: relative;
    display: block;
    padding: 0 30px 0 10px;
    width:80px;
    font-size:18px;
    font-weight: 400;
    color: #fff;
    line-height: 30px;
    background: #1f2a44;
    border-radius: 4px;
    border: 0.1em solid #fff;
    cursor: pointer;
}
/*后面那个V图形*/
.custom-select-trigger:after {
    position: absolute;
    display: block;
    content: '';
    width: 10px;
    height: 10px;
    top: 50%;
    right: 10px;
    margin-top: -3px;
    border-bottom: 1px solid #111;
    border-right: 1px solid #111;
    transform: rotate(45deg) translateY(-50%);
    transition: all 0.4s ease-in-out;
    transform-origin: 50% 0;
}
.custom-select.opened .custom-select-trigger:after {
    margin-top: 3px;
    transform: rotate(-135deg) translateY(-50%);
}

.custom-options {
    position: absolute;
    display: block;
    top: 100%;
    left: 0;
    right: 0;
    min-width: 100%;
    max-height: 400px;
    overflow-y: auto;
    overflow-x: hidden;
    margin: 15px 0;
    border: 1px solid #1f2a44;
    border-radius: 4px;
    box-sizing: border-box;
    background: #1f2a44;
    transition: all 0.4s ease-in-out;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transform: translateY(-15px);
    z-index:500;
}
.custom-select.opened .custom-options {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
    transform: translateY(0);
}
.custom-options:before {
    position: absolute;
    display: block;
    content: '';
    bottom: 100%;
    right: 25px;
    width: 7px;
    height: 7px;
    margin-bottom: -4px;
    border-top: 1px solid #1f2a44;
    border-left: 1px solid #1f2a44;
    background: #1f2a44;
    transform: rotate(45deg);
    transition: all 0.4s ease-in-out;
}
.option-hover:before {
    background: #f9f9f9;
}
.custom-option {
    position: relative;
    display: block;
    padding: 0 22px;
    border-bottom: 1px solid #1f2a44;
    font-size:18px;
    font-weight: 600;
    color:#fff;
    line-height: 30px;
    cursor: pointer;
    transition: all 0.4s ease-in-out;
}
.custom-option:first-of-type {
    border-radius: 4px 4px 0 0;
}
.custom-option:last-of-type {
    border-bottom: 0;
    border-radius: 0 0 4px 4px;
}
.custom-option:hover,
.custom-option.selection {
    background: #f9f9f9;
}

/*显示文本继承不能选中的属性*/
div.title p,.single_option_text,.btn{
	-webkit-touch-callout: none; /* iOS Safari */
	-webkit-user-select: none; /* Chrome/Safari/Opera */
	-khtml-user-select: none; /* Konqueror */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* Internet Explorer/Edge */
	user-select: none; /* Non-prefixed version, currently
	not supported by any browser */
}

#tab-dnsServer{
    position:absolute; 
    left:0px;
    color:#fff;
    font-size:15px;
    height:20px;
    line-height:15px;
    text-decoration:none;
    font-weight:bold;
    text-shadow:none;
}

#popup-show-info{
    background-color: #1f2a44;
    color: #fff;
}

.ui-header{
    background-color: #1f2a44 !important;
    color: #fff !important;
    border-color: #fff !important;
}

.ui-header h1{
    margin-left: 0 !important;
    margin-right: 0 !important;
}
.ui-content a {
    color: #fff !important;
    background: #1f2a44 !important;
}

.ui-content a:hover{
    color: #ff7500 !important;
    border-color:#ff7500 !important;
}

#popup-form-input3{
    background-color: #1f2a44;
    color: #fff;
    border-color: #fff;
}

div.multi_selectbox{
	position:relative;
	width:100%;
	height:30px;
	margin-top: 0.5em;
}
.multi_selectbox_text{
    font-weight:var(--theme_icon_text_font_weight);
    text-transform:var(--theme_option_text_text_transform);
    color:var(--theme_text_color);
}
p.multi_selectbox_title{
	position:absolute; 
	left:0px;
	color: var(--theme_text_color);
	font-size:18px;
	top: -10px;
	text-decoration:none;
	text-shadow:none;
}

table.multi_selectbox_table{
    position:relative;
    float:right;
    margin-right:50%;
}

.checkBtnRed+label {
    width: 25px;
    height: 25px;
    display: inline-block;
    background: transparent var(--checkbox_off) no-repeat;
    background-size: contain;
    background-color: red;
    cursor: pointer;
}
.checkBtnRed:checked+label {
    background: transparent var(--checkbox_on) no-repeat;
    background-size: contain;
    background-color: red;
}

.checkBtnYellow+label {
    width: 25px;
    height: 25px;
    display: inline-block;
    background: transparent var(--checkbox_off) no-repeat;
    background-size: contain;
    background-color: yellow;
    cursor: pointer;
}
.checkBtnYellow:checked+label {
    background: transparent var(--checkbox_on) no-repeat;
    background-size: contain;
    background-color: yellow;
}

.checkBtnGreen+label {
    width: 25px;
    height: 25px;
    display: inline-block;
    background: transparent var(--checkbox_off) no-repeat;
    background-size: contain;
    background-color: green;
    cursor: pointer;
}
.checkBtnGreen:checked+label {
    background: transparent var(--checkbox_on) no-repeat;
    background-size: contain;
    background-color: green;
}

