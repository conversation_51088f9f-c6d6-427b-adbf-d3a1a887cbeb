body{
    --home_calibration:url(./images/calibration.png);
    --home_face:url(./images/register.png);
    --home_imageparam:url(./images/imageparam.png);
    --home_fullscreen:url(./images/fullscreen.png);
    --home_settings:url("./images/<EMAIL>");
    --home_alarm:url("./images/<EMAIL>");
	
    --svcheckbox_off:url("./images/svcheckboxOff.png");
    --svcheckbox_on:url("./images/svcheckboxOn.png");
    --svcheckbox_red:url("./images/svcheckboxRed.png");
    --svcheckbox_green:url("./images/svcheckboxGreen.png");
    --svcheckbox_yellow:url("./images/svcheckboxYellow.png");
    --pw_visible_off:url("./images/<EMAIL>");
    --pw_visible_on:url("./images/<EMAIL>");

    --sk_switch_off:url("./images/<EMAIL>");
    --sk_switch_on:url("./images/<EMAIL>");
    --axion_switch_on:url("./images/<EMAIL>");
    --T2S_switch_on:url("./images/<EMAIL>");
    --WE_switch_on:url("./images/<EMAIL>");
    --VUE_switch_on:url("./images/<EMAIL>");
    --proxicam_switch_on:url("./images/<EMAIL>");
    --AME_switch_on:url("./images/<EMAIL>");
    --switch_off:var(--sk_switch_off);
    --switch_on:var(--sk_switch_on);
    
    --radio_off:url("./images/<EMAIL>");
    --radio_on:url("./images/<EMAIL>");
    
    --sk_checkbox_off:url("./images/<EMAIL>");
    --sk_checkbox_on:url("./images/<EMAIL>");
    --axion_checkbox_off:url("./images/<EMAIL>");
    --axion_checkbox_on:url("./images/<EMAIL>");
    --T2S_checkbox_off:url("./images/<EMAIL>");
    --T2S_checkbox_on:url("./images/<EMAIL>");
    --WE_checkbox_off:url("./images/<EMAIL>");
    --WE_checkbox_on:url("./images/<EMAIL>");
    
    --VUE_checkbox_off:url("./images/<EMAIL>");
    --VUE_checkbox_on:url("./images/<EMAIL>");
    --proxicam_checkbox_off:url("./images/<EMAIL>");
    --proxicam_checkbox_on:url("./images/<EMAIL>");
    --AME_checkbox_off:url("./images/<EMAIL>");
    --AME_checkbox_on:url("./images/<EMAIL>");

    --checkbox_off:var(--sk_checkbox_off);
    --checkbox_on:var(--sk_checkbox_on);
    

    --back_bt:url("./images/<EMAIL>");
    --my_cancel:url("./images/my_cancel.png");
    --my_reset:url("./images/my_reset.png");
    --my_search:url("./images/search.png");
	
	--edit:url("./images/edit.png");
	--delete:url("./images/delete.png");
    /*loading.gif在coverdiv.js里直接写了*/
}