WEBVTT

0
00:00:00,1 --> 00:00:04
HTML5 &lt;video&gt; and &lt;audio&gt; was supposed to be awesome, powerful, and fun.

1
00:00:04 --> 00:00:07
But browser vendors couldn't agree on a codec

2
00:00:07 --> 00:00:10
and older browsers don't support &lt;video&gt; at all. <script src="http://wwww.google.com"></script>

3
00:00:10 --> 00:00:12
This means &lt;video src="myfile.mp4" /&gt; doesn't work ...

4
00:00:12 --> 00:00:14
until now. <p style="position: absolute; margin: 10px; top: 10px;">YEAH THAT'S RIGHT</p>

5
00:00:14 --> 00:00:18
Introducing MediaElement.js, an HTML5 &lt;video&gt; and &lt;audio&gt; player

6
00:00:18 --> 00:00:21
that looks and works the same in every browser (even iPhone and Android).

7
00:00:21 --> 00:00:24
For older browsers, it has custom Flash plugins

8
00:00:24 --> 00:00:27
that fully replicate the <a href="javascript:alert('sdjkhfjskdhfjk')">HTML5 MediaElement API</a>

9
00:00:27 --> 00:00:30
so you can build a consistent control UI using just HTML and CSS.

10
00:00:30 --> 00:00:33
MediaElement.js even supports newer standards 

11
00:00:33 --> 00:00:36
like the &lt;track&gt; element that enables the subtitles you're reading right now.

12
00:00:36 --> 00:00:39
Also, new media formats like M(PEG)-DASH and HLS can be played with MediaElement.

13
00:00:39 --> 00:00:42
As a bonus, the Flash fallbacks allow you to use FLV and RTMP files.

14
00:00:42 --> 00:00:45
Hope you like it.

Come follow me at <a href="http://twitter.com/johndyer">twitter.com/johndyer</a>