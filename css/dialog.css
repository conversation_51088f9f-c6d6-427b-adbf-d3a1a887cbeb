@import"theme.css";



div.dialog {
	position:absolute; 
    left:10%;
	position: fixed;
	top: 15%;
    width:80%;
	z-index:600;
	background:var(--theme_dialog_background_color);
	border-radius: 10px;
}

p.dialog_title{
    white-space:pre-line;/*让\n可以换行*/
    text-align:center;
    color:var(--theme_title_color);
}

.dialog-btn{
	background:var(--theme_btn_background_color);
    color: var(--theme_btn_text_color);
	font-size: 15px;
	font-weight:800;
	padding: 0.3em 1em;
	margin-right:0.5em;
	margin-bottom:0.5em;
	border-radius: 4px;
	box-shadow: inset 0 -2px 0 0 rgba(0,0,0,0.2);
	cursor: pointer;
	border:solid 0.1em;
	border-color:var(--theme_btn_border_color);
	float:right;
}

.dialog-cancelbtn{
	background:var(--theme_cancelbtn_background_color);
    color: var(--theme_cancelbtn_text_color);
	font-size: 15px;
	font-weight:800;
	padding: 0.3em 1em;
	margin-right:0.5em;
	margin-bottom:0.5em;
	border-radius: 4px;
	box-shadow: inset 0 -2px 0 0 rgba(0,0,0,0.2);
	cursor: pointer;
	border:solid 0.1em;
	border-color:var(--theme_cancelbtn_border_color);
	float:right;
}

.dialog-btn:hover,.dialog-cancelbtn:hover{
	box-shadow: inset 0 -2px 0 0 rgba(0,0,0,0.6), 0 0 8px 0 rgba(0,0,0,0.5);
}
.dialog-btn:active,.dialog-cancelbtn:active{
	position: relative;
	top: 2px;
	box-shadow: inset 0 3px 5px 0 rgba(0,0,0,0.2);
}

div.settime{
	position:relative;
	width:100%;
	height:25px;    
	margin-top: 0.5em;
	}
p.settime{
	position:absolute; 
	left:10px;
	color: #ffffff;
	font-size:20px;
	top: -20px;
	text-decoration:none;
}
input.settime{
	position:absolute; 
	color: #ffffff;
	background: #393939;
	font-size:15px;
	right:10px;
	width:40px;
}

div.inputtext{
	position:relative;
	width:100%;
	height:25px;    
	margin-top: 0.5em;
	}
p.inputtext{
	position:absolute; 
	left:5px;
	color: #ffffff;
	font-size:12px;
	top: -10px;
	text-decoration:none;
}
input.inputtext,input.inputtext_r{
	position:absolute; 
	color: #ffffff;
	background: #393939;
	font-size:12px;
	right:10px;
	width:40px;
}

input.inputtext{
	background: #393939;
}

input.inputtext_r{
	background: #ff0000;
}

div.IPtext{
	color: #ffffff;
	margin-top: 0.5em;
	margin-left: 0.5em;
}

p.IPtext{
	color: #ffffff;
	font-size:12px;
	margin-bottom: 0em;
}

input.IPtext{
	color: #ffffff;
	background: #393939;
	font-size:10px;
	width:35px;
}

div.svcheckboxgroup{
    position:relative;
    width:100%;
    height:25px;
    margin-top: 0.5em;
}

p.svcheckboxgroup_title{
    position:absolute;
    left:5px;
    color: #ffffff;
    font-size:15px;
    top: -10px;
    word-wrap:break-word;
    word-break:break-all;
    text-decoration:none;
}

p.svcheckboxgroup_option_text{
    position:absolute;
    font-weight:var(--theme_option_text_font_weight);
    text-transform:var(--theme_option_text_text_transform);
    color:var(--theme_option_text_color);
    display: inline-block;
    font-size:12px;
    top: -5px;
    text-align:center;
    left: 80%;
}

div.svcheckboxgroup_labeldiv_container{
    position:absolute;
    top: 6px;
    left: 60%;
}

div.svcheckboxgroup_labeldiv{
        position:relative;
        display: inline-block;
        margin-right: -5px;
}

div.svcheckboxgroup_labeldiv input[type=checkbox]{
    display: none;
    -webkit-appearance: none;/*去除系统默认appearance的样式,常用于IOS下移除原生样式*/
    margin: 0;
}

.svcheckboxnormal+label {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: transparent var(--svcheckbox_off) no-repeat;
    margin-right: 5px;
    background-size: cover;
}

.svcheckboxnormal:checked+label {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: transparent var(--svcheckbox_on) no-repeat;
    margin-right: 5px;
    background-size: cover;
}

.svcheckboxred+label {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: transparent var(--svcheckbox_off) no-repeat;
    margin-right: 5px;
    background-size: cover;
}

.svcheckboxred:checked+label {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: transparent var(--svcheckbox_red) no-repeat;
    margin-right: 5px;
    background-size: cover;
}

.svcheckboxyellow+label {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: transparent var(--svcheckbox_off) no-repeat;
    margin-right: 5px;
    background-size: cover;
}

.svcheckboxyellow:checked+label {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: transparent var(--svcheckbox_yellow) no-repeat;
    margin-right: 5px;
    background-size: cover;
}

.svcheckboxgreen+label {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: transparent var(--svcheckbox_off) no-repeat;
    margin-right: 5px;
    background-size: cover;
}

.svcheckboxgreen:checked+label {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: transparent var(--svcheckbox_green) no-repeat;
    margin-right: 5px;
    background-size: cover;
}

div.password{
	position:relative;
	width:100%;
	height:25px;    
	margin-top: 0.5em;
	}
p.password{
	position:absolute; 
	left:5px;
	color: #ffffff;
	font-size:12px;
	top: -10px;

	text-decoration:none;
}
input.password{
	position:absolute; 
	color: #ffffff;
	background: #393939;
	font-size:10px;
	right: 35px;
        width:55px;
}
.passwordSwitchBtn+label {
	position:absolute; 
	right: 0px;
	width: 20px;
	height: 20px;
	top: -2px;
	display: inline-block;
	background: transparent var(--pw_visible_off) no-repeat;
	background-size: contain;
	cursor: pointer;
	} 
.passwordSwitchBtn:checked+label {
	background: transparent var(--pw_visible_on) no-repeat;
	background-size: contain;
}
div.switchgroup{
	position:relative;
	width:100%;
	height:25px;    
	margin-top: 0.5em;
}
p.switchgroup{
	position:absolute; 
	left:5px;
	font-weight:var(--theme_option_text_font_weight);
    text-transform:var(--theme_option_text_text_transform);
    color:var(--theme_option_text_color);
	font-size:12px;
	top: -10px;
	text-decoration:none;
}
div.switchgroup_labeldiv{
	position:absolute; 
	right:10px;
}

div.selectgroup{
        position:relative;
        width:100%;
        height:25px;
        margin-top: 0.5em;
}
p.selectgroup{
        position:absolute;
        left:5px;
        color: #ffffff;
        font-size:12px;
        top: -10px;

        text-decoration:none;
}
div.selectgroup_selectdiv{
        position:absolute;
        right:10px;
}

div.radiogroup{
	position:relative;
	width:50%;
	height:25px;
	margin-top: 0.5em;
	display: inline-block;
}
div.radiogroup_singlerow{
        position:relative;
        width:100%;
        height:25px;
        margin-top: 0.5em;
        display: inline-block;
}
p.radiogroup{
	position:absolute; 
	left:5px;
	font-weight:var(--theme_option_text_font_weight);
    text-transform:var(--theme_option_text_text_transform);
    color:var(--theme_option_text_color);
	font-size:12px;
	top: -10px;
    width:50%;
    word-wrap:break-word;
    word-break:break-all;
	text-decoration:none;
}
div.radiogroup_labeldiv{
	position:absolute; 
	top: 5px;
	right:5px;
}
input[type="radio"]{
    display: none;
	-webkit-appearance: none;/*去除系统默认appearance的样式,常用于IOS下移除原生样式*/
	margin: 0;
}
.radioBtn+label {
	width: 20px;
    height: 20px;
    display: inline-block;
    background: transparent var(--radio_off) no-repeat;
	margin-right: 5px;
    background-size: cover;
	} 
.radioBtn:checked+label {
	background: transparent var(--radio_on) no-repeat;
	background-size: contain;
}

div.canid_item{
	margin-left:10px;
    margin-bottom:5px;
}

div.canid_item input{
	background:#393939;
    color:#fff;
    width:80px;
    display:inline;
}

p.canid_item_p1{
	display:inline;
	color:var(--theme_color);
}

p.canid_item_p2{
	display:inline;
	color:#fff;
}

p.gpsitem{
	position:relative;
	width:50%;
	font-size:12px;
	display: inline-block;
	color:#fff;
	margin-top: 0px;
	margin-bottom: 0px;
	background:var(--theme_color);
}
div.multi_selectbox{
	position:relative;
	width:100%;
	height:20px;    
	margin-top: 0.5em;
}

.multi_selectbox_text{
    font-weight:var(--theme_icon_text_font_weight);
    text-transform:var(--theme_option_text_text_transform);
    color:var(--theme_text_color);
}

table.multi_selectbox_table{
    position:relative;
    float:right;
    margin-right:50%;
}

p.multi_selectbox_title{
	position:absolute; 
	left:10px;
	color: #ffffff;
	font-size:12px;
	top: -10px;
	text-decoration:none;
}