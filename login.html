<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" href="./css/jquery.mobile.css" />
		<link rel="stylesheet" href="./css/jquery.extra-themes.css" />
		<link rel="stylesheet" href="./css/font.css" />
		<link rel="stylesheet" href="./css/common.css" />
		<!--link rel="stylesheet" href="./css/common.css"-->
		<script src="./js/jquery.min.js"></script>
		<script src="./js/jquery.mobile.js"></script>
		<script src="./js/jquery.mobile.theme.js"></script>
		<script src="./js/webapp-language.js"></script>
		<title data-desc="pag-title">Login</title>
		<script type="text/javascript">
			// 添加全局变量
			window.isScustomer200001 = false;

			$(document).ready(function () {
				var itempasswd1 = window.location.host + "-pwd";
				var itemremember1 = window.location.host + "-rem";
				var itemusername1 = window.location.host + "-usr";
				
				// 恢复密码
				var passwordElement = document.getElementById("password");
				if (passwordElement) {
					passwordElement.value = localStorage.getItem(itempasswd1) || "";
				}
				
				// 恢复记住我状态
				var rememberElement = document.getElementById("remember");
				if (rememberElement) {
					rememberElement.checked = (localStorage.getItem(itemremember1) === 'true');
				}
				
				// 恢复用户名 - 使用延时确保其他初始化代码执行完毕
				setTimeout(function() {
					var usernameElement = document.getElementById("username");
					if (usernameElement && !usernameElement.readOnly) {
						// 只有在元素存在且不是只读的情况下才恢复用户名
						var savedUsername = localStorage.getItem(itemusername1);
						if (savedUsername && localStorage.getItem(itemremember1) === 'true') {
							usernameElement.value = savedUsername;
						}
					}
				}, 200);
			});

			
			window.localStorage = window.localStorage || window.sessionStorage;
			var myLogin = (function ($) {
				var lang_json = general_lang,
					g_capital = false;
				var g_lang,
					itemLang = window.location.host + "-lang";
				window.localStorage && (g_lang = window.localStorage.getItem(itemLang));
				g_lang = g_lang || "EN";

				jQuery.fn.shake = function (
					intShakes /*Amount of shakes*/,
					intDistance /*Shake distance*/,
					intDuration /*Time duration*/
				) {
					this.each(function () {
						var jqNode = $(this);
						jqNode.css({ position: "relative" });
						for (var x = 1; x <= intShakes; x++) {
							jqNode
								.animate(
									{ left: intDistance * -1 },
									intDuration / intShakes / 4
								)
								.animate({ left: intDistance }, intDuration / intShakes / 2)
								.animate({ left: 0 }, intDuration / intShakes / 4);
						}
					});
					return this;
				};
				$(document).keydown(function (e) {
					var keyCode = e.keyCode || e.which;
					if (keyCode == 13) {
						myLogin.OnSubmit();
					}
				});
				$(document).ready(function () {
					var pwd,
						theme,
						itemPassword = window.location.host + "-pwd",
						itemTheme = window.location.host + "-theme",
						$password = $("#password");
					window.localStorage &&
						(pwd = window.localStorage.getItem(itemPassword));
					if (window.localStorage) {
						if (window.localStorage.getItem(itemTheme)) {
							theme = window.localStorage.getItem(itemTheme);
						} else {
							if (jquery_mobile_theme) {
								theme = jquery_mobile_theme;
								window.localStorage.setItem(itemTheme, jquery_mobile_theme);
							}
						}
					}
					theme = theme || "b";
					changeTheme(theme);
					if (pwd != null) {
						$("#remember").attr("checked", true); //.checkboxradio("refresh");
						$password.val(pwd);
					}
					$password.keydown(function (e) {
						if (e.keyCode === 20 && g_capital) {
							$("#captital-password").hide();
							g_capital = false;
						}
					});
					$password.keypress(function (e) {
						var keyCode = e.keyCode || e.which;
						var isShift = e.shiftKey || keyCode === 16 || false;
						if (keyCode === 9) {
							$("#captital-password").hide();
							g_capital = false;
						} else if (
							(keyCode >= 65 && keyCode <= 90 && !isShift) ||
							(keyCode >= 97 && keyCode <= 122 && isShift)
						) {
							$("#captital-password").show();
							g_capital = true;
						} else {
							$("#captital-password").hide();
							g_capital = true;
						}
					});
				});
				function changeLang(translate_lang) {
					$("*").each(function (index, dom) {
						var lang_id = $(dom).jqmData("desc");
						if (lang_id) {
							// 检查当前语言字典中是否有对应的翻译
							if (lang_json[lang_id] && lang_json[lang_id][translate_lang]) {
								$(dom).html(lang_json[lang_id][translate_lang]);
							}
						}
					});
					$(":button").val(lang_json["login"][translate_lang]);
					$("#Calibration").val(lang_json["Loginless"][translate_lang]);
					
					// 确保登录标题设置正确 - 添加延时确保在其他函数执行后执行
					setTimeout(function() {
						var scustomer = window.localStorage.getItem(window.location.host + "-scustomer");
						if (scustomer !== "200001") {
							// 非200001客户显示"welcome to Login"
							if (lang_json["welcome-login"] && lang_json["welcome-login"][translate_lang]) {
								$("#title").html(lang_json["welcome-login"][translate_lang]);
							} else {
								$("#title").html("Welcome to Login");
							}
						} else {
							// 200001客户保持显示"LOGIN"
							if (lang_json["login"] && lang_json["login"][translate_lang]) {
								$("#title").html(lang_json["login"][translate_lang]);
							} else {
								$("#title").html("LOGIN");
							}
						}
						console.log("标题设置完成: " + $("#title").html());
					}, 50);
				}
				function changeTheme(t) {
					$("#page-login")
						.removeClass("ui-page-theme-a ui-page-theme-b")
						.addClass("ui-page-theme-" + t);
					if (t === "f") {
						$("#page-login").css("background-color", "#1f2a44");
						$("#login-form").css({
							"background-color": "#1f2a44",
							"border-width": "0px",
						});
						$("#login-header").css("display", "none");
						$(".checkbox-container").addClass("ui-group-theme-fa");
						$("#login-btn-group").addClass("ui-group-theme-fb");

						$("#logo").css("display", "block");
						$("#logo").attr("src", "./css/images/logo_luis.png");
					}
				}
				function changeCSS(customer) {
					if (customer == "200032") {
						if (
							navigator.userAgent.match(
								/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
							)
						) {
							$("<link>")
								.attr({
									rel: "stylesheet",
									type: "text/css",
									href: "./css/luis-mobilecommon.css",
								})
								.appendTo("head");
						} else {
							$("<link>")
								.attr({
									rel: "stylesheet",
									type: "text/css",
									href: "./css/luis-common.css",
								})
								.appendTo("head");
						}
					} else {
						if (
							navigator.userAgent.match(
								/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
							)
						) {
							$("<link>")
								.attr({
									rel: "stylesheet",
									type: "text/css",
									href: "./css/mobilecommon.css",
								})
								.appendTo("head");
						} else {
							$("<link>")
								.attr({
									rel: "stylesheet",
									type: "text/css",
									href: "./css/common.css",
								})
								.appendTo("head");
						}
					}
				}
				$(document).on("pagebeforecreate", function () {
					var lang,
						itemLang = window.location.host + "-lang";
					window.localStorage && (lang = window.localStorage.getItem(itemLang));
					lang = lang || "EN";
					$.ajax({
						type: "GET",
						url: "/config",
						success: function (data, status) {
							if (
								data.hasOwnProperty("ipcIdentification") &&
								data.ipcIdentification.hasOwnProperty("scustomer")
							) {
								var hardware = data.ipcIdentification.hardware || "";
								var scustomer = data.ipcIdentification.scustomer || "";
								// 检查customer属性是否存在
								var customer = data.ipcIdentification.customer || "";

								// 安全处理calibration显示逻辑
								if (customer && (
									customer.indexOf("202018") != -1 ||
									customer.indexOf("202122") != -1 ||
									customer.indexOf("201623") != -1
								)) {
									$("#Calibration").show();
									$("#Calibration").css("width", "100%");
								} else {
									$("#Calibration").hide();
								}
								
								// 存储scustomer值到localStorage
								var itemCustomer = window.location.host + "-scustomer";
								window.localStorage &&
									window.localStorage.setItem(itemCustomer, scustomer);
								
								// 也存储到-customer键，以兼容login.js
								var itemCustomerOld = window.location.host + "-customer";
								window.localStorage &&
									window.localStorage.setItem(itemCustomerOld, scustomer);
								
								// 使用scustomer值调用changeCSS
								changeCSS(scustomer);
								
								// 保存颜色设置到localStorage以便在页面切换后恢复
								var itemThemeColor = window.location.host + "-theme-color";
								
								// 判断scustomer是否为200001
								if (scustomer !== "200001") {
									document.getElementById("username").value = "admin";
									document.getElementById("username").readOnly = true;
									document.getElementById("username").style.backgroundColor = "#f0f0f0";
									// 直接设置使用该变量的元素
									document.body.style.setProperty('--sk_color', '#0ab3cf');
									// 保存颜色设置到localStorage
									window.localStorage && window.localStorage.setItem(itemThemeColor, '#0ab3cf');
								
									
									// 非200001客户使用简化logo
									$("#logo").css({
										"display": "none",
										"width": "0%",
										"height": "0%",
										"border-radius": "10px",
										"margin": "0 auto"
									});
									$("#logo").removeAttr("src");
									// 标记logo已设置
									window.logoSetByCustomer = true;
									
									// // 非200001客户显示"welcome to Login"
									$("#title").text(lang_json["welcome-login"][lang] || "Welcome to Login");
								} else {
									document.getElementById("username").value = "";
									// 200001客户使用Brigade logo
									$("#logo").css({
										"display": "block",
										"width": "100%", 
										"height": "auto",
										"border-radius": "10px",
										"margin": "0 auto",
										"background": "transparent",
										"mix-blend-mode": "multiply"
									});
									$("#logo").attr("src", "./css/images/logo_Brigade.png");
									// 标记logo已设置
									window.logoSetByCustomer = true;
									window.isScustomer200001 = (scustomer === "200001");
									// // 200001客户保持显示"LOGIN"
									$("#title").text(lang_json["login"][lang] || "LOGIN");
								}
							} else {
								console.warn("未找到ipcIdentification.scustomer值");
							}
							window.localStorage &&
								window.localStorage.setItem(itemLang, lang);
							if (lang == "INC" || lang == "TEL") lang = "EN";

							changeLang(lang);
						}.bind(this),
						error: function (XMLHttpRequest) {}.bind(this),
						dataType: "json",
					});
				});
				return {
					OnSubmit: function () {
						var scustomer = window.localStorage.getItem(window.location.host + "-scustomer");
						console.log("OnSubmit - 当前客户类型:", scustomer);

						// 应用存储的颜色设置
						var savedColor = window.localStorage.getItem(window.location.host + "-theme-color");
						if (savedColor) {
							document.body.style.setProperty('--sk_color', savedColor, 'important');
							console.log("应用保存的颜色设置:", savedColor);
						}

						var lang,
							$password = $("#password"),
							$username = $("#username"),
							$showError = $("#show-error");
						var json = { usr: $username.val(), pwd: $password.val() };
						var json_str = JSON.stringify(json);
						$.mobile.loading("show");
						$.ajax({
							type: "POST",
							url: "login",
							data: json_str + "\r\n" + "pwd=" + $password.val(),
							success: function (data, status) {
								$.mobile.loading("hide");
								var itemPassword = window.location.host + "-pwd";
								var itemRemember = window.location.host + "-rem";
								var itemUsername = window.location.host + "-usr";
								if ($("#remember").is(":checked")) {
									window.localStorage &&
										window.localStorage.setItem(
											itemPassword,
											$("#password").val()
										);
									// 只有在用户名不是只读的情况下才保存用户名
									var usernameElement = document.getElementById("username");
									if (usernameElement && !usernameElement.readOnly) {
										window.localStorage &&
											window.localStorage.setItem(
												itemUsername,
												$("#username").val()
											);
									}
									window.localStorage &&
										window.localStorage.setItem(
											itemRemember,
											document.getElementById("remember").checked
										);
								} else {
									window.localStorage &&
										window.localStorage.removeItem(itemPassword);
									window.localStorage &&
										window.localStorage.removeItem(itemUsername);
									window.localStorage &&
										window.localStorage.removeItem(itemRemember);
									window.localStorage && window.localStorage.setItem(itemRemember,document.getElementById("remember").checked);
								}
								console.log("原始登录方法 - 登录成功，即将跳转");
								window.location.href = "index.html";
							},
							error: function (XMLHttpRequest) {
								$.mobile.loading("hide");
								switch (XMLHttpRequest.status) {
									case 401:
										$showError
											.text(lang_json["pwd-error"][g_lang])
											.shake(2, 10, 400);
										break;
									case 503:
										$showError
											.text(lang_json["sys-busy"][g_lang])
											.shake(2, 10, 400);
										break;
									default:
										$showError
											.text(lang_json["sys-internal-error"][g_lang])
											.shake(2, 10, 400);
								}
							},
						});
					},

					Calibration: function () {
						window.location.href = "no_password.html";
					},
				};
			})(jQuery);
		</script>
		<script src="./js/login.js"></script>
		<style>
			@font-face {
				font-family: GraublauWeb;
				src: url("./css/GothamProNarrowMedium.ttf");
			}
		</style>
	</head>

	<body>
		<div id="page-login" data-role="none" data-dom-cache="true">
			<div id="login-header" class="title">
				<p id="login-text" data-desc="AI-Camera">AI-Camera</p>
			</div>

			<div data-role="none" id="login-form" class="login_form" style="
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				-webkit-transform: translate(-50%, -50%);
				-moz-transform: translate(-50%, -50%);
				background: var(--theme_item_background_color);
				width: 300px;
				padding: 30px;
				border-style: solid;
				border-width: 1px;
				border-color: #343434;
				border-radius: 10px;
				box-shadow: 0 0 10px rgba(0,0,0,0.1);
			">
				<img
					id="logo"
					style="
						display: block;
						width: 100%;
						height: auto;
						border-radius: 10px;
						margin: 0 auto;
						background: transparent;
						mix-blend-mode: multiply;
					"
					src="./css/images/logo_Brigade.png"
				/>
				<h2
					id="title"
					class="single_option_text fct_single_option_text"
					style="
						font-family: 'Gotham-Narrow-Book';
						font-weight: normal;
						text-align: center;
						display: block;
					"
					data-desc="welcome-login"
				>
					LOGIN
				</h2>
				<form name="input" method="POST" action="#">
					<div data-role="none" class="input-text-box">
						<label
							class="fct_single_option_text"
							data-role="none"
							id="tab-username"
							for="username"
							data-desc="username"
							>User：</label
						>
						<input
							class="normal_input"
							data-role="none"
							name="usr"
							id="username"
							value=""
							type="text"
						/>
					</div>
					<div data-role="none" class="input-text-box">
						<label
							class="fct_single_option_text"
							data-role="none"
							id="tab-password"
							for="password"
							data-desc="password"
							>Password：</label
						>
						<input
							class="normal_input"
							data-role="none"
							name="pwd"
							id="password"
							value=""
							type="password"
						/>
					</div>
					<div id="captital-password" style="display: none">
						<span data-desc="caps-lock">Caps lock is turned on</span>
					</div>
					<div data-role="none" class="single_option">
						<p style="width: 90%">
							<lan
								data-role="none"
								class="single_option_text fct_single_option_text"
								data-desc="remember"
								>Remember Me</lan
							>
						</p>
						<div>
							<input
								data-role="none"
								class="checkBtn custom"
								type="checkbox"
								name="remember"
								id="remember"
							/><label data-role="none" for="remember"></label>
						</div>
					</div>
					<p onselectstart="return false;" id="show-error">&nbsp;</p>
					<button
						data-role="none"
						class="btn"
						style="width: 100%"
						type="button"
						onclick="myLogin.OnSubmit()"
					>
						Login
					</button>
					<input
						style="display: none"
						id="Calibration"
						data-role="none"
						class="btn"
						type="button"
						value="Calibration"
						onclick="myLogin.Calibration()"
					/>
				</form>
			</div>
		</div>

		<script type="text/javascript">
			$(document).ready(function () {
				// 确保标题显示正确
				setTimeout(function() {
					var lang = window.localStorage.getItem(window.location.host + "-lang") || "EN";
					var scustomer = window.localStorage.getItem(window.location.host + "-scustomer");
					
					if (scustomer !== "200001") {
						// 非200001客户显示"welcome to Login"
						if (general_lang["welcome-login"] && general_lang["welcome-login"][lang]) {
							$("#title").html(general_lang["welcome-login"][lang]);
						} else {
							$("#title").html("Welcome to Login");
						}
						console.log("非200001客户显示欢迎登录文本");
					} else {
						// 200001客户保持显示"LOGIN"
						if (general_lang["login"] && general_lang["login"][lang]) {
							$("#title").html(general_lang["login"][lang]);
						} else {
							$("#title").html("LOGIN");
						}
						console.log("200001客户显示标准登录文本");
					}
				}, 200);
			});
		</script>
	</body>
</html>
