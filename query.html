<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="./css/jquery.mobile.css">
<link rel="stylesheet" href="./css/jquery.extra-themes.css">
<!--link rel="stylesheet" href="./css/common.css"-->
<link rel="stylesheet" href="./css/mediaelementplayer.css">
<script src="./js/jquery.min.js"></script>
<script src="./js/jquery.mobile.js"></script>
<script src="./js/mediaelement-and-player.min.js"></script>
<script src="./js/FileSaver.js"></script>
<script src="./js/webapp-language.js"></script>
<script src="./js/login.js"></script>
<title data-desc="query">Query</title>
	<style>
		.box{
                width: 200px;
                height: 30px;
                border-radius: 15px;
                margin: 10px 0;
                background: url("./css/images/search.png") no-repeat!important;
                background-color: #f00;
                background-position: 3px;
                padding-left: 30px;
                border: 1px solid black;
                outline: none;
            }
		#tab-log {display: none;}
		#tab-recordlist {display: none;}
		#tab-alarmlist {display: none;}
		#popup-show-info {min-width:200px; max-width:400px;}
		.hide-anchor {display: none;}
		#popup-ask-for-sure {min-width:200px; max-width:400px; padding:10px;}
		.ui-content .ui-grid-a .ui-block-a {width: 30%}
		.ui-content .ui-grid-a .ui-block-b {width: 70%}
		.ui-icon-log:after {
			background-image: url("./css/images/icons-png/log-white.png");
			background-size: 14px 14px;
		}
		#mediaplayer {
			max-width:100%;
		}
		.ui-popup-container {
			z-index: 1100;
			display: inline-block;
			position: absolute;
			padding: 0;
			outline: 0;
		}
		#popup-show-info {
			min-width: 200px;
			max-width: 400px;
			background-color: #fff;
			border-color: #ddd;
			color: #333;
			text-shadow: 0 1px 0 #f3f3f3;
			border-width: 1px;
			border-style: solid;
		}
		.ui-content {
			border: 0;
			padding: 0;
			margin: 0;
			padding: 1em;
		}
		.ui-header, .ui-footer {
			border-width: 1px 0;
			border-style: solid;
			position: relative;
			background-color: #e9e9e9 !important;
			border-color: #ddd !important;
			color: #333 !important;
			text-shadow: 0 1px 0 #eee !important;
			font-weight: 700 !important;
		}
		.ui-btn-inline {
			background-color: #f6f6f6 !important;
			border-color: #ddd !important;
			color: #333 !important;
			text-shadow: 0 1px 0 #f3f3f3 !important;
		}
		#page-query{
			min-height: 887px;
		}
		input#jump-page {
			width:30px;
		}
	</style>
</head>
<body>
	<div id="page-query" data-role="page" style="background:#fff;" data-dom-cache="true">
		<div class='title index_hide_group'>
			<a class="title_back" href="./index.html" data-ajax="false"></a>
    		<p data-desc="query">Query</p>
    		<!--a class="btn-refresh title_refresh"></a-->
		</div>
		
		<div class='title_hide index_hide_group'></div>

		<div class="index_hide_group">
			<table class="menu" rules="none" cellspacing="5%">
            	<td><li><a class="menu_checked xbtn-nav"  data-goto-id="tab-status" data-desc="query-status">状态查询</a></li></td>
				<td id="li_recordlist" style="display: none;"><li><a class="xbtn-nav"  data-goto-id="tab-recordlist" data-desc="query-record">录像查询</a></li></td>
				<td><li><a class="xbtn-nav"  data-goto-id="tab-log" data-desc="query-log">日志查询</a></li></td>
				<td id="li_alarmlist" style="display: none;"><li><a class="xbtn-nav" data-goto-id="tab-alarmlist" data-desc="query-alarm">警报查询</a></li></td>
    		</table>
		</div>
							
		<div role="main" data-role="none" class="ui-content" id="win-img">
			<div id="tab-status" class="tab-query">
				<div id="status-list"></div>
				<script id="status-list-template" type="text/x-handlebars-template">
					<div class="infomenu"  {{{hideHardware "WFCR20S2 WFTR20S3"}}}>
						<div><p>{{getKeyLang "ethernet"}}</p></div>
						<div><p1 data-desc="ipaddr">IP Address</p1><p2>{{ethernet.ipAddr}}</p2></div>
						<div><p1 data-desc="subnet-mask">Subnet Mask</p1><p2>{{ethernet.submask}}</p2></div>
						<div><p1 data-desc="mac-address">MAC Address</p1><p2>{{ethernet.macAddr}}</p2></div>
					</div>
					{{#hasOwnProperty this "wifi"}}
					<div class="infomenu">
						<div><p>{{getKeyLang "wifi"}}</p></div>
						<div><p1 data-desc="module">Module</p1><p2>{{wifi.module}}</p2></div>
						<div><p1 data-desc="dual-band">Dual Band</p1><p2>{{wifi.dualBand}}</p2></div>
						<div><p1 data-desc="country-code">Country Code</p1><p2>{{wifi.countryCode}}</p2></div>
						<div><p1 data-desc="tx-pwr">Tx Power</p1><p2>{{wifi.txPower}}</p2></div>
						<div><p1 data-desc="cur-channel">Current Channel</p1><p2>{{wifi.curChannel}}</p2></div>
						<div {{{showHardware "DMS31V2 ADA47V1 WFCR20S2 WFTR20S3"}}}><p1 data-desc="sta-enable">STA Enable</p1><p2>{{wifi.staEnable}}</p2></div>
						<div {{{showHardware "DMS31V2 ADA47V1 WFCR20S2 WFTR20S3"}}}><p1 data-desc="sta-ssid">STA SSID</p1><p2>{{wifi.staSsid}}</p2></div>
						<div {{{showHardware "DMS31V2 ADA47V1 WFCR20S2 WFTR20S3"}}}><p1 data-desc="sta-ipAddr">STA IpAddr</p1><p2>{{wifi.staIpAddr}}</p2></div>
						<div {{{showHardware "DMS31V2 ADA47V1 WFCR20S2 WFTR20S3"}}}><p1 data-desc="sta-status">STA Status</p1><p2>{{wifi.staStatus}}</p2></div>
						<div {{{showHardware "DMS31V2 ADA47V1 WFCR20S2 WFTR20S3"}}}><p1 data-desc="sta-signal">STA Signal</p1><p2>{{wifi.staSignal}}</p2></div>
					</div>
					{{/hasOwnProperty}}
					
					<div class="infomenu" {{{hideHardware "ADA32V2 ADA32C4 ADA32V3 RCT16947V2 ADA32IR DMS31V2 DMS885N ADA42PTZV1" }}}>
						<div><p>{{getKeyLang "media-config"}}</p></div>
						<div {{#unequal media.isNightMode true}}style="display: none;"{{/unequal}}>
							<p1 data-desc="night-mode">Night Mode</p1><p2>{{getKeyLang "on"}}</p2>
						</div>
						<div {{#unequal media.isNightMode false}}style="display: none;"{{/unequal}}>
							<p1 data-desc="night-mode">Night Mode</p1><p2>{{getKeyLang "off"}}</p2>
						</div>

						<div><p1 data-desc="main-bitrate">Main Bitrate</p1><p2>{{media.mainBitrate}}</p2></div>
						<div {{{hideHardware "WFTR20S3 IPTR20S1" }}}><p1 data-desc="sub-bitrate">Sub Bitrate</p1><p2>{{media.subBitrate}}</p2></div>
						<div {{{hideNotHardware "WFTR20S1 WFTR20S2 WFTR20S3 IPTR20S1" }}}><p1 data-desc="video-mode">Video Mode</p1><p2>{{media.videoMode}}</p2></div>
						<div {{{hideHardware "WFTR20S1 WFTR20S2 WFTR20S3 IPTR20S1" }}}><p1 data-desc="sensor-type">Sensor Type</p1><p2>{{media.sensorType}}</p2></div>
						<div {{{hideHardware "ADA32V2  ADA32C4 ADA32V3 RCT16947V2 ADA32IR ADA42PTZV1 WFTR20S1 WFTR20S2 WFTR20S3 IPTR20S1" }}} ><p1 data-desc="filter-type">Filter Type</p1><p2>{{media.filter}}</p2></div>
					    <!--<div><p1 data-desc="temperature">Chip Temperature</p1><p2>{{media.temperature}}</p2></div>-->
					</div>
					<div class="infomenu" {{{hideHardware "ADA32V2  ADA32C4 ADA32V3 RCT16947V2 ADA32IR DMS31V2 DMS885N ADA42PTZV1" }}}>
						<div><p>{{getKeyLang "rtsp-client"}}</p></div>
						<table>
							<tr>
								<td data-desc="stream">Stream</td>
								<td data-desc="client-addr">Client Address</td>
							</tr>
							{{#each clientList}}
							<tr>
								<td>{{streamName}}</td>
								<td>{{clientAddr}}</td>
							</tr>
							{{/each}}
						</table>
					</div>

					<div class="infomenu" {{{showHardware "DMS31V2 ADA32C4"}}} {{{hideCellular}}} {{{hideBoard "37"}}} {{{hideHardware "ADA32V2 ADA32V3 RCT16947V2 ADA32IR ADA42PTZV1" }}}>
						<div><p>{{getKeyLang "cell-info"}}</p></div>
						<div><p1 data-desc="cell-moduleType">ModulStat</p1><p2>{{ModuleType}}</p2></div>
						<div><p1 data-desc="cell-hadSIM">hadSIM</p1><p2>{{hadSIM}}</p2></div>
						<div><p1 data-desc="cell-netType">NetType</p1><p2>{{NetType}}</p2></div>
						<div><p1 data-desc="cell-signal">Signal</p1><p2>{{Signal}}</p2></div>
						<div><p1 data-desc="cell-moduleDesc">ModuleDesc</p1><p2>{{ModuleDescribe}}</p2></div>
					</div>	

					<div class="infomenu" {{{showHardware "DMS31V2 ADA47V1 ADA32C4"}}} {{{hideHardware "ADA32V2 ADA32V3 RCT16947V2 ADA32IR ADA42PTZV1" }}}>
						<div><p>{{getKeyLang "cmsServer"}}</p></div>
						<div><p1 data-desc="cms-ServerStatus">ServerStatus</p1><p2>{{ServerStatus}}</p2></div>
						<div><p1 data-desc="cms-RegStatus">RegStatus</p1><p2>{{RegStatus}}</p2></div>
					</div>	

					<div class="infomenu" {{{showHardware "DMS31V2 ADA47V1 ADA32C4"}}} {{{hideHardware "ADA32V2 ADA32V3 RCT16947V2 ADA32IR ADA42PTZV1"}}}>
						<div><p>{{getKeyLang "storage"}}</p></div>
						<table>
							<tr>
								<td data-desc="position">Pos</td>
								<td data-desc="state">Stat</td>
								<td data-desc="filesystem">FS</td>
								<td data-desc="total">Total</td>
								<td data-desc="remain">Remain</td>
							</tr>
							{{#each statusList}}
							<tr>
								{{#unequal devStat 0}}
								<td>{{transSdName pos}}</td>
								<td>{{transSdStat devStat}}</td>
								<td>{{transFsType mediaFsType}}</td>
								<td>{{transMB2GB this "fsTotalSizeMB"}}G</td>
								<td>{{getObjVal this "fsRemainPercent"}}%</td>
								{{else}}
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								<td>-</td>
								{{/unequal}}
							</tr>
							{{/each}}
							
						</table>
					</div>
				</script>
				
				<div id="format-sdcard" style="margin-left:10px;margin-top:20px;margin-bottom:40px"{{{showHardware "DMS31V2 ADA47V1 ADA32C4"}}}>
					<script id="format-sdcard-template" type="text/x-handlebars-template">
						<div {{{showHardware "DMS31V2 ADA47V1 ADA32C4"}}}{{{hideHardware "ADA32V2 ADA32V3 RCT16947V2 ADA32IR ADA42PTZV1" }}}>
							<div class="custom-select-box">
								<label class="single_option_text" for="sel-blk-dev" data-desc="sel-device">选择操作设备</label>
								<div><select class="custom-select" id="sel-blk-dev" data-role="none">
									<option value="-1" data-desc="No Device">No device</option>
									<option value="0"{{#isA32}}style="display:none;"{{/isA32}}>SD1</option>
									<option value="3" {{{showHardware "ADA47V1"}}}>EMMC</option>
									<option value="4">USB</option>
									<option value="9" data-desc="all">ALL</option>
								</select></div>
							</div>
							<div style="height:60px"></div>
							<div class="button-wrap" >
							<a data-rel="popup" class="btn-popup btn" data-popup-type="ask-for-sure" data-popup-title="format" data-desc="format-sd">Action</a>
							</div>
						</div>
					</script>
				</div>
				
			</div>
			
			<div id="tab-log" class="tab-query">
				<div style="width:70%;">
					<legend class="single_option_text" style="margin-bottom:10px;" data-desc="log-type" >Select Log Type:</legend>
					<table class="menu menu_a" rules="none" cellspacing="5%" style="width: 350px;">
		            	<td><li>
		            		<a class="btn-log-type menu_checked" value="event" data-desc="log-event">Event</a>
		            	</li></td>
						<td><li>
							<a class="btn-log-type" value="normalLog" data-desc="log-debug">Debug</a>
						</li></td>
						<td><li>
							<a class="btn-log-type" value="all" data-desc="log-all">ALL</a>
						</li></td>
		    		</table>
	    		</div>

				<div style="margin-top:20px;margin-bottom:20px">
					<a id="btn-search-log" class="btn" data-desc="search" data-desc="log-search">Search</a>
				</div>
				
				<div id="log-list"></div>
				<script id="log-list-template" type="text/x-handlebars-template">
					<ol class="log">
						<div class="log_search">
				            <i class="icon-search"></i>
				            <input type="text" id="filter_input" data-role="none" placeholder="filter"/>
				        </div>
						<li data-role="list-divider">NO.&nbsp;&nbsp;Time&nbsp;&nbsp;Content</li>
						{{#hasOwnProperty this "logList"}}
						{{#each logList}}
						<li style="white-space:normal;">[{{transDatetime time}}] {{content}}</li>
						{{/each}}
						{{else}}
						<li style="white-space:normal;">-&nbsp;&nbsp;-</li>
						{{/hasOwnProperty}}
					</ol>
				</script>
			</div>

			<div id="tab-recordlist" class="tab-query">
				<div style="width:100%;">
					<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
		            	<td><li>
		            		<a id="query_normal"  class="btn-filter sel1 " data-sel-id="normal" data-desc="normal-record">Normal</a>
		            	</li></td>
						<td><li>
							<a id="query_alarm" class="btn-filter sel1 menu_checked"  data-sel-id="alarm" data-desc="warning-record">Alarm</a>
						</li></td>
						<td><li>
		            		<a id="query_video" class="btn-filter sel2 menu_checked" data-sel-id="video" data-desc="video" >video</a>
		            	</li></td>
						<td><li>
							<a id="query_pic" class="btn-filter sel2" data-sel-id="jpg" data-desc="picture">picture</a>
						</li></td>
					</table>
	    		</div>
				<div id="record-list">
				<script type="text/template" id="record-list-template">					
					<ul id="recordlist-ul" data-role="listview" data-split-icon="arrow-d" data-split-theme="a" data-inset="true">
						<!--each遍历json数组-->
						{{#each recordList}}
						<li>
							<a href="#" data-rel="popup" data-position-to="window" data-transition="pop" class="{{getResClass this}}" data-file-path="{{getResPath this}}">
								<img style="width:30%;margin-left:10px;margin-top:20px;" src="{{getResIcon this}}">
								<h2 data-file-path="{{getResPath this}}">{{transRecType recType}}</h2>
								<p data-file-path="{{getResPath this}}">{{transPts2Date startTime}} {{recType}} {{transDuration2Time duration}} {{transByte2MB fileSize}}</p>
							</a>
							<a href="{{getResPath this}}" data-rel="popup" data-position-to="window" data-transition="pop" class="btn-download" data-file-path="{{getResPath this}}"></a>
						</li>
						{{/each}}
					</ul>
				</script>
			</div>
			</div>

			<div id="tab-alarmlist" class="tab-query" style="overflow-x:scroll;text-align: center;">
				<div class="infomenu">
					<table rules=rows>
						<tbody id="tbody-alarmlist" style="font-size: 20px;word-break: keep-all;white-space:nowrap;">
							<tr>
							<th data-desc="alarmTime">Time</th>
							<th data-desc="plateNo">Plate No</th>
							<th data-desc="alarmType">Type</th>
							<th data-desc="Longitude">longitude</th> 
							<th data-desc="Latitude">Latitude</th>
							<th data-desc="alarmSpeed">Speed</th>
							</tr>
						</tbody>	
					</table >
				</div>
            </div>
		</div>
		<div class="footer" data-role="footer" data-position="fixed" data-mini="true" style="display: none;">
			<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
				<tr>
					<td><li>
						<a class="btn-page" data-page-id="pre" data-desc="pre" style="margin-bottom: -5px;">pre</a>
					</li></td>
					<td><li>
						<a class="btn-page" data-page-id="next" data-desc="next" style="margin-bottom: -5px;">next</a>
					</li></td>
				</tr>
				<tr>
					<td><li>
						<label data-role="none" for="jump-page" style="display: inline-block"><p data-desc="Page"></p> </label>	
						<input data-role="none" type="text" id="jump-page" class="empty-limit char-normal" style="display: inline-block;">

						<label data-role="none" style="display: inline-block;"><p data-desc="total"></p> </label>
						<p id="total-pages" style="display: inline-block;"></p> 
					</li></td>

					<td><li>
						<a id="jump-page-button" style="margin-bottom: -5px;" data-desc="jump">JUMP</a>
					</li></td>
				</tr>
			</table>
		</div>		
		<a id="show-info" href="#popup-show-info" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
		<div data-role="popup" id="popup-show-info" data-dismissible="false">
			<div data-role="header">
				<h1></h1>
			</div>
			<div role="main" class="ui-content">
				<h3></h3>
				<p></p>
				<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
			</div>
		</div>
		<a id="ask-for-sure" href="#popup-ask-for-sure" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
		<div data-role="popup" id="popup-ask-for-sure" data-dismissible="false" data-popup-title="">
			<div data-role="header">
				<h1></h1>
			</div>
			<div role="main" class="ui-content">
				<h3></h3>
				<p></p>
				<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-desc="cancel">取消</a>
				<a id="btn-ask-for-sure" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
			</div>
		</div>

		
	</div>
	<div  data-role="page" id="page-play-video" data-dom-cache="true">
			<div data-role="header" data-position="fixed">
				<a href="#page-query" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-role="button" data-icon="arrow-l" title="Back Query">
				<span class="ui-btn-inner ui-btn-corner-all">
					<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
				</span>
				</a>
				<h1 tabindex="0" role="heading" aria-level="1" data-desc="play-video">视频回放</h1>
			</div>
			<div id="play-pag" role="main" class="ui-content">
				<div class="media-wrapper">
					<video id="mediaplayer" width="100%" height="100%" preload="none" controls playsinline webkit-playsinline>
						<source id="video-path" src="" type="video/mp4">
						<track srclang="en" kind="subtitles" src="./css/mediaelement.vtt">
						<track srclang="en" kind="chapters" src="./css/chapters.vtt">
					</video>
				</div>
			</div>
	</div>
	<div  data-role="page" id="page-display-picture" data-dom-cache="true">
		<div data-role="header" data-position="fixed">
			<a href="#page-query" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-role="button" data-icon="arrow-l" title="Back Query">
			<span class="ui-btn-inner ui-btn-corner-all">
				<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
			</span>
			</a>
			<h1 tabindex="0" role="heading" aria-level="1" data-desc="play-picture">图片查看</h1>
		</div>
		<div role="main" class="ui-content">
			<img id="pic-player" src="" width="100%">
		</div>
	</div>
	<script src="./js/handlebars.js"></script>
	<script src="./js/dmuploader.min.js"></script>
	<script src="./js/webapp-common.js"></script>
	<script src="./js/webapp-model.js"></script>
	<script src="./js/webapp-view.js"></script>
	<script src="./js/webapp-ctrller.js"></script>
	<script type="text/javascript">
		$(document).ready(function() {
			var customer;
			var itemCustomer = window.location.host+"-customer";
			window.localStorage && (customer = window.localStorage.getItem(itemCustomer));
			if(customer=="200032"){
				if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-mobilecommon.css"}).appendTo("head");
				}else {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-common.css"}).appendTo("head");
				}
			}
			else{
				if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/mobilecommon.css"}).appendTo("head");
				}else {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/common.css"}).appendTo("head");
				}
			}

			switchCssStyle(customer);
			var model = new WebappModel();
			var view = new WebappView(model, "query");
			var controller = new WebappController(model, view);
			controller.getCellularStat();
			controller.refreshQueryInfo();
			controller.startWatchStatus();
		});
	</script>
</body>
</html>
