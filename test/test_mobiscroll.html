﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../css/jquery.mobile-1.4.5.min.css">
	<link href="../css/mobiscroll.animation.css" rel="stylesheet" type="text/css" />
	<link href="../css/mobiscroll.icons.css" rel="stylesheet" type="text/css" />
	<link href="../css/mobiscroll.frame.css" rel="stylesheet" type="text/css" />
	<!--<link href="../css/mobiscroll.frame.android-holo.css" rel="stylesheet" type="text/css" />-->
	<!--<link href="../css/mobiscroll.frame.ios.css" rel="stylesheet" type="text/css" />-->
	<link href="../css/mobiscroll.frame.jqm.css" rel="stylesheet" type="text/css" />
	<!--<link href="../css/mobiscroll.frame.wp.css" rel="stylesheet" type="text/css" />-->
	<link href="../css/mobiscroll.scroller.css" rel="stylesheet" type="text/css" />
	<!--<link href="../css/mobiscroll.scroller.android-holo.css" rel="stylesheet" type="text/css" />-->
	<!--<link href="../css/mobiscroll.scroller.ios.css" rel="stylesheet" type="text/css" />-->
	<link href="../css/mobiscroll.scroller.jqm.css" rel="stylesheet" type="text/css" />
	<!--<link href="../css/mobiscroll.scroller.wp.css" rel="stylesheet" type="text/css" />-->
	<link href="../css/mobiscroll.image.css" rel="stylesheet" type="text/css" />
	<!--<link href="../css/mobiscroll.android-holo-light.css" rel="stylesheet" type="text/css" />-->
	<!--<link href="../css/mobiscroll.wp-light.css" rel="stylesheet" type="text/css" />-->
	<!--<link href="../css/mobiscroll.mobiscroll-dark.css" rel="stylesheet" type="text/css" />-->
	<script src="../js/jquery.min.js"></script>
	<script src="../js/jquery.mobile-1.4.5.min.js"></script>

	<title>Test</title>
</head>
<body>
	<div data-role="page" data-theme="b" data-dom-cache="true">
		<div data-role="header" data-position="inline">
			<a href="/test/index.html" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-theme="b" data-role="button" data-icon="arrow-l" title=" Back ">
			<span class="ui-btn-inner ui-btn-corner-all">
				<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
			</span>
			</a>
			<h1 tabindex="0" role="heading" aria-level="1">test mobiscroll</h1>
		</div>
		<div role="main" class="ui-content">
			<input id="test-date-time" placeholder="select datetime...">
		</div>
	</div>
	<script src="../js/mobiscroll.dom.js"></script>
	<script src="../js/mobiscroll.core.js"></script>
	<script src="../js/mobiscroll.scrollview.js"></script>
	<script src="../js/mobiscroll.frame.js"></script>
	<!--<script src="../js/mobiscroll.frame.android-holo.js"></script>-->
	<!--<script src="../js/mobiscroll.frame.ios.js"></script>-->
	<script src="../js/mobiscroll.frame.jqm.js"></script>
	<!--<script src="../js/mobiscroll.frame.wp.js"></script>-->
	<script src="../js/mobiscroll.scroller.js"></script>
	<!--<script src="../js/mobiscroll.android-holo-light.js"></script>-->
	<!--<script src="../js/mobiscroll.wp-light.js"></script>-->
	<!--<script src="../js/mobiscroll.mobiscroll-dark.js"></script>-->
	<script src="../js/mobiscroll.i18n.zh.js"></script>
	<script>
		mobiscroll.scroller('#test-date-time', {
			theme: "jqm",
			display: "center",
			lang: "zh",
			wheels: [
				[{
					label: 'Year',
					data: ['2000', '2001', '2002', '2003', '2004', '2005', '2006', '2007', '2008', '2009',
						'2010', '2011', '2012', '2013', '2014', '2015', '2016', '2017', '2018', '2019']
				}, {
					label: 'Month',
					data: [{
						value: 0,
						display: 'January'
					}, {
						value: 1,
						display: 'February'
					}, {
						value: 2,
						display: 'March'
					}, {
						value: 3,
						display: 'April'
					}, {
						value: 4,
						display: 'May'
					}, {
						value: 5,
						display: 'June'
					}, {
						value: 6,
						display: 'July'
					}, {
						value: 7,
						display: 'August'
					}, {
						value: 8,
						display: 'September'
					}, {
						value: 9,
						display: 'October'
					}, {
						value: 10,
						display: 'November'
					}, {
						value: 11,
						display: 'December'
					}]
				}]
			]
		});
		/*var now = new Date(),
				minDate = new Date(now.getFullYear() - 10, now.getMonth(), now.getDate()),
				maxDate = new Date(now.getFullYear() + 10, now.getMonth(), now.getDate());

		mobiscroll.datetime('#test-date-time',{
			theme: 'jqm',
			display: 'bottom',
			min: minDate,
			max: maxDate
		});*/
	</script>
</body>
</html>