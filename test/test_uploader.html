<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" href="../css/jquery.mobile-1.4.5.min.css" />
		<script src="../js/jquery.min.js"></script>
		<script src="../js/jquery.mobile-1.4.5.min.js"></script>
		.
		<link rel="stylesheet" href="qunit/qunit-2.4.0.css" />
		<link rel="stylesheet" href="../css/uploader.css" />
		<link rel="stylesheet" href="../css/demo.css" />
		<title>Daniel.uy - Online Code Demos</title>
	</head>
	<body role="document">
		<div class="container demo-wrapper">
			<div class="page-header">
				<h1>Demo <small>JQuery Drag and Drop Files</small></h1>
			</div>

			<div class="row demo-columns">
				<div class="col-md-6">
					<!-- D&D Zone-->
					<div id="drag-and-drop-zone" class="uploader">
						<div>Drag &amp; Drop Images Here</div>
						<div class="or">-or-</div>
						<div class="browser">
							<label>
								<span>Click to open the file Browser</span>
								<input type="file" name="firmware" title="Click to add Files" />
							</label>
						</div>
					</div>
					<!-- /D&D Zone -->

					<!-- Debug box -->
					<div class="panel panel-default">
						<div class="panel-heading">
							<h3 class="panel-title">Debug</h3>
						</div>
						<div class="panel-body demo-panel-debug">
							<ul id="demo-debug"></ul>
						</div>
					</div>
					<!-- /Debug box -->
				</div>
				<!-- / Left column -->

				<div class="col-md-6">
					<div class="panel panel-default">
						<div class="panel-heading">
							<h3 class="panel-title">Uploads</h3>
						</div>
						<div class="panel-body demo-panel-files" id="demo-files">
							<span class="demo-note"
								>No Files have been selected/droped yet...</span
							>
						</div>
					</div>
				</div>
				<!-- / Right column -->
			</div>

			<div class="alert alert-info">
				Read the Blog Article and Source code for this Example here:
				<a href="#"
					>http://blog.daniel.com.uy/2014/01/31/jquery-drag-and-drop-progress-files.html</a
				>
			</div>

			<div class="demo-footer">
				<p>&copy; Daniel Morales 2014</p>
			</div>
		</div>

		<script type="text/javascript" src="../js/demo.min.js"></script>
		<script type="text/javascript" src="../js/dmuploader.min.js"></script>
		<script type="text/javascript">
			$("#drag-and-drop-zone").dmUploader({
				url: "/firmware",
				dataType: "json",
				allowedTypes: "*",
				maxFileSize: 50 * 1024 * 1024,
				maxFiles: 1,
				extFilter: "pkt",
				onInit: function () {
					$.danidemo.addLog(
						"#demo-debug",
						"default",
						"Plugin initialized correctly"
					);
				},
				onBeforeUpload: function (id) {
					$.danidemo.addLog(
						"#demo-debug",
						"default",
						"Starting the upload of #" + id
					);

					$.danidemo.updateFileStatus(id, "default", "Uploading...");
				},
				onNewFile: function (id, file) {
					$.danidemo.addFile("#demo-files", id, file);
				},
				onComplete: function () {
					$.danidemo.addLog(
						"#demo-debug",
						"default",
						"All pending tranfers completed"
					);
				},
				onUploadProgress: function (id, percent) {
					var percentStr = percent + "%";

					$.danidemo.updateFileProgress(id, percentStr);
				},
				onUploadSuccess: function (id, data) {
					$.danidemo.addLog(
						"#demo-debug",
						"success",
						"Upload of file #" + id + " completed"
					);

					$.danidemo.addLog(
						"#demo-debug",
						"info",
						"Server Response for file #" + id + ": " + JSON.stringify(data)
					);

					$.danidemo.updateFileStatus(id, "success", "Upload Complete");

					$.danidemo.updateFileProgress(id, "100%");
				},
				onUploadError: function (id, message) {
					$.danidemo.updateFileStatus(id, "error", message);

					$.danidemo.addLog(
						"#demo-debug",
						"error",
						"Failed to Upload file #" + id + ": " + message
					);
				},
				onFileTypeError: function (file) {
					$.danidemo.addLog(
						"#demo-debug",
						"error",
						"File '" + file.name + "' cannot be added: must be an image"
					);
				},
				onFileSizeError: function (file) {
					$.danidemo.addLog(
						"#demo-debug",
						"error",
						"File '" + file.name + "' cannot be added: size excess limit"
					);
				},
				onFileExtError: function (file) {
					$.danidemo.addLog(
						"#demo-debug",
						"error",
						"File '" + file.name + "' has a Not Allowed Extension"
					);
				},
				onFallbackMode: function (message) {
					$.danidemo.addLog(
						"#demo-debug",
						"info",
						"Browser not supported(do something else here!): " + message
					);
				},
			});
		</script>
	</body>
</html>
