﻿<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="stylesheet" href="../css/jquery.mobile-1.4.5.min.css">
	<link href="../css/mobiscroll.min.css" rel="stylesheet" type="text/css" />
	<script src="../js/jquery.min.js"></script>
	<script src="../js/jquery.mobile-1.4.5.min.js"></script>
	<script src="../js/mobiscroll.js"></script>
	<title>Test</title>
</head>
<body>
	<div data-role="page" data-theme="b" data-dom-cache="true">
		<div data-role="header" data-position="inline">
			<a href="/test/index.html" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-theme="b" data-role="button" data-icon="arrow-l" title=" Back ">
			<span class="ui-btn-inner ui-btn-corner-all">
				<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
			</span>
			</a>
			<h1 tabindex="0" role="heading" aria-level="1">test mobiscroll</h1>
		</div>
		<div role="main" class="ui-content">
			<input type="text" id="text" placeholder="select datetime...">
		</div>
	</div>
	<script>
		$(function () {
			var newjavascript={
				plugdatetime:function ($dateTxt,type) {
					var opt = {};
					opt.time = {preset : type};
					opt.date = {preset : type};
					opt.datetime = {
						preset : type,
						minDate: new Date(2000,1,01,00,00),
						maxDate: new Date(2020,12,31,24,59),
						stepMinute: 1
					};
					$dateTxt.val('').scroller('destroy').scroller(
							$.extend(opt[type],
									{
										theme: "sense-ui",
										mode: "scroller",
										display: "modal",
										lang: "english",
										monthText: "月",
										dayText: "日",
										yearText: "年",
										hourText: "时",
										minuteText: "分",
										ampmText:"上午/下午",
										setText: '确定',
										cancelText: '取消',
										dateFormat: 'yy-mm-dd'
									}
							)
					);
				}
			};
			newjavascript.plugdatetime($("#text"), "datetime")
		});
	</script>
</body>
</html>