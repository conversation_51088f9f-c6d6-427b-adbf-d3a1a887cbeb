<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>QUnit Test</title>
	<link rel="stylesheet" href="../css/jquery.mobile-1.4.5.min.css">
	<link rel="stylesheet" href="./qunit/qunit-2.4.0.css">
	<style>
		.ui-content .ui-grid-b .ui-block-a {width: 45%}
		.ui-content .ui-grid-b .ui-block-b {width: 45%}
		.ui-content .ui-grid-b .ui-block-c {width: 10%}
		#popup-modify-pwd form div {padding: 10px 20px;}
		#videoChannelConfig {display: none;}
		#networkConfig {display: none;}
		#recordingSettings {display: none;}
		#eventConfig {display: none;}
		#tab-chn-2 {display: none;}
		#tab-chn-3 {display: none;}
		#tab-chn-4 {display: none;}
		#tab-pin-2 {display: none;}
		#tab-pin-3 {display: none;}
		#tab-pin-4 {display: none;}
		#tab-pin-5 {display: none;}
		#tab-pin-6 {display: none;}
		#tab-pin-7 {display: none;}
		#tab-pin-8 {display: none;}
		#goto-popup {display: none;}
		#popup {max-width:400px;}
		.hide-anchor {display: none;}

		.ui-icon-global:after {
			background-image: url("../css/images/icons-png/global-white.png");
			background-size: 14px 14px;
		}
		.ui-icon-slider:after {
			background-image: url("../css/images/icons-png/slider-white.png");
			background-size: 14px 14px;
		}
	</style>
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>
<div data-role="header" data-position="fixed">
	<a href="index.html" data-ajax="false" class="ui-btn-left" data-iconpos="notext" data-theme="b" data-role="button" data-icon="back" title=" Home ">
		<span class="ui-btn-inner ui-btn-corner-all">
			<span data-form="ui-icon" class="ui-icon ui-icon-home"></span>
		</span>
	</a>
	<h1 class="nav-header" tabindex="0" role="heading" aria-level="1" data-desc="system-info">系统信息</h1>
	<a class="btn-refresh" data-iconpos="notext" data-theme="b" data-role="button" data-icon="refresh" data-btn-title="refresh-configs">
		<span class="ui-btn-inner ui-btn-corner-all">
			<span data-form="ui-icon" class="ui-icon ui-icon-refresh"></span>
		</span>
	</a>
</div>
<div data-role="navbar">
	<ul>
		<li><a href="#" data-icon="info" class="btn-nav ui-btn-active ui-state-persist" data-goto-id="dvrIdentification" data-desc="system-info">系统信息</a></li>
		<li><a href="#" data-icon="slider" class="btn-nav" data-goto-id="videoChannelConfig" data-desc="channel-conf">通道参数</a></li>
		<li><a href="#" data-icon="global" class="btn-nav" data-goto-id="networkConfig" data-desc="network-conf">网络配置</a></li>
		<li><a href="#" data-icon="video" class="btn-nav" data-goto-id="recordingSettings" data-desc="record-conf">录像配置</a></li>
		<li><a href="#" data-icon="alert" class="btn-nav" data-goto-id="eventConfig" data-desc="alarm-conf">报警配置</a></li>
	</ul>
</div>
<div id="main" role="main" class="ui-content"></div>
<div data-role="footer" class="ui-btn" data-position="fixed" data-fullscreen="false">
	<a href="#" id="btn-cancel" data-role="button" data-theme="b" data-icon="delete" data-desc="cancel">取消</a>
	<a href="#" id="btn-submit" data-role="button" data-theme="b" data-icon="check" data-desc="confirm">确定</a>
</div>
<a id="show-info" href="#popup-show-info" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
<div data-role="popup" id="popup-show-info" data-overlay-theme="b" data-theme="b" data-dismissible="false">
	<div data-role="header">
		<h1></h1>
	</div>
	<div role="main" class="ui-content">
		<h3></h3>
		<p></p>
		<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
	</div>
</div>
<a id="ask-for-sure" href="#popup-ask-for-sure" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
<div data-role="popup" id="popup-ask-for-sure" data-overlay-theme="b" data-theme="b" data-dismissible="false" data-popup-title="">
	<div data-role="header">
		<h1></h1>
	</div>
	<div role="main" class="ui-content">
		<h3></h3>
		<p></p>
		<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-desc="cancel">取消</a>
		<a id="btn-ask-for-sure" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
	</div>
</div>
<a id="form-input2" href="#popup-form-input2" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
<div data-role="popup" id="popup-form-input2" data-overlay-theme="b" data-theme="b" data-dismissible="false" data-popup-title="">
	<div data-role="header">
		<h1></h1>
	</div>
	<div role="main" class="ui-content">
		<h3></h3>
		<form>
			<div>
				<label for="form-input2-1" class="ui-hidden-accessible"></label>
				<input id="form-input2-1" value="" placeholder="" type="text">
				<label for="form-input2-2" class="ui-hidden-accessible"></label>
				<input id="form-input2-2" value="" placeholder="" type="text">
				<p class="form-input-note">&nbsp;</p>
				<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-desc="cancel">取消</a>
				<a id="btn-form-input2" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-desc="confirm">确定</a>
			</div>
		</form>
	</div>
</div>
<a id="form-input3" href="#popup-form-input3" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
<div data-role="popup" id="popup-form-input3" data-overlay-theme="b" data-theme="b" data-dismissible="false" data-popup-title="">
	<div data-role="header">
		<h1></h1>
	</div>
	<div role="main" class="ui-content">
		<h3></h3>
		<form>
			<div>
				<label for="form-input3-1" class="ui-hidden-accessible"></label>
				<input id="form-input3-1" value="" placeholder="" type="text">
				<label for="form-input3-2" class="ui-hidden-accessible"></label>
				<input id="form-input3-2" value="" placeholder="" type="text">
				<label for="form-input3-3" class="ui-hidden-accessible"></label>
				<input id="form-input3-3" value="" placeholder="" type="text">
				<p class="form-input-note">&nbsp;</p>
				<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-desc="cancel">取消</a>
				<a id="btn-form-input3" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-desc="confirm">确定</a>
			</div>
		</form>
	</div>
</div>
<script id="ssid-list-template" type="text/x-handlebars-template">
	<ul data-role="listview" data-split-icon="delete" data-inset="true">
		<li data-role="list-divider">SSID<i>/Password</i><span data-desc="list">列表</span></li>
		<li><a href="#" class="btn-popup" data-popup-type="form-input2" data-popup-title="edit-ssid" data-list-index="0"><img src="../css/images/icons-png/edit-white.png" alt="Edit" class="ui-li-icon ui-corner-all"><h2 id="networkConfig-wifi-ssid">{{ssid}}</h2><i id="networkConfig-wifi-password">{{password}}</i></a><a href="#" class="btn-popup" data-popup-type="ask-for-sure" data-popup-title="delete-ssid" data-list-index="0"></a></li>
		{{#each extraWifiList}}
		<li><a href="#" class="btn-popup" data-popup-type="form-input2" data-popup-title="edit-ssid" data-list-index="{{addOne @index}}"><img src="../css/images/icons-png/edit-white.png" alt="Edit" class="ui-li-icon ui-corner-all"><h2 id="networkConfig-wifi-extraWifiList-{{@index}}-ssid">{{ssid}}</h2><i id="networkConfig-wifi-extraWifiList-{{@index}}-password">{{password}}</i></a><a href="#" class="btn-popup" data-popup-type="ask-for-sure" data-popup-title="delete-ssid"  data-list-index="{{addOne @index}}"></a></li>
		{{/each}}
		<li data-icon="plus"><a href="#" class="btn-popup" data-popup-type="form-input2" data-popup-title="add-ssid">&nbsp;</a></li>
	</ul>
</script>
<script id="configs-template" type="text/x-handlebars-template">
	<div id="dvrIdentification" class="tab-configs">
		<div data-role="fieldcontain">
			<label for="dvrIdentification-cid" data-desc="cid">CID</label>
			<input type="text" id="dvrIdentification-cid" value="{{dvrIdentification.cid}}">
		</div>
		<div data-role="fieldcontain">
			<label for="dvrIdentification-vehicleNum" data-desc="vehicle-num">Vehicle Number</label>
			<input type="text" id="dvrIdentification-vehicleNum" value="{{dvrIdentification.vehicleNum}}">
		</div>
		<div data-role="fieldcontain">
			<label for="dvrIdentification-serialNo" data-desc="serial-num">Serial Number</label>
			<input type="text" id="dvrIdentification-serialNo" disabled="true" value="{{dvrIdentification.serialNo}}">
		</div>
		<div data-role="fieldcontain">
			<label for="dvrIdentification-version" data-desc="sw-version">Software Version</label>
			<input type="text" id="dvrIdentification-version" disabled="true" value="{{dvrIdentification.version}}">
		</div>
		<div data-role="fieldcontain">
			<label for="dvrIdentification-svNum" data-desc="sub-version">Sub Version</label>
			<input type="text" id="dvrIdentification-svNum" disabled="true" value="{{dvrIdentification.svNum}}">
		</div>
		<div data-role="fieldcontain">
			<label for="dvrIdentification-machineType" data-desc="mach-type">Machine Type</label>
			<input type="text" id="dvrIdentification-machineType" disabled="true" value="{{transMachine dvrIdentification.machineType}}">
		</div>
		<div data-role="fieldcontain">
			<label for="dvrIdentification-mcuId" data-desc="mcu-id">MCU ID</label>
			<input type="text" id="dvrIdentification-mcuId" disabled="true" value="{{dvrIdentification.mcuId}}">
		</div>
		<div data-role="fieldcontain">
			<label for="save-conf" data-desc="save-conf">保存配置</label>
			<select id="save-conf" data-role="slider">
				<option class="save-sel" value="off" data-desc="no">否</option>
				<option class="save-sel" value="on" data-desc="yes">是</option>
			</select>
		</div>
		<a href="#" data-rel="popup" data-position-to="window" class="btn-popup ui-btn ui-corner-all ui-shadow" data-popup-type="ask-for-sure" data-popup-title="restore" data-desc="restore">恢复出厂</a>
		<a href="#" data-rel="popup" data-position-to="window" class="btn-popup ui-btn ui-corner-all ui-shadow" data-popup-type="form-input3" data-popup-title="change-pwd" data-desc="change-pwd">修改密码</a>
	</div>

	<div id="videoChannelConfig" class="tab-configs">
		<fieldset data-role="controlgroup" data-type="horizontal">
			<input name="sel-chn" id="sel-ch1" class="sel-chn" value="ch1" checked="checked" type="radio" data-tabchn-id="tab-chn-1">
			<label for="sel-ch1" data-desc="ch1">通道1</label>
			<input name="sel-chn" id="sel-ch2" class="sel-chn" value="ch2" type="radio" data-tabchn-id="tab-chn-2">
			<label for="sel-ch2" data-desc="ch2">通道2</label>
			<input name="sel-chn" id="sel-ch3" class="sel-chn" value="ch3" type="radio" data-tabchn-id="tab-chn-3">
			<label for="sel-ch3" data-desc="ch3">通道3</label>
			<input name="sel-chn" id="sel-ch4" class="sel-chn" value="ch4" type="radio" data-tabchn-id="tab-chn-4">
			<label for="sel-ch4" data-desc="ch4">通道4</label>
		</fieldset>

		{{#each videoChannelConfig}}
		<div id="tab-chn-{{channelId}}" class="tab-chn-conf">
			<input type="text" id="videoChannelConfig-{{@index}}-channelId" value="{{channelId}}" disabled="disabled" style="display: none;">
			<div data-role="fieldcontain">
				<label for="videoChannelConfig-{{@index}}-resolution" data-desc="resolution">分辨率</label>
				<select id="videoChannelConfig-{{@index}}-resolution">
					<option value="CIF" {{#equal resolution "CIF"}}selected="selected"{{/equal}}>CIF</option>
					<option value="D1" {{#equal resolution "D1"}}selected="selected"{{/equal}}>D1</option>
					<option value="720P" {{#equal resolution "720P"}}selected="selected"{{/equal}}>720P</option>
					<option value="1080P" {{#equal resolution "1080P"}}selected="selected"{{/equal}}>1080P</option>
				</select>
			</div>
			<div data-role="fieldcontain">
				<label for="videoChannelConfig-{{@index}}-framerate" data-desc="framerate">帧率</label>
				<input type="text" id="videoChannelConfig-{{@index}}-framerate" value="{{framerate}}">
			</div>
			<div data-role="fieldcontain">
				<label for="videoChannelConfig-{{@index}}-bitrate" data-desc="bitrate">码率</label>
				<input type="text" id="videoChannelConfig-{{@index}}-bitrate" value="{{bitrate}}">
			</div>
			<div data-role="fieldcontain">
				<label for="videoChannelConfig-{{@index}}-rcMode" data-desc="rc-mode">码率控制模式</label>
				<select id="videoChannelConfig-{{@index}}-rcMode">
					<option value="0" {{#equal rcMode 0}}selected="selected"{{/equal}}>VBR</option>
					<option value="1" {{#equal rcMode 1}}selected="selected"{{/equal}}>CBR</option>
					<option value="2" {{#equal rcMode 2}}selected="selected"{{/equal}}>ABR</option>
					<option value="3" {{#equal rcMode 3}}selected="selected"{{/equal}}>FIXQP</option>
				</select>
			</div>
			<div data-role="fieldcontain">
				<label for="videoChannelConfig-{{@index}}-maxQp" data-desc="max-qp">最大Qp值</label>
				<input type="range" id="videoChannelConfig-{{@index}}-maxQp" value="{{maxQp}}" min="-1" max="50" data-highlight="true">
			</div>
			<div data-role="fieldcontain">
				<label for="videoChannelConfig-{{@index}}-minQp" data-desc="min-qp">最小Qp值</label>
				<input type="range" id="videoChannelConfig-{{@index}}-minQp" value="{{minQp}}" min="-1" max="50" data-highlight="true"  />
			</div>
			<div data-role="fieldcontain">
				<label for="videoChannelConfig-{{@index}}-fixQp" data-desc="fix-qp">固定Qp值</label>
				<input type="range" id="videoChannelConfig-{{@index}}-fixQp" value="{{fixQp}}" min="0" max="50" data-highlight="true"  />
			</div>
			<div data-role="fieldcontain">
				<fieldset data-role="controlgroup">
					<legend data-desc="extra-attribute">额外属性</legend>
					<label for="videoChannelConfig-{{@index}}-OSDEnabled" data-desc="osd-enable">OSD使能</label>
					<input type="checkbox" id="videoChannelConfig-{{@index}}-OSDEnabled" class="custom" {{#if OSDEnabled}}checked="checked"{{/if}}>
					<label for="videoChannelConfig-{{@index}}-OSDMirrored" data-desc="osd-mirror">OSD镜像</label>
					<input type="checkbox" id="videoChannelConfig-{{@index}}-OSDMirrored" class="custom" {{#if OSDMirrored}}checked="checked"{{/if}}>
					<label for="videoChannelConfig-{{@index}}-audioEnabled" data-desc="audio-enable">音频使能</label>
					<input type="checkbox" id="videoChannelConfig-{{@index}}-audioEnabled" class="custom" {{#if audioEnabled}}checked="checked"{{/if}}>
					<label for="videoChannelConfig-{{@index}}-mirrorEnable" data-desc="mirror-enable">画面镜像</label>
					<input type="checkbox" id="videoChannelConfig-{{@index}}-mirrorEnable" class="custom" {{#if mirrorEnable}}checked="checked"{{/if}}>
					<label for="videoChannelConfig-{{@index}}-flipEnable" data-desc="flip-enable">画面翻转</label>
					<input type="checkbox" id="videoChannelConfig-{{@index}}-flipEnable" {{#if flipEnable}}checked="checked"{{/if}}>
				</fieldset>
			</div>
		</div>
		{{/each}}
	</div>
	<div id="networkConfig" class="tab-configs">
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="ethernet">Ethernet</h1>
			<div data-role="fieldcontain">
				<label for="networkConfig-ethernet-ipAddress" data-desc="ipaddr">IP Address</label>
				<input type="text" id="networkConfig-ethernet-ipAddress" value="{{networkConfig.ethernet.ipAddress}}">
			</div>
			<div data-role="fieldcontain">
				<label for="networkConfig-ethernet-macAddress" data-desc="mac-address">MAC Address</label>
				<input type="text" id="networkConfig-ethernet-macAddress" value="{{networkConfig.ethernet.macAddress}}">
			</div>
			<div data-role="fieldcontain">
				<label for="networkConfig-ethernet-subnetMask" data-desc="subnet-mask">Subnet Mask</label>
				<input type="text" id="networkConfig-ethernet-subnetMask" value="{{networkConfig.ethernet.subnetMask}}">
			</div>
			<div data-role="fieldcontain">
				<label for="networkConfig-ethernet-dnsServer" data-desc="dns-server">DNS Server</label>
				<input type="text" id="networkConfig-ethernet-dnsServer" value="{{networkConfig.ethernet.dnsServer}}">
			</div>
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1>WIFI</h1>
			<fieldset data-role="controlgroup">
				<label for="networkConfig-wifi-enable" data-desc="enable">Enable</label>
				<input type="checkbox" id="networkConfig-wifi-enable" class="custom" {{#if networkConfig.wifi.enable}}checked="checked"{{/if}}>
			</fieldset>
			<div data-role="fieldcontain">
				<label for="networkConfig-wifi-authmode" data-desc="auth-mode">Auth Mode</label>
				<select id="networkConfig-wifi-authmode">
					<option value="OPEN" {{#equal networkConfig.wifi.authmode "OPEN"}}selected="selected"{{/equal}}>OPEN</option>
					<option value="SHARED" {{#equal networkConfig.wifi.authmode "SHARED"}}selected="selected"{{/equal}}>SHARED</option>
					<option value="WPAPSK" {{#equal networkConfig.wifi.authmode "WPAPSK"}}selected="selected"{{/equal}}>WPAPSK</option>
					<option value="WPA2PSK" {{#equal networkConfig.wifi.authmode "WPA2PSK"}}selected="selected"{{/equal}}>WPA2PSK</option>
					<option value="AUTO" {{#equal networkConfig.wifi.authmode "AUTO"}}selected="selected"{{/equal}}>AUTO</option>
				</select>
			</div>
			<div data-role="fieldcontain">
				<label for="networkConfig-wifi-encryption" data-desc="encryption">Encryption</label>
				<select id="networkConfig-wifi-encryption">
					<option value="NONE" {{#equal networkConfig.wifi.encryption "NONE"}}selected="selected"{{/equal}}>NONE</option>
					<option value="WEP" {{#equal networkConfig.wifi.encryption "WEP"}}selected="selected"{{/equal}}>WEP</option>
					<option value="AES" {{#equal networkConfig.wifi.encryption "AES"}}selected="selected"{{/equal}}>AES</option>
					<option value="TKIP" {{#equal networkConfig.wifi.encryption "TKIP"}}selected="selected"{{/equal}}>TKIP</option>
					<option value="AUTO" {{#equal networkConfig.wifi.encryption "AUTO"}}selected="selected"{{/equal}}>AUTO</option>
				</select>
			</div>
			<div id="ssid-list"></div>

		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="cellular">Cellular</h1>
			<fieldset data-role="controlgroup">
				<label for="networkConfig-cellular-enabled" data-desc="enable">Enable</label>
				<input type="checkbox" id="networkConfig-cellular-enabled" class="custom" {{#if networkConfig.cellular.enabled}}checked="checked"{{/if}}>
			</fieldset>
			<div data-role="fieldcontain">
				<label for="networkConfig-cellular-apn">APN</label>
				<input type="text" id="networkConfig-cellular-apn" value="{{networkConfig.cellular.apn}}">
			</div>
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="management-server">Management Server</h1>
			<div data-role="fieldcontain">
				<label for="managementServerConfig-url" data-desc="url">URL</label>
				<input type="text" id="managementServerConfig-url" value="{{managementServerConfig.url}}">
			</div>
			<div data-role="fieldcontain">
				<label for="managementServerConfig-user" data-desc="user">user:</label>
				<input type="text" id="managementServerConfig-user" value="{{managementServerConfig.user}}">
			</div>
			<div data-role="fieldcontain">
				<label for="managementServerConfig-password" data-desc="password">Password</label>
				<input type="password" id="managementServerConfig-password" value="{{managementServerConfig.password}}">
			</div>
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="network-disk">Network Disk Server</h1>
			<div data-role="fieldcontain">
				<label for="mediaDeliveryConfig-url" data-desc="url">URL</label>
				<input type="text" id="mediaDeliveryConfig-url" value="{{mediaDeliveryConfig.url}}">
			</div>
			<div data-role="fieldcontain">
				<label for="mediaDeliveryConfig-accessKey" data-desc="access-key">s3AccessKey</label>
				<input type="password" id="mediaDeliveryConfig-accessKey" value="{{mediaDeliveryConfig.accessKey}}">
			</div>
			<div data-role="fieldcontain">
				<label for="mediaDeliveryConfig-secretKey" data-desc="secret-key">s3SecretKey</label>
				<input type="password" id="mediaDeliveryConfig-secretKey" value="{{mediaDeliveryConfig.secretKey}}">
			</div>
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="time-sync-config">Time Sync Config</h1>
			<div data-role="fieldcontain">
				<label for="timeConfig-ntpServer" data-desc="ntp-server">NTP Server</label>
				<input type="text" id="timeConfig-ntpServer" value="{{timeConfig.ntpServer}}">
			</div>
			<div data-role="fieldcontain">
				<label for="timeConfig-ntpIntervalMinutes" data-desc="ntp-interval-min">NTP Interval(min)</label>
				<input type="text" id="timeConfig-ntpIntervalMinutes" value="{{timeConfig.ntpIntervalMinutes}}">
			</div>
			<fieldset data-role="controlgroup">
				<label for="timeConfig-gpsSyncBackup" data-desc="gps-sync-backup">GPS Sync Backup</label>
				<input type="checkbox" id="timeConfig-gpsSyncBackup" class="custom" value="{{timeConfig.gpsSyncBackup}}" {{#if timeConfig.gpsSyncBackup}}checked="checked"{{/if}}>
			</fieldset>
		</div>
	</div>
	<div id="recordingSettings" class="tab-configs">
		<div data-role="fieldcontain">
			<label for="recordingSettings-loopFileMinutes" data-desc="loop-file-min">Loop-Rec File(min)</label>
			<input type="text" id="recordingSettings-loopFileMinutes" value="{{recordingSettings.loopFileMinutes}}">
		</div>
		<div data-role="fieldcontain">
			<label for="recordingSettings-continueLoopWaterLevel" data-desc="loop-water-level">Loop Water Level</label>
			<input type="text" id="recordingSettings-continueLoopWaterLevel" value="{{recordingSettings.continueLoopWaterLevel}}">
		</div>
		<fieldset data-role="controlgroup">
			<legend data-desc="storage-attr">Storage Attribute</legend>
			<label for="recordingSettings-sdLockIgnore" data-desc="sd-lock-ignore">SD Lock Ignore</label>
			<input type="checkbox" id="recordingSettings-sdLockIgnore" class="custom" {{#if recordingSettings.sdLockIgnore}}checked="checked"{{/if}}>
			<label for="recordingSettings-backupVideoEnable" data-desc="backup-video">Backup Video Enable</label>
			<input type="checkbox" id="recordingSettings-backupVideoEnable" class="custom" {{#if recordingSettings.backupVideoEnable}}checked="checked"{{/if}}>
			<label for="recordingSettings-diffCardBackRec" data-desc="diff-card-back-rec">Rec&Backup In Diff Card</label>
			<input type="checkbox" id="recordingSettings-diffCardBackRec" class="custom" {{#if recordingSettings.diffCardBackRec}}checked="checked"{{/if}}>
		</fieldset>
		<div data-role="fieldcontain">
			<fieldset data-role="controlgroup" data-type="horizontal">
				<legend data-desc="rec-channel-enable">Record Channel Enable</legend>
				{{#each recordingSettings.recEnableArray}}
				<label for="recordingSettings-recEnableArray-{{@index}}">CH{{addOne @index}}</label>
				<input type="checkbox" id="recordingSettings-recEnableArray-{{@index}}" class="custom" {{#if this}}checked="checked"{{/if}}>
				{{/each}}
			</fieldset>
		</div>
		<div data-role="fieldcontain">
			<fieldset data-role="controlgroup" data-type="horizontal">
				<legend data-desc="jpeg-channel-enable">JPEG Channel Enable</legend>
				{{#each recordingSettings.jpegEnableArray}}
				<label for="recordingSettings-jpegEnableArray-{{@index}}">CH{{addOne @index}}</label>
				<input type="checkbox" id="recordingSettings-jpegEnableArray-{{@index}}" class="custom" {{#if this}}checked="checked"{{/if}}>
				{{/each}}
			</fieldset>
		</div>
		<div data-role="fieldcontain">
			<fieldset data-role="controlgroup" data-type="horizontal">
				<legend data-desc="storage-devices-enable">Storage Devices Enable</legend>
				{{#each recordingSettings.storageEnableArray}}
				<label for="recordingSettings-storageEnableArray-{{@index}}">{{#lessThan @index 4}}SD{{addOne @index}}{{else}}USB{{/lessThan}}</label>
				<input type="checkbox" id="recordingSettings-storageEnableArray-{{@index}}" class="custom" {{#if this}}checked="checked"{{/if}}>
				{{/each}}
			</fieldset>
		</div>
		<div data-role="fieldcontain">
			<label for="recordingSettings-eventBufferPct" data-desc="event-buffer-pct">Event Buffer Percent</label>
			<input type="text" id="recordingSettings-eventBufferPct" value="{{recordingSettings.eventBufferPct}}">
		</div>
		<div data-role="fieldcontain">
			<label for="recordingSettings-clipGenerating_Limit" data-desc="evnet-meantime-limit">Event Meantime Limit(s)</label>
			<input type="text" id="recordingSettings-clipGenerating_Limit" value="{{recordingSettings.clipGenerating_Limit}}">
		</div>
		<div data-role="fieldcontain">
			<label for="recordingSettings-maxClipNum" data-desc="max-evnet-clip-num">Max-Event-Clip Number</label>
			<input type="text" id="recordingSettings-maxClipNum" value="{{recordingSettings.maxClipNum}}">
		</div>
	</div>
	<div id="eventConfig" class="tab-configs">
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="evnet-config">Event Config</h1>
			<fieldset data-role="controlgroup">
				<label for="eventConfig-convertEnable" data-desc="video-convert-enable">Vedio Convert Enable</label>
				<input type="checkbox" id="eventConfig-convertEnable" class="custom" {{#if eventConfig.convertEnable}}checked="checked"{{/if}}>
				<label for="eventConfig-audioEnable" data-desc="clip-audio-enable">Audio Clip Enable</label>
				<input type="checkbox" id="eventConfig-audioEnable" class="custom" {{#if eventConfig.audioEnable}}checked="checked"{{/if}}>
			</fieldset>
			<div data-role="fieldcontain">
				<label for="eventConfig-bitrate" data-desc="conv-bitrate">Convert Bitrate</label>
				<input type="text" id="eventConfig-bitrate" value="{{eventConfig.bitrate}}">
			</div>
			<div data-role="fieldcontain">
				<label for="eventConfig-resolution" data-desc="conv-resolution">Convert Resolution</label>
				<select id="eventConfig-resolution">
					<option value="0" {{#equal eventConfig.resolution 0}}selected="selected"{{/equal}}>QCIF</option>
					<option value="1" {{#equal eventConfig.resolution 1}}selected="selected"{{/equal}}>CIF</option>
					<option value="2" {{#equal eventConfig.resolution 2}}selected="selected"{{/equal}}>D1</option>
					<option value="3" {{#equal eventConfig.resolution 3}}selected="selected"{{/equal}}>720P</option>
					<option value="4" {{#equal eventConfig.resolution 4}}selected="selected"{{/equal}}>1080P</option>
				</select>
			</div>
			<div data-role="fieldcontain">
				<label for="eventConfig-preSeconds" data-desc="previous">Previous(s)</label>
				<input type="text" id="eventConfig-preSeconds" value="{{eventConfig.preSeconds}}">
			</div>
			<div data-role="fieldcontain">
				<label for="eventConfig-postSeconds" data-desc="postpone">Postpone(s)</label>
				<input type="text" id="eventConfig-postSeconds" value="{{eventConfig.postSeconds}}">
			</div>
			<div data-role="fieldcontain">
				<fieldset data-role="controlgroup" data-type="horizontal">
					<legend data-desc="event-channel-enable">Event Channel Enable</legend>
					<input type="checkbox" id="eventConfig-channel1Enable" class="custom" {{#if eventConfig.channel1Enable}}checked="checked"{{/if}}>
					<label for="eventConfig-channel1Enable">CH1</label>
					<input type="checkbox" id="eventConfig-channel2Enable" class="custom" {{#if eventConfig.channel2Enable}}checked="checked"{{/if}}>
					<label for="eventConfig-channel2Enable">CH2</label>
					<input type="checkbox" id="eventConfig-channel3Enable" class="custom" {{#if eventConfig.channel3Enable}}checked="checked"{{/if}}>
					<label for="eventConfig-channel3Enable">CH3</label>
					<input type="checkbox" id="eventConfig-channel4Enable" class="custom" {{#if eventConfig.channel4Enable}}checked="checked"{{/if}}>
					<label for="eventConfig-channel4Enable">CH4</label>
				</fieldset>
			</div>
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="gpio-config">GPIO Config</h1>
			<fieldset data-role="controlgroup" data-type="horizontal">
				<input id="sel-pin1" name="sel-pin" class="sel-pin" checked="checked" type="radio" data-tabpin-id="tab-pin-1">
				<label for="sel-pin1">PIN1</label>
				<input id="sel-pin2" name="sel-pin" class="sel-pin" type="radio" data-tabpin-id="tab-pin-2">
				<label for="sel-pin2">PIN2</label>
				<input id="sel-pin3" name="sel-pin" class="sel-pin" type="radio" data-tabpin-id="tab-pin-3">
				<label for="sel-pin3">PIN3</label>
				<input id="sel-pin4" name="sel-pin" class="sel-pin" type="radio" data-tabpin-id="tab-pin-4">
				<label for="sel-pin4">PIN4</label>
				<input id="sel-pin5" name="sel-pin" class="sel-pin" type="radio" data-tabpin-id="tab-pin-5">
				<label for="sel-pin5">PIN5</label>
				<input id="sel-pin6" name="sel-pin" class="sel-pin" type="radio" data-tabpin-id="tab-pin-6">
				<label for="sel-pin6">PIN6</label>
				<input id="sel-pin7" name="sel-pin" class="sel-pin" type="radio" data-tabpin-id="tab-pin-7">
				<label for="sel-pin7">PIN7</label>
				<input id="sel-pin8" name="sel-pin" class="sel-pin" type="radio" data-tabpin-id="tab-pin-8">
				<label for="sel-pin8">PIN8</label>
			</fieldset>

			{{#each gpioConfig}}
			<div id="tab-pin-{{addOne @index}}" class="tab-pin-conf">
				<label for="gpioConfig-{{@index}}-eventTriggerEnable" data-desc="event-trigger-enable">Event Trigger Enable</label>
				<input id="gpioConfig-{{@index}}-eventTriggerEnable" class="custom" type="checkbox" {{#if eventTriggerEnable}}checked="checked"{{/if}}>
				<label for="gpioConfig-{{@index}}-bindAoutEn" data-desc="audio-output-enable">Audio Output Enable</label>
				<input id="gpioConfig-{{@index}}-bindAoutEn" class="custom" type="checkbox" {{#if bindAoutEn}}checked="checked"{{/if}}>
				<fieldset data-role="controlgroup" data-type="horizontal">
					<legend data-desc="video-output-chn">Video Output Channel</legend>
					<input id="gpioConfig-{{@index}}-bindVout-0" type="checkbox" {{#isBitSet bindVout 0}}checked="checked"{{/isBitSet}}>
					<label for="gpioConfig-{{@index}}-bindVout-0">CH1</label>
					<input id="gpioConfig-{{@index}}-bindVout-1" type="checkbox" {{#isBitSet bindVout 1}}checked="checked"{{/isBitSet}}>
					<label for="gpioConfig-{{@index}}-bindVout-1">CH2</label>
					<input id="gpioConfig-{{@index}}-bindVout-2" type="checkbox" {{#isBitSet bindVout 2}}checked="checked"{{/isBitSet}}>
					<label for="gpioConfig-{{@index}}-bindVout-2">CH3</label>
					<input id="gpioConfig-{{@index}}-bindVout-3" type="checkbox" {{#isBitSet bindVout 3}}checked="checked"{{/isBitSet}}>
					<label for="gpioConfig-{{@index}}-bindVout-3">CH4</label>
				</fieldset>
				<label for="gpioConfig-{{@index}}-triggerDelayTime" data-desc="trigger-delay-time">Trigger Delay(s)</label>
				<input type="range" id="gpioConfig-{{@index}}-triggerDelayTime" value="{{triggerDelayTime}}" min="1" max="20" data-highlight="true">
				<label for="gpioConfig-{{@index}}-bindVoutPriority" data-desc="bind-vout-priority">Video Output Priority</label>
				<input type="range" id="gpioConfig-{{@index}}-bindVoutPriority" value="{{bindVoutPriority}}" min="1" max="20" data-highlight="true">
			</div>
			{{/each}}
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="acc-config">ACC Config</h1>
			<div data-role="fieldcontain">
				<label for="accConfig-delaySec" data-desc="delay-shutdonw">Delay Shutdown(S)</label>
				<input type="text" id="accConfig-delaySec" value="{{accConfig.delaySec}}">
			</div>
			<div data-role="fieldcontain">
				<label for="accConfig-poweroffVoltage" data-desc="poweroff-voltage">Poweroff Voltage</label>
				<input type="text" id="accConfig-poweroffVoltage" value="{{accConfig.poweroffVoltage}}">
			</div>
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="rtc-calibration">RTC Calibration</h1>
			<fieldset data-role="controlgroup">
				<label for="rtcRepair-autoCalibrationEn" data-desc="auto-calibration">Auto Calibration Enable</label>
				<input type="checkbox" id="rtcRepair-autoCalibrationEn" {{#if rtcRepair.autoCalibrationEn}}checked="checked"{{/if}}>
			</fieldset>
			<div data-role="fieldcontain">
				<label for="rtcRepair-prescale">Prescale</label>
				<input type="text" id="rtcRepair-prescale" value="{{rtcRepair.prescale}}">
			</div>
			<div data-role="fieldcontain">
				<label for="rtcRepair-slowdown">Slowdown</label>
				<input type="text" id="rtcRepair-slowdown" value="{{rtcRepair.slowdown}}">
			</div>
			<div data-role="fieldcontain">
				<label for="rtcRepair-autoCalibrationThreshold">Auto Calibration Threshold</label>
				<input type="text" id="rtcRepair-autoCalibrationThreshold" value="{{rtcRepair.autoCalibrationThreshold}}">
			</div>
			<div data-role="fieldcontain">
				<label for="rtcRepair-autoCalibrationMaxAccuracy">Auto Calibration Max Accuracy</label>
				<input type="text" id="rtcRepair-autoCalibrationMaxAccuracy" value="{{rtcRepair.autoCalibrationMaxAccuracy}}">
			</div>
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1 data-desc="log-config">Log Config</h1>
			<fieldset data-role="controlgroup">
				<label for="logConfig-gpsLog" data-desc="gps-log-enable">GPS Log Enable</label>
				<input type="checkbox" id="logConfig-gpsLog" {{#if logConfig.gpsLog}}checked="checked"{{/if}}>
			</fieldset>
			<fieldset data-role="controlgroup">
				<label for="logConfig-gsensorLog" data-desc="gsensor-log-enable">Gsensor Log Enable</label>
				<input type="checkbox" id="logConfig-gsensorLog" {{#if logConfig.gsensorLog}}checked="checked"{{/if}}>
			</fieldset>
			<fieldset data-role="controlgroup">
				<label for="logConfig-tcpDumpEnable" data-desc="tcp-dump-enable">TCP Dump Enable</label>
				<input type="checkbox" id="logConfig-tcpDumpEnable" {{#if logConfig.tcpDumpEnable}}checked="checked"{{/if}}>
			</fieldset>
			<fieldset data-role="controlgroup">
				<label for="logConfig-kmsgDumpEnable" data-desc="kmsg-dump-enable">Kernel Dump Enable</label>
				<input type="checkbox" id="logConfig-kmsgDumpEnable" {{#if logConfig.kmsgDumpEnable}}checked="checked"{{/if}}>
			</fieldset>
			<fieldset data-role="controlgroup">
				<label for="logConfig-mcuLogOutput" data-desc="mcu-log-enable">MCU Log Enable</label>
				<input type="checkbox" id="logConfig-mcuLogOutput" {{#if logConfig.mcuLogOutput}}checked="checked"{{/if}}>
			</fieldset>
			<div data-role="fieldcontain">
				<label for="logConfig-recLevel" data-desc="rec-log-level">Record Log Level</label>
				<select id="logConfig-recLevel">
					<option value="-1" {{#equal logConfig.recLevel -1}}selected="selected"{{/equal}}>-1</option>
					<option value="0" {{#equal logConfig.recLevel 0}}selected="selected"{{/equal}}>0</option>
					<option value="1" {{#equal logConfig.recLevel 1}}selected="selected"{{/equal}}>1</option>
					<option value="2" {{#equal logConfig.recLevel 2}}selected="selected"{{/equal}}>2</option>
					<option value="3" {{#equal logConfig.recLevel 3}}selected="selected"{{/equal}}>3</option>
				</select>
			</div>
			<div data-role="fieldcontain">
				<label for="logConfig-uploadLevel" data-desc="upload-log-level">Upload Log Level</label>
				<select id="logConfig-uploadLevel">
					<option value="-1" {{#equal logConfig.uploadLevel -1}}selected="selected"{{/equal}}>-1</option>
					<option value="0" {{#equal logConfig.uploadLevel 0}}selected="selected"{{/equal}}>0</option>
					<option value="1" {{#equal logConfig.uploadLevel 1}}selected="selected"{{/equal}}>1</option>
					<option value="2" {{#equal logConfig.uploadLevel 2}}selected="selected"{{/equal}}>2</option>
				</select>
			</div>
		</div>
	</div>
</script>
<script src="../js/jquery.min.js"></script>
<script src="../js/jquery.mobile-1.4.5.min.js"></script>
<script src="qunit/qunit-2.4.0.js"></script>
<script src="../js/handlebars.js"></script>
<script src="../js/webapp-model.js"></script>
<script src="../js/webapp-view.js"></script>
<script src="../js/webapp-controller.js"></script>
<script type="text/javascript">
	QUnit.test( "refreshValue", function( assert ) {
		var json_body = {
			"dvrIdentification": {
				"cid": 0,
				"vehicleNum": "0",
				"serialNo": "853",
				"version": "DV423V1.0.0",
				"svNum": "2568",
				"machineType": 1,
				"mcuId": "39:ff:d6:05:4e:42:38:38:18:79:21:51"
			},
			"videoChannelConfig": [
				{
					"channelId": 1,
					"bitrate": 1000,
					"framerate": 12,
					"rcMode": 1,
					"maxQp": -1,
					"minQp": -1,
					"fixQp": 30,
					"resolution": "D1",
					"OSDEnabled": true,
					"OSDMirrored": true,
					"audioEnabled": true,
					"mirrorEnable": true,
					"flipEnable": false
				},
				{
					"channelId": 2,
					"bitrate": 1000,
					"framerate": 12,
					"rcMode": 1,
					"maxQp": -1,
					"minQp": -1,
					"fixQp": 30,
					"resolution": "D1",
					"OSDEnabled": true,
					"OSDMirrored": false,
					"audioEnabled": true,
					"mirrorEnable": false,
					"flipEnable": false
				},
				{
					"channelId": 3,
					"bitrate": 1000,
					"framerate": 12,
					"rcMode": 2,
					"maxQp": -1,
					"minQp": -1,
					"fixQp": 30,
					"resolution": "D1",
					"OSDEnabled": true,
					"OSDMirrored": false,
					"audioEnabled": true,
					"mirrorEnable": false,
					"flipEnable": false
				},
				{
					"channelId": 4,
					"bitrate": 1000,
					"framerate": 12,
					"rcMode": 3,
					"maxQp": -1,
					"minQp": -1,
					"fixQp": 30,
					"resolution": "D1",
					"OSDEnabled": true,
					"OSDMirrored": false,
					"audioEnabled": true,
					"mirrorEnable": false,
					"flipEnable": false
				}
			],
			"networkConfig": {
				"ethernet": {
					"ipAddress": "*************",
					"macAddress": "38:d6:38:18:79:21",
					"subnetMask": "***************",
					"dnsServer": "*******"
				},
				"wifi": {
					"authmode": "AUTO",
					"encryption": "AUTO",
					"enable": true,
					"ssid": "Xiaomi_FDDC",
					"password":"88888888",
					"extraWifiList": [{"ssid":"sc2.5g", "password":"123456789"}, {"ssid":"abc", "password":"123456789"}]
				},
				"ap": {
					"enable": true,
					"frequency": "2.4G"
				},
				"cellular": {
					"enabled": true,
					"apn": "m2m005132.attz"
				},
				"webUiEnable": true
			},
			"mediaDeliveryConfig": {
				"url": "sharpvision-test-1.s3.amazonaws.com",
				"accessKey": "123456789",
				"secretKey": "123456789"
			},
			"managementServerConfig": {
				"url": "*************:2222",
				"user": "admin",
				"password": "123456"
			},
			"recordingSettings": {
				"loopFileMinutes": 15,
				"diffCardBackRec": true,
				"backupVideoEnable": true,
				"recStatistic": false,
				"continueLoopWaterLevel": 800,
				"recEnableArray": [
					1,
					0,
					1,
					0
				],
				"jpegEnableArray": [
					0,
					1,
					0,
					1
				],
				"sdLockIgnore": 0,
				"eventBufferPct": 10,
				"clipGenerating_Limit": 14400,
				"maxClipNum": 500,
				"storageEnableArray": [
					1,
					1,
					1,
					1,
					0
				]
			},
			"gpioConfig": [
				{
					"bindAoutEn": 0,
					"bindVout": 5,
					"triggerDelayTime": 1,
					"bindVoutPriority": 10,
					"eventTriggerEnable": 1
				},
				{
					"bindAoutEn": 0,
					"bindVout": 0,
					"triggerDelayTime": 1,
					"bindVoutPriority": 10,
					"eventTriggerEnable": 1
				},
				{
					"bindAoutEn": 0,
					"bindVout": 0,
					"triggerDelayTime": 1,
					"bindVoutPriority": 10,
					"eventTriggerEnable": 1
				},
				{
					"bindAoutEn": 0,
					"bindVout": 0,
					"triggerDelayTime": 1,
					"bindVoutPriority": 10,
					"eventTriggerEnable": 1
				},
				{
					"bindAoutEn": 0,
					"bindVout": 0,
					"triggerDelayTime": 1,
					"bindVoutPriority": 10,
					"eventTriggerEnable": 1
				},
				{
					"bindAoutEn": 0,
					"bindVout": 0,
					"triggerDelayTime": 1,
					"bindVoutPriority": 10,
					"eventTriggerEnable": 1
				},
				{
					"bindAoutEn": 0,
					"bindVout": 0,
					"triggerDelayTime": 1,
					"bindVoutPriority": 10,
					"eventTriggerEnable": 1
				},
				{
					"bindAoutEn": 0,
					"bindVout": 0,
					"triggerDelayTime": 1,
					"bindVoutPriority": 10,
					"eventTriggerEnable": 1
				}
			],
			"eventConfig": {
				"convertEnable": false,
				"bitrate": 1250,
				"resolution": 2,
				"audioEnable": 1,
				"preSeconds": 10,
				"postSeconds": 10,
				"channel1Enable": false,
				"channel2Enable": false,
				"channel3Enable": false,
				"channel4Enable": false
			},
			"timeConfig": {
				"ntpServer": "pool.ntp.org",
				"ntpIntervalMinutes": 15,
				"gpsSyncBackup": false
			},
			"accConfig": {
				"delaySec": 1800,
				"poweroffVoltage": 9000
			},
			"logConfig": {
				"gpsLog": false,
				"gsensorLog": false,
				"tcpDumpEnable": false,
				"kmsgDumpEnable": false,
				"mcuLogOutput": false,
				"recLevel": 2,
				"uploadLevel": 0
			},
			"rtcRepair": {
				"prescale": 32768,
				"slowdown": 0,
				"autoCalibrationThreshold": 5,
				"autoCalibrationMaxAccuracy": 604800,
				"autoCalibrationEn": false
			},
			"imageParam": {
				"brightness": -1,
				"contrast": -1,
				"hue": -1,
				"saturation": -1,
				"sharpness": -1
			}
		};

		/*var model = new WebappModel();
		var view = new WebappView(model, "config");
		var controller = new WebappController(model, view);
		view.refreshValue(json_body);
		view.refreshSaveConf(true);
		var json_path = [];
		function jsonContain(main, sub) {
			if (typeof(sub) == "object") {
				for (var k in sub) {
					json_path.push(k);
					jsonContain(main[k], sub[k]);
				}
				json_path.pop();
			} else {
				assert.equal(main, sub, json_path.join("."));
				json_path.pop();
			}
		}
		jsonContain(json_body, view.collectValue());*/
		assert.ok(true, "view");
	});

	QUnit.test( "refreshConfigs", function( assert ) {
		var model = new WebappModel();
		var view = new WebappView(model, "config");
		var controller = new WebappController(model, view);
		controller.refreshConfigs();
		assert.ok(true, "refreshConfigs");
	});

</script>
</body>
</html>