<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>QUnit Test</title>
	<link rel="stylesheet" href="../css/jquery.mobile-1.4.5.min.css">
	<link rel="stylesheet" href="./qunit/qunit-2.4.0.css">
	<style>
		.ui-content .ui-grid-b .ui-block-a {width: 45%}
		.ui-content .ui-grid-b .ui-block-b {width: 45%}
		.ui-content .ui-grid-b .ui-block-c {width: 10%}
		#popup-restore {max-width: 400px;}
		#popup-modify-pwd {max-width: 600px;}
		#popup-modify-pwd form div {padding: 10px 20px;}
		#mediaConfig {display: none;}
		#networkConfig {display: none;}
		#recordingSettings {display: none;}
		#eventConfig {display: none;}
		#tab-chn-2 {display: none;}
		#tab-chn-3 {display: none;}
		#tab-chn-4 {display: none;}
		#goto-popup-edit-ssid {display: none;}
		#popup-edit-ssid {max-width: 600px;}
		#popup-edit-ssid form div {padding: 10px 20px;}
		#edit-index {display: none;}
		#goto-popup-delete-ssid  {display: none;}
		#popup-delete-ssid {max-width: 600px;}
		#popup-delete-ssid form div {padding: 10px 20px;}
		#delete-ssid {text-align: center;}
		#delete-index {display: none;}
		#goto-popup-add-ssid {display: none;}
		#popup-add-ssid {max-width: 600px;}
		#popup-add-ssid form div {padding: 10px 20px;}
		#add-index {display: none;}
		#goto-popup-add-fail {display: none;}
		#popup-add-fail {max-width: 600px;}
		#popup-add-fail form div {padding: 10px 20px;}
		#popup-add-fail form div h3{text-align: center;}
		#goto-popup-delete-fail {display: none;}
		#popup-delete-fail {max-width: 600px;}
		#popup-delete-fail form div {padding: 10px 20px;}
		#popup-delete-fail form div h3{text-align: center;}
		#tab-pin-2 {display: none;}
		#tab-pin-3 {display: none;}
		#tab-pin-4 {display: none;}
		#tab-pin-5 {display: none;}
		#tab-pin-6 {display: none;}
		#tab-pin-7 {display: none;}
		#tab-pin-8 {display: none;}
		#goto-popup {display: none;}
		#popup {max-width:400px;}
		.hide-anchor {display: none;}

		.ui-icon-global:after {
			background-image: url("../css/images/icons-png/global-white.png");
			background-size: 14px 14px;
		}
		.ui-icon-slider:after {
			background-image: url("../css/images/icons-png/slider-white.png");
			background-size: 14px 14px;
		}
	</style>
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>
<div data-role="navbar">
	<ul>
		<li><a href="#" data-icon="slider" class="btn-nav" data-goto-id="mediaConfig" data-desc="nav-channel">媒体配置</a></li>
		<li><a href="#" data-icon="global" class="btn-nav" data-goto-id="networkConfig" data-desc="nav-network">网络配置</a></li>
		<li><a href="#" data-icon="alert" class="btn-nav" data-goto-id="logConfig" data-desc="nav-alarm">日志配置</a></li>
	</ul>
</div>
<div id="main" role="main" class="ui-content"></div>
<a id="show-info" href="#popup-show-info" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
<div data-role="popup" id="popup-show-info" data-overlay-theme="b" data-theme="b" data-dismissible="false">
	<div data-role="header">
		<h1></h1>
	</div>
	<div role="main" class="ui-content">
		<h3></h3>
		<p></p>
		<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
	</div>
</div>
<a id="ask-for-sure" href="#popup-ask-for-sure" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
<div data-role="popup" id="popup-ask-for-sure" data-overlay-theme="b" data-theme="b" data-dismissible="false" data-popup-title="">
	<div data-role="header">
		<h1></h1>
	</div>
	<div role="main" class="ui-content">
		<h3></h3>
		<p></p>
		<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-desc="cancel">取消</a>
		<a id="btn-ask-for-sure" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
	</div>
</div>
<a id="form-input2" href="#popup-form-input2" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
<div data-role="popup" id="popup-form-input2" data-overlay-theme="b" data-theme="b" data-dismissible="false" data-popup-title="">
	<div data-role="header">
		<h1></h1>
	</div>
	<div role="main" class="ui-content">
		<h3></h3>
		<form>
			<div>
				<label for="form-input2-1" class="ui-hidden-accessible"></label>
				<input id="form-input2-1" value="" placeholder="" type="text">
				<label for="form-input2-2" class="ui-hidden-accessible"></label>
				<input id="form-input2-2" value="" placeholder="" type="text">
				<p class="form-input-note">&nbsp;</p>
				<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-desc="cancel">取消</a>
				<a id="btn-form-input2" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-desc="confirm">确定</a>
			</div>
		</form>
	</div>
</div>
<a id="form-input3" href="#popup-form-input3" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
<div data-role="popup" id="popup-form-input3" data-overlay-theme="b" data-theme="b" data-dismissible="false" data-popup-title="">
	<div data-role="header">
		<h1></h1>
	</div>
	<div role="main" class="ui-content">
		<h3></h3>
		<form>
			<div>
				<label for="form-input3-0" class="ui-hidden-accessible"></label>
				<input id="form-input3-0" value="" placeholder="" type="text">
				<label for="form-input3-1" class="ui-hidden-accessible"></label>
				<input id="form-input3-1" value="" placeholder="" type="text">
				<label for="form-input3-2" class="ui-hidden-accessible"></label>
				<input id="form-input3-2" value="" placeholder="" type="text">
				<label for="form-input3-3" class="ui-hidden-accessible"></label>
				<input id="form-input3-3" value="" placeholder="" type="text">
				<p class="form-input-note">&nbsp;</p>
				<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-rel="back" data-desc="cancel">取消</a>
				<a id="btn-form-input3" href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline ui-btn-b" data-desc="confirm">确定</a>
			</div>
		</form>
	</div>
</div>
<script id="ssid-list-template" type="text/x-handlebars-template">
	<ul data-role="listview" data-split-icon="delete" data-inset="true">
		<li data-role="list-divider">SSID<i>/Password</i><span data-desc="list">列表</span></li>
		{{#each this}}
		<li><a href="#" class="btn-popup" data-popup-type="form-input2" data-popup-title="edit-ssid" data-list-index="{{@index}}"><img src="../css/images/icons-png/edit-white.png" alt="Edit" class="ui-li-icon ui-corner-all"><h2 id="networkConfig-wifi-staMode-apList-{{@index}}-ssid">{{ssid}}</h2><i id="networkConfig-wifi-staMode-apList-{{@index}}-password">{{password}}</i></a><a href="#" class="btn-popup" data-popup-type="ask-for-sure" data-popup-title="delete-ssid"  data-list-index="{{@index}}"></a></li>
		{{/each}}
		<li data-icon="plus"><a href="#" class="btn-popup" data-popup-type="form-input2" data-popup-title="add-ssid">&nbsp;</a></li>
	</ul>
</script>
<script id="configs-template" type="text/x-handlebars-template">
	<div id="mediaConfig" class="tab-configs">
		<fieldset data-role="controlgroup" data-type="horizontal">
			<input name="sel-chn" id="sel-ch1" class="sel-chn" value="ch1" checked="checked" type="radio">
			<label for="sel-ch1" data-desc="main-stream">主码流</label>
			<input name="sel-chn" id="sel-ch2" class="sel-chn" value="ch2" type="radio" disabled="disabled">
			<label for="sel-ch2" data-desc="sub-stream">子码流</label>
		</fieldset>

		<div id="tab-chn-{{channelId}}" class="tab-chn-conf">
			<div data-role="fieldcontain">
				<label for="mediaConfig-mainStream-resolution" data-desc="resolution">分辨率</label>
				<select id="mediaConfig-mainStream-resolution">
					<option value="CIF" {{#equal mediaConfig.mainStream.resolution "CIF"}}selected="selected"{{/equal}}>CIF</option>
					<option value="D1" {{#equal mediaConfig.mainStream.resolution "D1"}}selected="selected"{{/equal}}>D1</option>
					<option value="720P" {{#equal mediaConfig.mainStream.resolution "720P"}}selected="selected"{{/equal}}>720P</option>
					<option value="1080P" {{#equal mediaConfig.mainStream.resolution "1080P"}}selected="selected"{{/equal}}>1080P</option>
				</select>
			</div>
			<div data-role="fieldcontain">
				<label for="mediaConfig-mainStream-framerate" data-desc="framerate">帧率</label>
				<input type="text" id="mediaConfig-mainStream-framerate" value="{{mediaConfig.mainStream.framerate}}">
			</div>
			<div data-role="fieldcontain">
				<label for="mediaConfig-mainStream-bitrate" data-desc="bitrate">码率</label>
				<input type="text" id="mediaConfig-mainStream-bitrate" value="{{mediaConfig.mainStream.bitrate}}">
			</div>
			<div data-role="fieldcontain">
				<label for="mediaConfig-mainStream-rcMode" data-desc="rc-mode">码率控制模式</label>
				<select id="mediaConfig-mainStream-rcMode">
					<option value="VBR" {{#equal mediaConfig.mainStream.rcMode "VBR"}}selected="selected"{{/equal}}>VBR</option>
					<option value="CBR" {{#equal mediaConfig.mainStream.rcMode "CBR"}}selected="selected"{{/equal}}>CBR</option>
					<option value="ABR" {{#equal mediaConfig.mainStream.rcMode "ABR"}}selected="selected"{{/equal}}>ABR</option>
				</select>
			</div>
			<div data-role="fieldcontain">
				<label for="mediaConfig-mainStream-volume" data-desc="volume">音量</label>
				<input type="text" id="mediaConfig-mainStream-volume" value="{{mediaConfig.mainStream.volume}}">
			</div>
			<div data-role="fieldcontain">
				<fieldset data-role="controlgroup">
					<legend data-desc="extra-attribute">额外属性</legend>
					<label for="mediaConfig-mainStream-OSDEnable" data-desc="osd-enable">OSD使能</label>
					<input type="checkbox" id="mediaConfig-mainStream-OSDEnable" class="custom" {{#if mediaConfig.mainStream.OSDEnable}}checked="checked"{{/if}}>
					<label for="mediaConfig-mainStream-audioEnable" data-desc="audio-enable">音频使能</label>
					<input type="checkbox" id="mediaConfig-mainStream-audioEnable" class="custom" {{#if mediaConfig.mainStream.audioEnable}}checked="checked"{{/if}}>
					<label for="mediaConfig-mainStream-mirrorEnable" data-desc="mirror-enable">画面镜像</label>
					<input type="checkbox" id="mediaConfig-mainStream-mirrorEnable" class="custom" {{#if mediaConfig.mainStream.mirrorEnable}}checked="checked"{{/if}}>
					<label for="mediaConfig-mainStream-flipEnable" data-desc="flip-enable">画面翻转</label>
					<input type="checkbox" id="mediaConfig-mainStream-flipEnable" {{#if mediaConfig.mainStream.flipEnable}}checked="checked"{{/if}}>
				</fieldset>
			</div>
		</div>
	</div>
	<div id="networkConfig" class="tab-configs">
		<div data-role="fieldcontain">
			<label for="networkConfig-dnsServer" data-desc="dns-server">DNS Server</label>
			<input type="text" id="networkConfig-dnsServer" value="{{networkConfig.dnsServer}}">
		</div>
		<div data-role="fieldcontain">
			<label for="networkConfig-gateway" data-desc="gateway">Gateway</label>
			<input type="text" id="networkConfig-gateway" value="{{networkConfig.gateway}}">
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1>{{getKeyLang "ethernet"}}</h1>
			<div data-role="fieldcontain">
				<label for="networkConfig-ethernet-ipAddress" data-desc="ipaddr">IP Address</label>
				<input type="text" id="networkConfig-ethernet-ipAddress" value="{{networkConfig.ethernet.ipAddress}}">
			</div>
			<div data-role="fieldcontain">
				<label for="networkConfig-ethernet-macAddress" data-desc="mac-address">MAC Address</label>
				<input type="text" id="networkConfig-ethernet-macAddress" value="{{networkConfig.ethernet.macAddress}}">
			</div>
			<div data-role="fieldcontain">
				<label for="networkConfig-ethernet-subnetMask" data-desc="subnet-mask">Subnet Mask</label>
				<input type="text" id="networkConfig-ethernet-subnetMask" value="{{networkConfig.ethernet.subnetMask}}">
			</div>
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1>{{getKeyLang "wifi"}}</h1>
			<fieldset data-role="controlgroup">
				<label for="networkConfig-wifi-apMode-enable">Enable AP</label>
				<input type="checkbox" id="networkConfig-wifi-apMode-enable" class="custom" {{#if networkConfig.wifi.apMode.enable}}checked="checked"{{/if}}>
			</fieldset>
			<fieldset data-role="fieldcontain">
				<label for="networkConfig-wifi-apMode-ssid" data-desc="ssid">SSID</label>
				<input type="text" id="networkConfig-wifi-apMode-ssid" value="{{networkConfig.wifi.apMode.ssid}}">
			</fieldset>
			<fieldset data-role="fieldcontain">
				<label for="networkConfig-wifi-apMode-password" data-desc="password">Password</label>
				<input type="text" id="networkConfig-wifi-apMode-password" value="{{networkConfig.wifi.apMode.password}}">
			</fieldset>
			<fieldset data-role="controlgroup">
				<label for="networkConfig-wifi-staMode-enable">Enable STA</label>
				<input type="checkbox" id="networkConfig-wifi-staMode-enable" class="custom" {{#if networkConfig.wifi.staMode.enable}}checked="checked"{{/if}}>
			</fieldset>
			<div id="ssid-list"></div>
		</div>
		<div data-role="collapsible" data-collapsed="false">
			<h1>NTP</h1>
			<div data-role="fieldcontain">
				<label for="networkConfig-ntp-enable" data-desc="enable">Enable</label>
				<input type="checkbox" id="networkConfig-ntp-enable" class="custom" {{#if networkConfig.ntp.enable}}checked="checked"{{/if}}>
			</div>
			<div data-role="fieldcontain">
				<label for="networkConfig-ntp-serverAddress" data-desc="ntp-server">NTP服务器</label>
				<input type="text" id="networkConfig-ntp-serverAddress" value="{{networkConfig.ntp.serverAddress}}">
			</div>
			<div data-role="fieldcontain">
				<label for="networkConfig-ntp-interval" data-desc="ntp-interval-min">NTP同步间隔(分钟)</label>
				<input type="text" id="networkConfig-ntp-interval" value="{{networkConfig.ntp.interval}}">
			</div>
		</div>
	</div>
	<div id="logConfig" class="tab-configs">
		<fieldset data-role="controlgroup">
			<label for="logConfig-enable" data-desc="log-enable">Log Enable</label>
			<input type="checkbox" id="logConfig-enable" {{#if logConfig.enable}}checked="checked"{{/if}}>
		</fieldset>
		<div data-role="fieldcontain">
			<label for="logConfig-serverAddress" data-desc="url">URL</label>
			<input type="text" id="logConfig-serverAddress" value="{{logConfig.serverAddress}}">
		</div>
		<div data-role="fieldcontain">
			<label for="logConfig-recordLevel" data-desc="rec-log-level">Record Log Level</label>
			<select id="logConfig-recordLevel">
				<option value="-1" {{#equal logConfig.recordLevel -1}}selected="selected"{{/equal}}>-1</option>
				<option value="0" {{#equal logConfig.recordLevel 0}}selected="selected"{{/equal}}>0</option>
				<option value="1" {{#equal logConfig.recordLevel 1}}selected="selected"{{/equal}}>1</option>
				<option value="2" {{#equal logConfig.recordLevel 2}}selected="selected"{{/equal}}>2</option>
				<option value="3" {{#equal logConfig.recordLevel 3}}selected="selected"{{/equal}}>3</option>
			</select>
		</div>
		<div data-role="fieldcontain">
			<label for="logConfig-uploadLevel" data-desc="upload-log-level">Upload Log Level</label>
			<select id="logConfig-uploadLevel">
				<option value="-1" {{#equal logConfig.uploadLevel -1}}selected="selected"{{/equal}}>-1</option>
				<option value="0" {{#equal logConfig.uploadLevel 0}}selected="selected"{{/equal}}>0</option>
				<option value="1" {{#equal logConfig.uploadLevel 1}}selected="selected"{{/equal}}>1</option>
				<option value="2" {{#equal logConfig.uploadLevel 2}}selected="selected"{{/equal}}>2</option>
			</select>
		</div>
	</div>
</script>
<script src="../js/jquery.min.js"></script>
<script src="../js/jquery.mobile-1.4.5.min.js"></script>
<script src="qunit/qunit-2.4.0.js"></script>
<script src="../js/handlebars.js"></script>
<script src="../js/webapp-model.js"></script>
<script src="../js/webapp-view.js"></script>
<script type="text/javascript">
	QUnit.test( "refreshValue", function( assert ) {
		var json_body = {
			"ipcIdentification": {
				"serialNo": "1234",
				"firmware": "v1.0.0",
				"hardware": "IPC01"
			},
			"mediaConfig": {
				"mainStream": {
					"rcMode": "CBR",
					"bitrate": 3000,
					"framerate": 20,
					"resolution": "720P",
					"OSDEnable": true,
					"audioEnable": true,
					"volume": 5,
					"mirrorEnable": false,
					"flipEnable": false
				}
			},
			"networkConfig": {
				"dnsServer": "*******",
				"gateway": "************",
				"ethernet": {
					"ipAddress": "************23",
					"macAddress": "00:00:00:00:00:00",
					"subnetMask": "*************"
				},
				"wifi": {
					"apMode": {
						"enable": false,
						"ssid": "hd900-hd1",
						"password": "123456789"
					},
					"staMode": {
						"enable": true,
						"apList": [
							{
								"ssid": "xiaomi",
								"password": "123456789"
							},
							{
								"ssid": "tp-link",
								"password": "123456789"
							},
							{
								"ssid": "dvrap",
								"password": "123456789"
							}
						]
					}
				},
				"ntp": {
					"enable": true,
					"serverAddress": "*************",
					"interval": 30
				}
			},
			"logConfig": {
				"enable": true,
				"recordLevel": 2,
				"uploadLevel": 1,
				"serverAddress": "*************:2222"
			}
		};

		var model = new WebappModel();
		var view = new WebappView(model, "config");
		view.refreshConfigs(json_body);
		view.collectValue();
		var json_path = [];
		function jsonContain(main, sub) {
			if (typeof(sub) == "object") {
				for (var k in sub) {
					json_path.push(k);
					jsonContain(main[k], sub[k]);
				}
				json_path.pop();
			} else {
				assert.equal(main, sub, json_path.join("."));
				json_path.pop();
			}
		}
		jsonContain(json_body, view.collectJson);
		view.showErrorInfo("getConfigError", {"errorType":255, "description":"internal error!"}).showLoading().hideLoading();
		assert.ok(true, "view");
	});
</script>
</body>
</html>