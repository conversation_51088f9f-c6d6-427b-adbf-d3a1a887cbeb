<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>QUnit Test</title>
	<link rel="stylesheet" href="qunit/qunit-2.4.0.css">
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>
<script src="../js/jquery.min.js"></script>
<script src="../js/jquery.mobile-1.4.5.min.js"></script>
<script src="qunit/qunit-2.4.0.js"></script>
<script src="../js/handlebars.js"></script>
<script src="../js/webapp-model.js"></script>
<script type="text/javascript">
	QUnit.test( "getKeyLang", function( assert ) {
		var appModel = new WebappModel();
		assert.equal(appModel.getKeyLang("webui", "en"), "WEB UI");
		assert.equal(appModel.getKeyLang("webui", "cn"), "WEB界面");
		assert.equal(appModel.getKeyLang("123", "en"), undefined);
		assert.equal(appModel.getKeyLang("test-result:", "en"), "Test Result:");
		assert.equal(appModel.getKeyLang("test-result:", "cn"), "测试结果：");
	});

	QUnit.test("setConfigJson", function( assert ) {
		var appModel = new WebappModel();
		var config = {
			"ipcIdentification": {
				"serialNo": "1234",
				"firmware": "v1.0.0",
				"hardware": "IPC01"
			},
			"mediaConfig": {
				"mainStream": {
					"rcMode": "CBR",
					"bitrate": 3000,
					"framerate": 20,
					"resolution": "720P",
					"OSDEnable": true,
					"audioEnable": true,
					"volume": 5,
					"mirrorEnable": false,
					"flipEnable": false
				}
			},
			"networkConfig": {
				"dnsServer": "*******",
				"gateway": "************",
				"ethernet": {
					"ipAddress": "************23",
					"macAddress": "00:00:00:00:00:00",
					"subnetMask": "*************"
				},
				"wifi": {
					"apMode": {
						"enable": false,
						"ssid": "hd900-hd1",
						"password": "123456789"
					},
					"staMode": {
						"enable": true,
						"apList": []
					}
				},
				"ntp": {
					"enable": true,
					"serverAddress": "*************",
					"interval": 30
				}
			},
			"logConfig": {
				"enable": true,
				"recordLevel": 2,
				"uploadLevel": 1,
				"serverAddress": "*************:2222"
			}
		};
		appModel.setConfigJson(config);
		console.log(appModel.getConfigJson());
		assert.ok(true, "setConfigJson");
	});

	QUnit.test("setScanSsidJson", function( assert ) {
		var scanSsidJson = {
			"replyFrom": "factory_daemon",
			"wifiRemember": [
				{
					"ssid": "Xiaomi_FDDC",
					"password": "88888888",
					"signal": 0
				},
				{
					"ssid": "sc2.5g",
					"password": "123456789",
					"signal": 100
				}
			],
			"wifiNewScan": [
				{
					"ssid": "360WiFi-3A",
					"signal": 100
				},
				{
					"ssid": "ST-DVR1036000005",
					"signal": 94
				},
				{
					"ssid": "Xiaomi_CE85_plus",
					"signal": 100
				},
				{
					"ssid": "citic",
					"signal": 100
				}
			]
		};
		var appModel = new WebappModel();
		appModel.setScanSsidJson(scanSsidJson);
		console.log(appModel.getScanSsidJson());
		appModel.addScanSsid({"ssid":"360WiFi-3A","password":"123456789","signal":100});
		assert.equal(appModel.getScanSsidRememberLenght(), 3);
		console.log(appModel.getScanSsidJson());
		appModel.delScanSsid({"ssid":"sc2.5g","password":"123456789"});
		assert.equal(appModel.getScanSsidRememberLenght(), 2);
		console.log(appModel.getScanSsidJson());
		console.log(appModel.getScanSsidForConfig());
		assert.ok(true, "setScanSsidJson");
	});
</script>
</body>
</html>