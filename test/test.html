<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width">
	<title>QUnit Test</title>
	<link rel="stylesheet" href="qunit/qunit-2.4.0.css">
</head>
<body>
<div id="qunit"></div>
<div id="qunit-fixture"></div>
<script src="qunit/qunit-2.4.0.js"></script>
<script src="../js/jquery.min.js"></script>
<script type="text/javascript">
	QUnit.test( "hello test", function( assert ) {
		assert.ok( 1 == "1", "Passed!" );
	});
	QUnit.test( "array sort", function( assert ) {
		list = [];
		for (i=0; i<10; i++){
			for (j=0; j<4; j++){
				var e = {};
				e["time"] = i;
				e["chn"] = j;
				list.push(e);
			}
		}
		//console.log(list);
		list.sort(function(a, b){
			if (a["chn"] < b["chn"]){
				return -1;
			}else if (a["chn"] > b["chn"]){
				return 1;
			}else if (a["time"] < b["time"]){
				return -1;
			}else if(a["time"] > b["time"]){
				return 1;
			}
		});
		console.log(list);
		assert.ok( true, "Passed!" );
	});
	QUnit.test("test Date()", function( assert ) {
		now = new Date();
		console.log(now+":"+now.getTime()/1000);
		console.log(now.getTimezoneOffset()+":"+now.getTimezoneOffset()/60);
		time = new Date(now.getTime());
		console.log(time);
		assert.ok( true, "Passed!" );
	});

	QUnit.test("parse json keys", function( assert ) {
		var json = {"a":1, "b":"b-value", "c":[1, 2, 3], "d":{"d1":1, "d2":2}, "e":[{"e":1},{"e":2},{"e":3}], "f": true, "g":[]};

		for (var k in json){
			//console.log(k+",type:"+typeof(json[k])+",value:"+json[k]);
		}

		var path = [];
		function traverse1(o) {
			if (typeof(o) == "object") {
				for (var k in o){
					//console.log("key:"+k);
					path.push(k);
					traverse1(o[k]);
				}
				path.pop();
			} else {
				//console.log(typeof(o)+":"+o);
				console.log(path.join(".")+":"+typeof(o)+":"+o);
				path.pop();
			}
		}
		traverse1(json);
		assert.ok( true, "Passed!" );
	});

	QUnit.test("extern json", function( assert ) {
		var json1 = {
			"dvrIdentification": {
			"cid": 0,
			"vehicleNum": "0",
			"serialNo": "853",
			"version": "DV423V1.0.0",
			"svNum": "2583",
			"machineType": 1,
			"mcuId": "39:ff:d6:05:4e:42:38:38:18:79:21:51"
		}}, json2 = {
			"networkConfig": {
				"ethernet": {
					"ipAddress": "*************",
					"macAddress": "38:d6:38:18:79:21",
					"subnetMask": "***************",
					"dnsServer": "*******"
				}
			}
		};
		var json = {};
		$.extend(json, json1);
		console.log(json);
		$.extend(json, json2);
		console.log(json);
		assert.ok( true, "Passed!" );
	});
</script>
</body>
</html>