﻿<!DOCTYPE html>
<html>
<head>
 <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
 <meta http-equiv="Pragma" content="no-cache" />
 <meta http-equiv="Expires" content="0" />
 
<!-- 内联脚本：在DOM渲染前隐藏指定元素 -->
<script>
(function() {
    try {
        // 从localStorage读取需要隐藏的元素ID
        var hiddenElementsStr = localStorage.getItem('hiddenElements');
        if (hiddenElementsStr) {
            var hiddenElements = JSON.parse(hiddenElementsStr);
            if (hiddenElements && hiddenElements.length) {
                // 创建样式标签
                var style = document.createElement('style');
                style.type = 'text/css';
                
                // 为每个元素生成CSS规则
                var css = hiddenElements.map(function(id) {
                    return '#' + id + ' { display: none !important; }';
                }).join('\n');
                
                // 添加CSS到样式标签
                if (style.styleSheet) {
                    style.styleSheet.cssText = css; // 兼容IE
                } else {
                    style.appendChild(document.createTextNode(css));
                }
                
                // 将样式标签添加到head（必须等head标签存在）
                document.head.appendChild(style);
                console.log('页面加载前已隐藏元素:', hiddenElements);
            }
        }
    } catch (e) {
        console.error('预加载隐藏元素时出错:', e);
    }
})();
</script>

<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="./css/jquery.mobile.css">
<link rel="stylesheet" href="./css/jquery.extra-themes.css">
<!-- link id="dialogLink" rel="stylesheet" href="./css/dialog.css" -->
<!-- link rel="stylesheet" href="./css/common.css" -->
<script src="./js/jquery.min.js"></script>
<script src="./js/FileSaver.js"></script>


<script src="./js/jquery.mobile.js"></script>
<script src="./js/webapp-language.js"></script>
<script src="./js/login.js"></script>
<script>
// 页面加载时应用保存的颜色主题
$(document).ready(function() {
    // 从localStorage获取保存的颜色
    var savedColor = window.localStorage.getItem(window.location.host + "-theme-color");
    if (savedColor) {
        // 应用保存的颜色设置
        document.body.style.setProperty('--sk_color', savedColor, 'important');
        console.log("应用保存的颜色设置:", savedColor);
        
        // 应用到其他可能使用该颜色的元素
        $('.ui-btn-active').css('background-color', savedColor);
        $('.ui-page-theme-b a.ui-btn').css('color', savedColor);
    }
});
</script>
<title data-desc="webui">WEB UI</title>
	<style>
		.footer {
			overflow:hidden;
		}
		html:-moz-full-screen {
			background: red;
			width: 100%;
			height: auto;
		}
		html:-webkit-full-screen {
			background: red;
			width: 100%;
			height: auto;
		}
		html:fullscreen {
			background: red;
			width: 100%;
			height: auto;
		}
		.btn-sd-hd {
			cursor: pointer;
			overflow: visible;
			border: 0;
			padding: 0;
			margin: 0;
			float: right;
			/*opacity: 0.75;*/
			display: block;
			/*-webkit-appearance: none;*/
			/*-webkit-transition: opacity 0.2s;*/
			/*transition: opacity 0.2s;*/
			/*-webkit-box-shadow: none;*/
			/*box-shadow: none;*/
			/*background: url(./css/images/icons-png/slider-white.png) 0 0 no-repeat;*/
			z-index: 998;
			right: 100px;
			bottom: 55px;
			/*color: #FFF;*/
			margin-top: -80px !important;
			position: absolute !important;
		}
		.ui-content {
			border: 0;
			padding: 0;
			margin: 0;
		}
		#popup-show-image-param {
			width:350px;
		}
		#popup-show-alg-param {
			width:350px;
			height: 100px;
		}
		#adjust-box {
			margin: 10px;
		}
		.select_button_text{
			margin-top: 0px;
			height: 17px;
			width: 150px !important;
			display: inline-block;
			font-size: 17px;
			font-weight: 400;
			line-height: 0px;  /*line-height must be equal to height*/
			text-shadow: none !important;
			border-color:var(--theme_btn_border_color)!important;
		}
		.select_button_text:active {
			position:relative;
			top:1px;
		}
		.control-btn:active {
			color: black;
			background: yellow;
		}
		.control-round-left:active {
			color: black;
			background: yellow;
		}
		.control-round-right:active {
			color: black;
			background: yellow;
		}
		.control-wrapper {
			cursor: pointer;
			position: relative;
			width: 25vw;
			height: 25vw;
			max-width: 150px;
			max-height: 150px;
			min-width: 120px;
			min-height: 120px;
			margin: 0px auto;
			border-radius: 50%;
		}
		.control-btn {
			position: absolute;
			width: 38%;
			height: 38%;
			display: flex;
			align-items: center;
			justify-content: center;
			border: 1px solid #78aee4;
			box-sizing: border-box;
			transition: all .3s linear;
		}
		.control-btn:after {
			content: '';
			position: absolute;
			width: 60%;
			height: 60%;
			/*background: #fff;*/
			z-index: 2;
		}
		.control-btn:before {
			content: '';
			position: relative;
			display: block;
			width: 8px;
			height: 8px;
			border-top: 3px solid #78aee4;
			border-right: 3px solid #78aee4;
			border-radius: 0 4px 0 0;
			box-sizing: border-box;
			z-index: 2;
		}
		.control-top {
			top: -10px;
			left: 50%;
			transform: translateX(-50%) rotate(-45deg);
			border-radius: 4px 100% 4px 85%;
		}
		.control-top:before {
			transform: translate(30%, -25%);
		}
		.control-top:after {
			left: 0;
			bottom: 0;
			border-top: 1px solid #78aee4;
			border-right: 1px solid #78aee4;
			border-radius: 0 100% 0 0;
		}
		.control-bottom {
			left: 50%;
			bottom: -10px;
			transform: translateX(-50%) rotate(45deg);
			border-radius: 85% 4px 100% 4px;
		}
		.control-bottom:before {
			transform: translate(25%, 25%) rotate(90deg);
		}
		.control-bottom:after {
			top: 0;
			left: 0;
			border-bottom: 1px solid #78aee4;
			border-right: 1px solid #78aee4;
			border-radius: 0 0 100% 0;
		}
		.control-left {
			top: 50%;
			left: -10px;
			transform: translateY(-50%) rotate(45deg);
			border-radius: 4px 85% 4px 100%;
		}
		.control-left:before {
			transform: translate(-25%, 30%) rotate(180deg);
		}
		.control-left:after{
			right: 0;
			top: 0;
			border-bottom: 1px solid #78aee4;
			border-left: 1px solid #78aee4;
			border-radius: 0 0 0 100%;
		}
		.control-right {
			top: 50%;
			right: -10px;
			transform: translateY(-50%) rotate(45deg);
			border-radius: 4px 100% 4px 85%;
		}
		.control-right:before {
			transform: translate(30%, -25%);
		}
		.control-right:after {
			left: 0;
			bottom: 0;
			border-top: 1px solid #78aee4;
			border-right: 1px solid #78aee4;
			border-radius: 0 100% 0 0;
		}
		.control-round {
			position: absolute;
			top: 25%;
			left: 25%;
			transform: translate(-50%, -50%);
			width: 25.6%;
			height: 25.6%;
			/*background: #fff;*/
			border-radius: 50%;
		}
		.control-round-left {
			position: absolute;
			top: 150%;
			left: 150%;
			transform: translate(-105%, -50%);
			display: flex;
			justify-content: center;
			align-items: center;
			width: 27px;
			height: 55px;
			border: 1px solid #78aee4;
			border-radius: 45px 0 0 45px;
		}
		.control-round-left:after {
			content: "-";
			display: block;
			width: 50px;
			line-height: 50px;
			text-align: center;
			/* background: #78aee4; */
			font-weight: bolder;
			font-size: 22px;
			color: #78aee4;
			border-radius: 50%;
		}
		#TestPreview{
			height:100%;
			width:20%;
			position:absolute;
			left:40%; top:-18px;
			font-size:18px;
			line-height:52px;
			text-align:center;
			font-weight:var(--theme_title_font_weight);
			text-transform:var(--theme_title_text_transform);
			color:var(--theme_title_color);
			text-decoration:none;
		}
		.control-round-right {
			position: absolute;
			top: 150%;
			left: 150%;
			transform: translate(5%, -50%);
			display: flex;
			justify-content: center;
			align-items: center;
			width: 27px;
			height: 55px;
			border: 1px solid #78aee4;
			border-radius:  0 45px 45px 0;
		}
		.control-round-right:after {
			content: "+";
			display: block;
			width: 50px;
			line-height: 50px;
			text-align: center;
			/*background: #78aee4;*/
			font-weight: bolder;
			font-size: 22px;
			color: #78aee4;
			border-radius: 50%;
		}
		#change_ch_select {
				z-index: 1501;
				position: absolute;
				left: 48px;
				top: 13px;
			}
    </style>
</head>

<body>
	<div id="page-home" data-role="none" style="background:#fff;" data-dom-cache="true">
		<div class='title index_hide_group'>			
			<a class="title_back" id="btn-logout"></a>
			<img id="vue_logo" style="display:none;" src="./css/images/logo/VUE.png" />
			<p id="TestPreview" data-desc="preview">Preview</p>
			<div class="title_right" style ="top:13px;right:10px;" id="div-lang">
			
				<script id="sel-lang-template" type="text/x-handlebars-template">
				<select id="sel-lang" data-role="none" class="custom-select" placeholder="undefind">
					<option value="EN">English</option>
					<option value="CN">简体中文</option>
					
					<option value="JP">日本語</option>
					<option value="ES">Español</option>
					<option value="PT">Português</option>
					<option value="RU">Pусский</option>
					<option value="TUR" {{#unequal ipcIdentification.hardware "ADA32V2"}}style="display: none;"{{/unequal}}>Türkiye</option>
					<option value="INC" {{#unequal ipcIdentification.hardware "DMS31V2 DMS885N"}}style="display: none;"{{/unequal}}>Indic</option>
					<option value="TEL" {{#unequal ipcIdentification.customer "201876"}}style="display: none;"{{/unequal}}>తెలుగు</option>
					<option value="DG"  {{#equal ipcIdentification.hardware "DMS31V2 DMS885N"}}style="display: none;"{{/equal}}>Deutsch</option>
					<option value="ITA" {{#unequal ipcIdentification.customer "200032"}}style="display: none;"{{/unequal}} {{#equal ipcIdentification.hardware "DMS31V2 DMS885N"}}style="display: none;"{{/equal}}>Italiano</option>
					<option value="FRA" {{#equal ipcIdentification.hardware "DMS31V2 DMS885N"}}style="display: none;"{{/equal}}>Français</option>
				</select>
				</script>
			</div>	
			
			<select id="change_ch_select" data-role="none" placeholder="undefind">
				<option value="ch0">CH1</option>
				<option value="ch1">CH2</option>
			</select>
		</div>
		
		<div class='title_hide'></div>
		<div id="index-menu">
		<script id="index-menu-template" type="text/x-handlebars-template">
		<div class="index_hide_group" id="index_menu">
			<table class="menu" rules="none" cellspacing="5%">
            	<td><li><a class="menu_checked" href="./index.html" data-transition="slidedown" data-desc="preview">预览</a></li></td>
				<!-- <td id="index_query"{{{hideHWandCustomer "ADA32V2" "201570"}}} {{{hideNotWebuiFull "ADA32V2 ADA32V3 RCT16947V2 ADA32IR ADA42PTZV1"}}}><li><a href="./query.html" data-ajax="false" data-transition="slidedown"  data-desc="query">查询</a></li></td> -->
				<td id="index_config"><li><a href="./config.html" data-ajax="false" data-transition="slidedown"  id="nav-dev-man" data-desc="config">配置</a></li></td>
				<td id="index_system" ><li><a href="./system.html" data-ajax="false" data-transition="slidedown"  data-desc="system">系统</a></li></td>
    		</table>
		</div>
		</script>
		</div>
		
		<div role="main" data-role="none" class="ui-content" id="win-img">
			<div class='title index_frs_group' style="display: none;">
					<a id='frs_div_back' class="title_back"  href='javascript:void(0)'></a>
					<p data-desc="face-recognition">Face Recognition</p>
			</div>
			<div id="cam-win-4x" class="ui-grid-a win-chn" style="display: none">
				<div id="blk-ch1" class="ui-block-a ui-bar-b win-4x-left">
					<img class="cam-img" alt="CH1" src="" width="100%" data-click="btn-ch1" />
				</div>
				<div id="blk-ch2" class="ui-block-b ui-bar-b win-4x-right">
					<img class="cam-img" alt="CH2" src="" width="100%" data-click="btn-ch2"/>
				</div>
				<div id="blk-ch3" class="ui-block-a ui-bar-b win-4x-left">
					<img class="cam-img" alt="CH3" src="" width="100%" data-click="btn-ch3"/>
				</div>
				<div id="blk-ch4" class="ui-block-b ui-bar-b win-4x-right">
					<img class="cam-img" alt="CH4" src="" width="100%" data-click="btn-ch4"/>
				</div>
			</div>
			<div id="cam-win-2x" class="ui-grid-a win-chn" style="display: none">
				<div id="blk-2x-ch1" class="ui-block-a ui-bar-b win-2x-left">
					<img class="cam-img" alt="CH1" src="" width="100%" data-click="btn-ch1" />
				</div>
				<div id="blk-2x-ch2" class="ui-block-b ui-bar-b win-2x-right">
					<img class="cam-img" alt="CH2" src="" width="100%" data-click="btn-ch2"/>
				</div>
			</div>
			<div id="cam-win-1x" class="ui-grid-solo" style='position:relative;margin-right: calc(100% - 100vw);'>
				<div class="ui-block-a win-chn" style="text-align:center;" >
					<img id="img-solo" class="cam-img" alt="CHx"  src="" width="100%" data-click="btn-all"/>
					<video id="rtc_media_player" autoplay="" style="display: none; width: 100%; height: 100%; object-fit: fill;"></video>
				</div>

				<div class="div_warn"><p id="txt_warn"></p></div>
				<canvas id="draw_canvas" style='position:absolute;top:0px;display: block;text-align: center;'/>
			</div>
		</div>
		<div data-role="footer" id="face_footer" data-position="fixed" data-fullscreen="false" style="display:none;">
			<a style="visibility:hidden;"></a>
			<a href="#" id="export_user" data-role="button" data-desc="click-export-faceid" style="display:inline-block;vertical-align:middle;">ExportFaceId</a>
			<!--a href="#" id="click-import-user" data-role="button" data-desc="click-import-faceid">ImportFaceId</a-->
			<label class="input-label" style="height: 23px; display:inline-block;font-size:12.5px;font-weight:700;vertical-align:middle;text-align:center;margin-top:7px;text-shadow:0 1px 0 #0ab3cf">
				<span style="margin-left:15px;margin-right:15px;vertical-align:middle;" data-desc="click-import-faceid">Import FaceId</span>
				<input type="file" id="import_user" name="importuser" title='open file'>
			</label>
		</div>

		<div id="calibration-info">
		<script id="calibration-info-template" type="text/x-handlebars-template">
				
			<div data-role="none" class="index_hide_group" id="calibration_pre_div" style="width:100%;">
				<table style="width:100%" rules="none" cellspacing="5%">
					<!-- td {{{showHardware "ADA32N1 ADA32NSDK"}}}><li style="width:100%;text-align:center;"><button id="streamType" class="btn-streamtype"></button></li></td -->	
					<td id="td-btn-calibration"><li style="width:100%;text-align:center;"><a class="btn-calibration" data-title="Toggle calibration"></a></li></td>
					<!-- <td {{#isNotSupportDMS ipcIdentification.hardware}}style="display: none;"{{/isNotSupportDMS}}><li style="width:100%;text-align:center;"><a class="btn-register" data-title="Toggle register"></a></li></td>
					<td id="td-btn-imageparam"{{{hideHWandCustomer "ADA32V2" "201570"}}}{{#isSupportAlg ipcIdentification.hardware}}style="display: none;"{{/isSupportAlg}} ><li style="width:100%;text-align:center;"><a id="show-image-param" class="btn-imageparam" href="#popup-show-image-param"  data-rel="popup" data-title="Toggle imageparam"></a></li></td>
					<td><li {{#isPTZ ipcIdentification.hardware}} style="display:none" {{/isPTZ}} style="width:100%;text-align:center;"><a class="btn-trackpos" data-title="Get track position"></a></li></td>
					<td><li {{{hideNotHardware "HDW845V1"}}} style="width:100%;text-align:center;"><a class="btn-zoom" data-title="Set Zoom"></a></li></td>					 -->
					<td id="td-btn-fullscreen" ><li style="width:100%;text-align:center;"><a class="btn-fullscreen" data-title="Toggle fullscreen"></a></li></td>
					<!-- <td id="td-btn-algparam" {{{showHWandCustomer "ADA32V2" "201570"}}}><li style="width:100%;text-align:center;"><a id="show-alg-param" class="btn-algparam" href="#popup-show-alg-param"  data-rel="popup" data-title="Toggle algparam"></a></li></td>		 -->
				</table>	
			</div>
			
			<div {{{hideHardware "ADA900V1"}}}>	
	
				<div id="calibration_start_div" style="display: none">
					<div class="custom-select-box"  id="calibration-mode" >
						<label class="single_option_text" for="algConfig-pd-pdWorkMode" data-desc="calibration-mode">标定模式</label>
						<div><select class="custom-select" id="algConfig-pd-pdWorkMode" data-role="none" value="{{pdWorkMode}}">
							<option value="normal" {{#equal pdWorkMode 0}}selected="selected"{{/equal}}data-desc="calibration-mode-normal">普通模式</option>
							<!-- <option id="mod-measure" {{#equal pdWorkMode 1}}selected="selected"{{/equal}}{{#isA37}}style="display:none;"{{/isA37}} {{{hideHardware "ADA32V3 ADA32N1"}}} value="measure" data-desc="calibration-mode-measure">测距模式</option> -->
						</select></div>
					</div>
				</div>

				<div class="custom-select-box" id="select-roistyle" style="display: none;">
					<button class="select_button_text" id="roi_button" data-desc="pd-RoiStyle">ROI类型</button>
					<div style="display: inline-block;"><select class="custom-select" id="algConfig-pd-pdRoiStyle" data-role="none" value="{{pdRoiStyle}}">
						<option data-desc="pd-RoiStyle_0" value=0 {{#equal pdRoiStyle 0}}selected="selected"{{/equal}}>水平梯形</option>
						<option data-desc="pd-RoiStyle_1" id="vertical_left" value=1 {{#equal pdRoiStyle 1}}selected="selected"{{/equal}}>垂直(左边红色)</option>
						<option data-desc="pd-RoiStyle_2" id="vertical_right" value=2 {{#equal pdRoiStyle 2}}selected="selected"{{/equal}}>垂直(右边红色)</option>
						<!--<option data-desc="pd-RoiStyle_3" id="semicircle" value=3 {{#isIRCamButNotExhibition ../ipcIdentification.board ../ipcIdentification.customer "000USA"}}style="display: none"{{/isIRCamButNotExhibition}} {{#equal pdRoiStyle 3}}selected="selected"{{/equal}}>半圆</option>-->
						<option data-desc="pd-RoiStyle_3" id="semicircle" value=3  {{#equal pdRoiStyle 3}}selected="selected"{{/equal}}>半圆</option>
						<option data-desc="pd-RoiStyle_4" id="ellipse" value=4  {{#isIRCam ../ada42Identification.board}}style="display: none"{{/isIRCam}} {{#equal pdRoiStyle 4}}selected="selected"{{/equal}}>椭圆</option>
						<option data-desc="pd-RoiStyle_5" id="draw_board" value=5 {{#equal pdRoiStyle 5}}selected=“selected"{{/equal}}>画布</option>
					</select></div>
				</div>

				<div id="hollow_div" style="display: none">
					<div class="custom-select-box"  id="hollow_onoff" {{#isNotSupportPD ../ada42Identification.hardware}}style="display: none;"{{/isNotSupportPD}}>
						<button class="select_button_text" id="hollow_button" data-desc="calibration-hollow" >hollow</button>
						<div style="display: inline-block;"><select class="custom-select" id="select-hollow" data-role="none" value="{{pdHollow}}">		
							<option value=1 data-desc="on"  {{#equal pdHollow 1}}selected="selected"{{/equal}}>开</option>
							<option value=0 data-desc="off" {{#equal pdHollow 0}}selected="selected"{{/equal}}>关</option>
						</select></div>
					</div>
				</div>

				<div data-role="none" class="input-switch-box" id="switch-freemode-box" style="display: none;">
					<p data-desc="roi-freemode">ROI任意多边形开关</p>
					<input data-role="none" class="switchBtn custom" type="checkbox" id="roi-freemode-enable" >
					<label data-role="none" for="roi-freemode-enable"></label>
				</div>

				<div id="alarmIcon" style="display: none">
					<div class="custom-select-box">
						<label class="single_option_text"  data-desc="alarm-icon-pos" >报警图标位置</label>
						<div style="display: inline-block;"><select class="custom-select" id="select-iconLocation" data-role="none" value="{{iconLocation}}">		
							<option value=0 data-desc="alarmIcon-top"  {{#equal iconLocation 0}}selected="selected"{{/equal}}>顶部</option>
							<option value=1 data-desc="alarmIcon-bottom" {{#equal iconLocation 1}}selected="selected"{{/equal}}>底部</option>
						</select></div>
					</div>
				</div>

				<div class="roi_board_class" style="display: none;">
					<div class="custom-select-box">
						<label class="single_option_text" data-desc="select-zone">颜色选择</label>
						<div><select id="roi-board-color-select" class="custom-select" data-role="none" value=2>
							<option value=0 > &#x1F7E9; </option>
							<option value=1 > &#x1F7E8; </option>
							<option value=2 selected="selected"> &#x1F7E5; </option>
						</select></div>
					</div>
					
					<div data-role="none" class="rangeinput">
						<p class="rangeinput_title" data-desc="smoothness">光滑度<p>
						<label data-role="none" for="roi-board-color-scale"></label>
						<input data-role="none"  class="rangeinput_input" type="range"  id = "roi-board-color-scale" step=0.01 min=0 max=1 value={{pdRoiBoard.pdRedScale}}>
						<p class="rangeinput_value">{{pdRoiBoard.pdRedScale}}<p>
					</div>	

					<div data-role="none" class="input-switch-box">
						<p data-desc="hide-other-zone">隐藏其他区域</p>
						<input data-role="none" class="switchBtn custom" type="checkbox" id="roi-board-hide-other" >
						<label data-role="none" for="roi-board-hide-other"></label>
					</div>
				</div>

			</div>
			<div {{{showHardware "ADA900V1"}}}>	
				<div class="custom-select-box" id="apc_direction_div" style="display: none;">
					<label class="single_option_text" for="select-apc-direction" data-desc="apc-direction">监测行人方向</label>
					<div><select class="custom-select" id="select-apc-direction" data-role="none" value="{{algConfig.apcConf.apcDirection}}">
						<option value=0 id="apc-down" {{#equal algConfig.apcConf.apcDirection 0}}selected="selected"{{/equal}} data-desc="apc-direction-down"">向下</option>
						<option value=1 id="apc-up"   {{#equal algConfig.apcConf.apcDirection 1}}selected="selected"{{/equal}} data-desc="apc-direction-up"">向上</option>
						<option value=2 id="apc-right" {{#equal algConfig.apcConf.apcDirection 2}}selected="selected"{{/equal}} data-desc="apc-direction-left"">向左</option>
						<option value=3 id="apc-left"{{#equal algConfig.apcConf.apcDirection 3}}selected="selected"{{/equal}}  data-desc="apc-direction-right"">向右</option>
					</select></div>
				</div>
			</div>
			<div id="calibration_button" style="display: none">
				<div class="control-wrapper" style='display:none;position:relative;margin-top:0px;margin-left:50%;transform: translate(-50%, 0%);'>
					<div class="ptz-btn control-btn control-top" data-ptz="up"></div>
					<div class="ptz-btn control-btn control-left" data-ptz="left"></div>
					<div class="ptz-btn control-btn control-bottom" data-ptz="down"></div>
					<div class="ptz-btn control-btn control-right" data-ptz="right"></div>
					<div class="control-round">
						<div class="ptz-btn control-round-left" data-ptz="zoomDown"></div>
						<div class="ptz-btn control-round-right" data-ptz="zoomUp"></div>
					</div>
				</div>
				<a href="#" class="href-btn" id="btn_calibrationboard_submit" data-role="button" style="display:none;" data-desc="submit-caliboard" {{#isNotSupportPD ipcIdentification.hardware}}style="display: none;"{{/isNotSupportPD}}>确定标定描绘</a>
				<a href="#" class="href-btn" id="btn_calibration_start_confirm" data-role="button" data-desc="confirm">确定</a>
				<a href="#" class="href-btn" id="btn_calibration_start_cancel" data-role="button" data-desc="cancel">取消</a>
			</div>	
			<div id="btn-zoom-array" style="display:none;text-align:center;position: absolute;width: 100%;">
				<div  style="display:inline-block;">
					<a href="#" class="href-btn" id="btn-zoom-tele" data-role="button" data-desc="zoom-tele" data-btn-idx = "1" style="float:left;width:120px">放大</a>
					<a href="#" class="href-btn" id="btn-zoom-wide" data-role="button" data-desc="zoom-wide" data-btn-idx = "2" style="float:left;width:120px">缩小</a>
				</div>
				<div data-role="none" class="rangeinput" style="top:-20px;width:100%;text-align:center;">
					<p class="rangeinput_title" style="position:relative;margin-top:0px;top:10px;transform: translate(-20%, 0%);">{{getKeyLang "dig-zoom"}}<p>
					<label data-role="none" for="dig-zoom"></label>
					<input data-role="none"  class="rangeinput_input" type="range" id="dig-zoom" min="1.0" max="5.0" step="0.1" value="1.0" style="position:relative; top:0px">
					<p class="rangeinput_value" style="position:relative;margin-top:0px;margin-left:70%;right:0px;top:-70px;width:20px;"><p>
				</div>
				<div  style="display:inline-block;">
					<div >
						<a href="#" class="href-btn" id="btn-focus-far" data-role="button" data-desc="focus-far" data-btn-idx = "5" style="float:left;width:120px">远焦</a>
						<a href="#" class="href-btn" id="btn-focus-near" data-role="button" data-desc="focus-near" data-btn-idx = "6" style="float:left;width:120px">近焦</a>				
					</div>				
					<div >
						<a href="#" class="href-btn" id="btn-focus" data-role="button" data-desc="focus" data-btn-idx = "4" style="float:left;width:120px">对焦</a>		
						<a href="#" class="href-btn btn-popup" data-popup-type="ask-for-sure" data-popup-title="af-cal" id="btn-zoom-calibration" data-role="button" data-desc="zoom-cal" data-btn-idx = "3" style="float:left;width:120px">标定</a>
					</div>
				</div>	
				<div>
					<a href="#" id="btn-zoom-back" data-role="button" class="href-btn" data-desc="cancel" data-btn-idx = "7"style="width:120px;left: 50%;transform: translateX(-50%);">取消</a>
				</div>
			</div>
			<div {{{showHardware "ADA900V1"}}}>
				<div class="infomenu">
					<div><p data-desc="apc-statistic">统计</p></div>
					<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
						<tr>
							<th data-desc="apc-in" style="width:20%;">In</th>
							<th data-desc="apc-out" style="width:20%;">Out</th>
							<th data-desc="apc-rect" style="width:20%;">Rect</th>
							<th data-desc="apc-total" style="width:20%;">Total</th>
							<th data-desc="apc-calibrate" style="width:20%;">Calibrate</th>
						</tr>
						<tr>
							<td><li><input style="width:72%;" id="apc-in" data-role="none" type="number" class="mult_input1" value="0"></li></td>
							<td><li><input style="width:72%;" id="apc-out" data-role="none" type="number" class="mult_input2" value="0"></li></td>
							<td><li><input style="width:72%;" id="apc-rect" data-role="none" type="number" class="mult_input3" value="0" disabled="disabled"></li></td>
							<td><li><input style="width:72%;" id="apc-total" data-role="none" type="number" class="mult_input4" min="0" max="999" value="0"></li></td>
							<td><a style="width:70%;" id="btn-apc" href="#" class="ui-btn ui-corner-all">OK</a></td>
						</tr>
					</table>
				</div>
			</div>
		</script>
		</div>
		<div class='index_frs_group' style="display: none">
			<div style="height:10px"></div>
			<button id='testLoginBtn' class="btn" onfocus='this.blur();' style='width:100%;' data-desc="test-login">Test Login</button>
			<div style="height:10px"></div>
			<button id='newUserBtn' class="btn" onfocus='this.blur();' style='width:100%;'  data-desc="new-user">New User</button>
			<div style="height:10px"></div>
			<button id='deleteUserBtn' class="btn btn-popup" onfocus='this.blur();' style="width:100%;display: none;" data-popup-type="ask-for-sure" data-popup-title="batch-delete" data-desc="deleteUser">Delete User</button>
			<div class="deleteuser_checkbox" id = "div-checkall" style="display: none;">
				<p data-desc="all-select" style="right:50%">全选</p>
				<input data-role="none" class="smallcheckBtn custom" type="checkbox" id="check-all"/>
				<label data-role="none" for="check-all" style="right:45%"></label>
			</div>
			<div id="userList" style="width:100%;"></div>			
		</div>
			
		<form class="btn-sd-hd" style="display: none">
			<fieldset data-role="controlgroup" data-type="horizontal">
				<input type="radio" name="stream" id="hd-stream" class="btn-switch-stream" value="snap0.jpeg" checked="checked">
				<label for="hd-stream">HD</label>
				<input type="radio" name="stream" id="sd-stream" class="btn-switch-stream" value="snap1.jpeg">
				<label for="sd-stream">SD</label>
			</fieldset>
		</form>

		<!--<div><a href="test/index.html" data-theme="b" data-role="button" data-icon="carat-r">Goto Test</a></div>-->
		<!--div class="footer index_frs_group"  data-role="footer" data-position="fixed" data-mini="true" style="display: none;">
			<div data-role="footer" data-position="fixed" data-fullscreen="false">
			<div id="click-export-user" class="uploadBtn">
				<a id="export_user">
					<div>
						<label class="input-label"><span data-desc="click-export-faceid">Export FaceId</span></label>
					</div>
				</a>
			</div>
			<div id="click-import-user" class="uploadBtn">
				<div>
					<label class="input-label">
						<span data-desc="click-import-faceid">Import FaceId</span>
						<input type="file" id="import_user" name="importuser" title='open file'>
					</label>
				</div>
			</div>
			</div>
		</div-->

		<div data-role="popup" id="popup-show-image-param" data-dismissible="false">
			<a href="#" class="ui-btn ui-corner-all ui-shadow ui-icon-delete ui-btn-icon-notext ui-btn-right" data-rel="back">Close</a>
			<div role="main" class="ui-content">
				<div id="adjust-box">
					<div class="ui-field-contain">
						<label for="brightness" data-desc="brightness">Brightness</label>
						<input type="range" id="brightness" class="slider-image-param" min="0" max="100" data-highlight="true" value="50">
					</div>
					<div data-role="fieldcontain">
						<label for="contrast" data-desc="contrast">Contrast</label>
						<input type="range" id="contrast" class="slider-image-param" min="0" max="100" data-highlight="true" value="50">
					</div>
					<div data-role="fieldcontain">
						<label for="saturation" data-desc="saturation">Saturation</label>
						<input type="range" id="saturation" class="slider-image-param" min="0" max="100" data-highlight="true" value="50">
					</div>
					<div data-role="fieldcontain">
						<label for="sharpness" data-desc="sharpness">Sharpness</label>
						<input type="range" id="sharpness" class="slider-image-param" min="0" max="100" data-highlight="true" value="50">
					</div>
				</div>
			</div>
		</div>

		<div data-role="popup" id="popup-show-alg-param" data-dismissible="false">
			<a href="#" class="ui-btn ui-corner-all ui-shadow ui-icon-delete ui-btn-icon-notext ui-btn-right" data-rel="back">Close</a>
			<div role="main" class="ui-content">
				<table class="menu menu_a" rules="none" cellspacing="5%" style="margin-left:1px;width: 99%;">
					<td><li>
						<label for="FCT-Set-pdSensitivity" data-desc="pd-Sensitivity">pd Sensitivity</label>
					</li></td>
					<td><li>
						<select style="right: 10%; display: inline-block;" id="FCT-Set-pdSensitivity" data-role="none" placeholder="undefind">
							<option value=0 data-desc="sensitivity-low">Low</option>
							<option value=1 data-desc="sensitivity-medium">Medium</option>
							<option value=2 data-desc="sensitivity-high">High</option>
						</select>
					</li></td>			
				</table>
				<div data-role="none" class="input-switch-box" style="left:10%">
					<p data-desc="shelterAlarm" id="label-shelterEnable"  style="font-size: 16px;">遮挡报警开关</p>
					<input data-role="none" class="switchBtn custom" type="checkbox" id="FCT-shelterEnable">
					<label data-role="none" for="FCT-shelterEnable" style="left:60%"></label>
				</div>
			</div>
		</div>
		
		<a id="show-info" href="#popup-show-info" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
		<div data-role="popup" id="popup-show-info" data-dismissible="false">
			<div data-role="header">
				<h1 style="text-align:center;"></h1>
			</div>
			<div role="main" class="ui-content" align="center">
				<h3 style="text-align:center;min-width:200px"></h3>
				<p></p>
				<a href="#" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
			</div>
		</div>
		<a id="ask-for-sure" href="#popup-ask-for-sure" data-rel="popup" data-position-to="window" class="hide-anchor"></a>
		<div data-role="popup" id="popup-ask-for-sure" data-dismissible="false" data-popup-title="" align="center">
			<div data-role="header">
				<h1></h1>
			</div>
			<div role="main" class="ui-content">
				<h3></h3>
				<p></p>
				<a id="btn-ask-for-cancel" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-desc="cancel">取消</a>
				<a id="btn-ask-for-sure" class="ui-btn ui-corner-all ui-shadow ui-btn-inline" data-rel="back" data-transition="flow" data-desc="confirm">确定</a>
			</div>
		</div>	
	</div>
	
	</div>
	<script src="./js/handlebars.js"></script>
	<script src="./js/webapp-model.js"></script>
	<script src="./js/webapp-calibration-screen.js"></script>
	<script src="./js/webapp-common.js"></script>
	<script src="./js/webapp-view.js"></script>
	<script src="./js/webapp-ctrller.js"></script>
	<script type="text/template" id="userListTemplate">
		<!--each遍历json数组-->
		{{#each this}}
		<div id="{{Dir}}d1" class="username">
			<a id="{{Dir}}a1" class="username_user_img" style='background: transparent url("./{{Dir}}/img.jpg"); no-repeat;background-size: cover;'></a>
			<a id="{{Dir}}a2" class="username">{{Name}}</a>			
  			<a id="{{Dir}}a3" class="username_edit_img"></a>
			<a id="{{Dir}}a4" class="username_delete_img"></a>
			<div class="deleteuser_checkbox" style="display:none;">
				<input data-role="none" class="smallcheckBtn  delete_user" type="checkbox" id="{{Dir}}check1"/>
				<label data-role="none" for="{{Dir}}check1"></label>
			</div>
		</div>	      
		{{/each}}
	</script>

	<script type="text/javascript">
		$(document).ready(function(){
			var customer;
			var itemCustomer = window.location.host+"-customer";
			window.localStorage && (customer = window.localStorage.getItem(itemCustomer));
			console.log(customer);
			if(customer=="200032"){
				if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-mobilecommon.css"}).appendTo("head");
				}else {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-common.css"}).appendTo("head");
				}
			}
			else{
				if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/mobilecommon.css"}).appendTo("head");
				}else {
					$("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/common.css"}).appendTo("head");
				}
			}
			var streamType;
            var itemStreamType = window.location.host+"-streamType";
            window.localStorage && (streamType = window.localStorage.getItem(itemStreamType));
			streamType = streamType || 0;
			switchCssStyle(customer);
			var model = new WebappModel();
			var view = new WebappView(model, "preview");
			var controller = new WebappController(model, view);			
			controller.refreshConfigs(null,"only");		
			controller.startPreview(streamType);

			let ch = localStorage.getItem('ch')
			 ch=window.ch = ch ? ch : 'ch0';

			 const changeChSelect = document.getElementById("change_ch_select");
			 changeChSelect.value = ch;
			 changeChSelect.addEventListener("change", function () {
					window.ch = this.value;
				localStorage.setItem('ch',this.value)

					controller.refreshConfigs(null,"only");		
			controller.startPreview(streamType);
				});
		});
	</script>
</body>
</html>
