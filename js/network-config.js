/**
 * 网络配置文件
 * 用于控制WebUI的网络连接方式
 */
var NetworkConfig = {
    // 开发模式配置
    development: {
        enabled: true,  // 设置为true启用开发模式，false使用生产模式
        useProxy: true,  // 使用代理模式解决CORS问题
        deviceIP: "**************",  // 开发时指向的设备IP
        devicePort: "8080"  // 设备端口
    },
    
    // 生产模式配置（部署到设备上时使用）
    production: {
        // 生产模式下使用动态获取的IP和端口
        useDynamicHost: true
    },
    
    // 获取当前使用的主机地址
    getCurrentHost: function() {
        if (this.development.enabled) {
            if (this.development.useProxy) {
                // 代理模式：使用localhost
                return window.location.host;
            } else {
                // 直连模式：使用配置的设备IP
                return this.development.deviceIP + ":" + this.development.devicePort;
            }
        } else {
            // 生产模式：使用当前访问的地址
            return window.location.host;
        }
    },
    
    // 获取当前使用的主机名（不含端口）
    getCurrentHostname: function() {
        if (this.development.enabled) {
            return this.development.deviceIP;
        } else {
            return window.location.hostname;
        }
    },
    
    // 获取当前使用的端口
    getCurrentPort: function() {
        if (this.development.enabled) {
            return this.development.devicePort;
        } else {
            return window.location.port || "8080";
        }
    },
    
    // 构建完整的URL
    buildUrl: function(protocol, port, path) {
        protocol = protocol || "http";
        port = port || this.getCurrentPort();
        path = path || "";
        
        if (this.development.enabled) {
            return protocol + "://" + this.development.deviceIP + ":" + port + path;
        } else {
            var host = window.location.host;
            if (port !== window.location.port) {
                host = window.location.hostname + ":" + port;
            }
            return protocol + "://" + host + path;
        }
    },
    
    // 获取WebSocket URL
    getWebSocketUrl: function(port, path) {
        var protocol = this.development.enabled ? "ws" : 
                      (window.location.protocol === "https:" ? "wss" : "ws");
        return this.buildUrl(protocol, port, path);
    },
    
    // 获取HTTPS WebSocket URL
    getSecureWebSocketUrl: function(port, path) {
        var protocol = this.development.enabled ? "wss" : 
                      (window.location.protocol === "https:" ? "wss" : "ws");
        return this.buildUrl(protocol, port, path);
    },
    
    // 获取API URL - 专门用于Ajax请求
    getApiUrl: function(path) {
        path = path || "";
        if (path.charAt(0) !== "/") {
            path = "/" + path;
        }
        
        if (this.development.enabled && this.development.useProxy) {
            // 代理模式：使用/api前缀
            return "/api" + path;
        } else {
            // 直连模式或生产模式
            return this.buildUrl("http", this.getCurrentPort(), path);
        }
    },
    
    // 检查是否为file://协议
    isFileProtocol: function() {
        return window.location.protocol === 'file:';
    },
    
    // 获取开发提示信息
    getDevTips: function() {
        if (this.isFileProtocol() && this.development.enabled) {
            return {
                hasWarning: true,
                message: "检测到使用file://协议访问，可能会遇到CORS问题。建议使用HTTP服务器运行页面。",
                solution: "运行 start_server.bat 或使用 python -m http.server 8000 启动本地服务器"
            };
        }
        return { hasWarning: false };
    }
};

// 在控制台输出当前配置状态
console.log("Network Config Status:", {
    mode: NetworkConfig.development.enabled ? "Development" : "Production",
    host: NetworkConfig.getCurrentHost(),
    hostname: NetworkConfig.getCurrentHostname(),
    port: NetworkConfig.getCurrentPort()
}); 