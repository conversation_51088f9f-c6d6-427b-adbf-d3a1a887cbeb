//显示遮罩
function showCoverDiv(){
	var mybg = document.getElementById("mybg");
	if(mybg){
		mybg.style.display = "block";
	}else{
	    var procbg = document.createElement("div"); //首先创建一个div
		procbg.setAttribute("id","mybg"); //定义该div的id
		procbg.style.background = "#000000";
		procbg.style.width = "100%";
		procbg.style.height = "100%";
		procbg.style.position = "fixed";
		procbg.style.top = "0";
		procbg.style.left = "0";
		procbg.style.zIndex = "500";
		procbg.style.opacity = "0.6";
		procbg.style.filter = "Alpha(opacity=70)";
		document.body.appendChild(procbg);
	}
}

//取消遮罩
function hideCoverDiv() {
    var mybg = document.getElementById("mybg");
    if(mybg)
        mybg.style.display = "none";
}

function initDialog(prefix,title){
	if(!document.getElementById("dialogLink"))
	{
		var link = document.createElement("link");
		link.setAttribute("id","dialogLink");
		link.rel="stylesheet";
		link.type="text/css";
		link.href="css/dialog.css?ver=1";
		document.head.appendChild(link);
	}

	var dialog_div = document.getElementById(prefix+"_dialog");
	if(dialog_div){
		document.body.removeChild(dialog_div);
	}

	dialog_div = document.createElement("div");
	dialog_div.setAttribute("id",prefix+"_dialog"); //定义该div的id
	dialog_div.setAttribute("class","dialog"); 
	var dialog_p = document.createElement("p");
	dialog_p.setAttribute("id",prefix+"_title");
    dialog_p.setAttribute("class","dialog_title");
	dialog_p.innerHTML=title;
	dialog_div.appendChild(dialog_p);
	
	return dialog_div;
}

function createDialogButtonBox(prefix,rightbtn_text,rightbtn_fun,leftbtn_text,leftbtn_fun){
	var btns_div = document.createElement("div");

	var dialog_rightbtn = document.createElement("a");
	dialog_rightbtn.setAttribute("id",prefix+"_rightbtn");
	dialog_rightbtn.setAttribute("class","dialog-btn");
	dialog_rightbtn.onfocus="this.blur();";
	dialog_rightbtn.innerHTML=rightbtn_text;
	dialog_rightbtn.addEventListener("click",rightbtn_fun);
	btns_div.appendChild(dialog_rightbtn);
	

	if(leftbtn_text){
		var dialog_leftbtn = document.createElement("a");
		dialog_leftbtn.setAttribute("id",prefix+"_leftbtn");
		dialog_leftbtn.setAttribute("class","dialog-cancelbtn");
		dialog_leftbtn.onfocus="this.blur();";
		dialog_leftbtn.innerHTML=leftbtn_text;
		dialog_leftbtn.addEventListener("click",leftbtn_fun);
		btns_div.appendChild(dialog_leftbtn);
	}

	return btns_div;
}

function removeDialog(prefix){
	var dialog_div = document.getElementById(prefix+"_dialog");
	if(dialog_div){
		document.body.removeChild(dialog_div);
	}
}

function createNormalDialog(title,btn_texts,btn_clicks){
	showCoverDiv();
	var prefix="normalDialog";
	var dialog_div = initDialog(prefix,title);

	var btns_div = createDialogButtonBox(
		prefix,
		btn_texts[0],
		function(e){
			btn_clicks[0]();
			this.removeEventListener(e.type,arguments.callee,false);//接触本次函数的绑定，不然没用一次弹窗，这按键都会多个处理函数
		},
		btn_texts[1],
		function(e){
			btn_clicks[1]();
			this.removeEventListener(e.type,arguments.callee,false);//接触本次函数的绑定，不然没用一次弹窗，这按键都会多个处理函数
		}
	);

	dialog_div.appendChild(btns_div);
	document.body.appendChild(dialog_div);
}

function removeNormalDialog(hidecover){
	removeDialog("normalDialog");
	if(hidecover)
        hideCoverDiv();
}


function createInputDialog(title,btn_texts,btn_clicks){
	showCoverDiv();
	var prefix="inputDialog";
	var dialog_div = initDialog(prefix,title);

	var dialog_input = document.createElement("input");
	dialog_input.setAttribute("id",prefix+"_input");
    dialog_input.setAttribute("maxlength",16);
	dialog_input.style.marginLeft="10px";
	dialog_input.style.marginBottom="10px";
	dialog_div.appendChild(dialog_input);

	var btns_div = createDialogButtonBox(
		prefix,
		btn_texts[0],
		function(e){
			if(dialog_input.value)
				btn_clicks[0](dialog_input.value);
			else
				btn_clicks[0]("valueIsEmpty");
			this.removeEventListener(e.type,arguments.callee,false);//接触本次函数的绑定，不然没用一次弹窗，这按键都会多个处理函数
		},
		btn_texts[1],
		function(e){
			btn_clicks[1]();
			this.removeEventListener(e.type,arguments.callee,false);//接触本次函数的绑定，不然没用一次弹窗，这按键都会多个处理函数
		}
	);

	dialog_div.appendChild(btns_div);
	document.body.appendChild(dialog_div);
}

function removeInputDialog(hidecover){
	removeDialog("inputDialog");
	if(hidecover)
        hideCoverDiv();
}

