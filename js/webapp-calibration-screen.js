/**
 * 绘制进行二维码标定时的标定框
 *
 * @param {canvas} draw_canvas - canvas html 画布对象
 * @param {number} mode - 模式
 */
var CalibrationBoard = function (draw_canvas, mode) {
	/* 标定框 */
	this.mode = mode; //测距模式mode=2，画两个框；跟踪模式mode=1，画1个框
	this.draw_canvas = draw_canvas;
	this.downEnable = true;
	this.calibrationBoardPos = {};
	this.calibrationBoardPos.box1_start_x;
	this.calibrationBoardPos.box1_start_y;
	this.calibrationBoardPos.box1_end_x;
	this.calibrationBoardPos.box1_end_y;
	this.calibrationBoardPos.box2_start_x;
	this.calibrationBoardPos.box2_start_y;
	this.calibrationBoardPos.box2_end_x;
	this.calibrationBoardPos.box2_end_y;
	this.offsettop = $("#win-img").offset().top; //滚动后会有y坐标的偏差，用于修正鼠标坐标
	this.createCalibrationBoard();
};

CalibrationBoard.prototype = {
	createCalibrationBoard: function () {
		var thiz = this;
		var draw_canvas = this.draw_canvas;
		var w = document.getElementById("img-solo").width,
			h = document.getElementById("img-solo").height;
		var offsetLeft = document.getElementById("img-solo").offsetLeft;
		draw_canvas.width = w;
		draw_canvas.height = h;
		draw_canvas.style.left = offsetLeft + "px";
		var video_div = draw_canvas;
		var ctx = draw_canvas.getContext("2d");
		ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);
		var downPos = {}; //记录鼠标点击的位置
		var flag = 0;
		var isdown = false;
		function refreshTestLines() {
			var ctx = draw_canvas.getContext("2d");
			ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);

			var point1_x;
			var point2_x;
			var point1_y;
			var point2_y;

			if (flag == 1 && thiz.mode == 2) {
				//绘画第一个框
				point1_x = thiz.calibrationBoardPos.box1_start_x * draw_canvas.width;
				point1_y = thiz.calibrationBoardPos.box1_start_y * draw_canvas.height;
				point2_x = thiz.calibrationBoardPos.box1_end_x * draw_canvas.width;
				point2_y = thiz.calibrationBoardPos.box1_end_y * draw_canvas.height;
				console.log(
					point1_x + "," + point1_y + "," + point2_x + "," + point2_y
				);
				ctx.strokeStyle = "#FF0000";
				ctx.beginPath();
				ctx.lineWidth = 3;
				ctx.strokeRect(
					point1_x,
					point1_y,
					point2_x - point1_x,
					point2_y - point1_y
				);
			}
			point1_x = downPos.start_x * draw_canvas.width;
			point1_y = downPos.start_y * draw_canvas.height;
			point2_x = downPos.end_x * draw_canvas.width;
			point2_y = downPos.end_y * draw_canvas.height;

			ctx.strokeStyle = "#FF0000";
			ctx.beginPath();
			ctx.lineWidth = 3;
			ctx.strokeRect(
				point1_x,
				point1_y,
				point2_x - point1_x,
				point2_y - point1_y
			);
		}

		function downEvent(pos_x, pos_y) {
			if (!thiz.downEnable) {
				return;
			}
			downPos.start_x = (pos_x * 1.0) / draw_canvas.clientWidth;
			downPos.start_y = (pos_y * 1.0) / draw_canvas.clientHeight;
			if (flag == 0) {
				thiz.calibrationBoardPos.box1_start_x = downPos.start_x;
				thiz.calibrationBoardPos.box1_start_y = downPos.start_y;
			} else if (flag == 1 && thiz.mode == 2) {
				//只有测距模式才会画两个框
				thiz.calibrationBoardPos.box2_start_x = downPos.start_x;
				thiz.calibrationBoardPos.box2_start_y = downPos.start_y;
			}
			//refreshTestLines() ;
		}

		function moveEvent(pos_x, pos_y) {
			if (!thiz.downEnable) {
				return;
			}
			downPos.end_x = (pos_x * 1.0) / draw_canvas.clientWidth;
			downPos.end_y = (pos_y * 1.0) / draw_canvas.clientHeight;
			if (flag == 0) {
				thiz.calibrationBoardPos.box1_end_x = downPos.end_x;
				thiz.calibrationBoardPos.box1_end_y = downPos.end_y;
			} else if (flag == 1 && thiz.mode == 2) {
				thiz.calibrationBoardPos.box2_end_x = downPos.end_x;
				thiz.calibrationBoardPos.box2_end_y = downPos.end_y;
			}
			refreshTestLines();
		}

		function upEvent() {
			if (!thiz.downEnable) {
				return;
			}
			flag++;
			if (flag == 2) flag = 0;
			isdown = false;
		}

		// PC 端
		video_div.onmousedown = function (e) {
			isdown = true;
			var doe = document.documentElement || document.body,
				scrollY = window.scrollY || doe.scrollTop; //滚动条滚动的距离
			var ev = e || window.event;
			var mouseY =
				ev.clientY +
				scrollY -
				document.getElementById("cam-win-1x").offsetTop -
				($("#win-img").offset().top - thiz.offsettop); //firefox不支持offsetY，所以用通过这样算出
			var mouseX = ev.clientX - offsetLeft;
			//console.log(mouseX,mouseY);
			downEvent(mouseX, mouseY);
		};
		video_div.onmouseup = function (e) {
			upEvent();
		};
		video_div.onmouseout = function (e) {
			upEvent();
		};
		video_div.onmousemove = function (e) {
			if (!isdown) return;
			var doe = document.documentElement || document.body,
				scrollY = window.scrollY || doe.scrollTop; //滚动条滚动的距离
			var ev = e || window.event;
			var mouseY =
				ev.clientY +
				scrollY -
				document.getElementById("cam-win-1x").offsetTop -
				($("#win-img").offset().top - thiz.offsettop); //firefox不支持offsetY，所以用通过这样算出
			var mouseX = ev.clientX - offsetLeft;
			moveEvent(mouseX, mouseY);
		};

		//移动端
		//阻止浏览器的默认行为
		function stopDefault(e) {
			if (e && e.preventDefault) e.preventDefault(); //阻止默认浏览器动作(W3C)
			else window.event.returnValue = false; //IE中阻止函数器默认动作的方式
			return false;
		}
		video_div.ontouchstart = function (e) {
			isdown = true;
			var ev = e || window.event;
			var mouseY =
				e.touches[0].pageY -
				document.getElementById("cam-win-1x").offsetTop -
				($("#win-img").offset().top - thiz.offsettop); //firefox不支持offsetY，所以用通过这样算出
			var mouseX = e.touches[0].pageX - offsetLeft;
			downEvent(mouseX, mouseY);
			if (isdown) stopDefault(e);
		};
		video_div.ontouchend = function (e) {
			upEvent();
			stopDefault(e);
		};
		video_div.ontouchcancel = function (e) {
			upEvent();
			stopDefault(e);
		};
		video_div.ontouchmove = function (e) {
			if (!isdown) return;
			stopDefault(e);
			var ev = e || window.event;
			var mouseY =
				e.touches[0].pageY -
				document.getElementById("cam-win-1x").offsetTop -
				($("#win-img").offset().top - thiz.offsettop); //firefox不支持offsetY，所以用通过这样算出
			var mouseX = e.touches[0].pageX - offsetLeft;
			moveEvent(mouseX, mouseY);
		};

		var ctx0 = draw_canvas.getContext("2d");
		ctx0.clearRect(0, 0, draw_canvas.clientWidth, draw_canvas.clientHeight);
		//this.refreshCal = refreshTestLines;
		moveEvt = moveEvent;
		return this;
	},
	getCalibrationBoardPonits: function () {
		var ret = new Array();
		for (var i = 1; i <= this.mode; i++) {
			ret[4 * (i - 1)] = this.calibrationBoardPos["box" + i + "_start_x"] * 1;
			ret[4 * (i - 1) + 1] =
				this.calibrationBoardPos["box" + i + "_start_y"] * 1;
			ret[4 * (i - 1) + 2] = this.calibrationBoardPos["box" + i + "_end_x"] * 1;
			ret[4 * (i - 1) + 3] = this.calibrationBoardPos["box" + i + "_end_y"] * 1;
		}
		return ret;
	},
	reviseMouseX: function (arg) {
		this.widthpading = arg;
	},
	quit: function () {
		var ctx = this.draw_canvas.getContext("2d");
		ctx.clearRect(0, 0, this.draw_canvas.width, this.draw_canvas.height);
		this.downEnable = false;
	},
	clearColor: function () {
		var ctx = this.draw_canvas.getContext("2d");
		ctx.clearRect(0, 0, this.draw_canvas.width, this.draw_canvas.height);
	},
};

/**
 * 绘制A32/A32IR的ROi标定区域
 * @param {string} calibration_project - 标定项目类型 ADA32 ADA32IR ......
 * @param {string} customer - 消费者客户号
 * @param {canvas} draw_canvas - canvas html 画布对象
 * @param {number} mode - 模式
 */
var CalibrationScreen = function (calibration_project, customer, draw_canvas) {
	this.widthpading = 0;
	this.moveEvt = null;
	this.calibration_project = calibration_project;
	this.customer = customer;
	this.draw_canvas = draw_canvas;
	this.downEnable = false;
	this.freemode = false; //非自由模式，左边的点限制在同一直线上，右边的点限制在同一直线上
	this.roiShowLevel = 0; //0:全显示 1：显示红黄 2：显示红
	this.drawEnable = 0; //只画roi 或者 只画hollow
	this.lang;
	//if(customer && (customer == "200598"))
	//{
	//	this.freemode = true;
	//}

	/*              矩形标定                */
	this.points = {};
	this.points.sv_dbl_point11_x = 0.2;
	this.points.sv_dbl_point11_y = 0.5;
	this.points.sv_dbl_point12_x = 0.2;
	this.points.sv_dbl_point12_y = 0.7;
	this.points.sv_dbl_point13_x = 0.2;
	this.points.sv_dbl_point13_y = 0.9;
	this.points.sv_dbl_point14_x = 0.2;
	this.points.sv_dbl_point14_y = 1.0;
	this.points.sv_dbl_point21_x = 0.8;
	this.points.sv_dbl_point21_y = 0.5;
	this.points.sv_dbl_point22_x = 0.8;
	this.points.sv_dbl_point22_y = 0.7;
	this.points.sv_dbl_point23_x = 0.8;
	this.points.sv_dbl_point23_y = 0.9;
	this.points.sv_dbl_point24_x = 0.8;
	this.points.sv_dbl_point24_y = 1.0;
	/* ROI类型:0 水平梯形，1：竖直版本(左红)，2：竖直版本(右红)，3：半圆，4：椭圆 */
	this.points.sv_int_state = 0; //可选 0 1 2 3 4

	this.roi_limit = {};
	this.roi_limit.left = 0.0;
	this.roi_limit.right = 1.0;
	this.roi_limit.top = 0.0;
	this.roi_limit.bottom = 1.0;

	/*   抛物线 1/2 短轴长度 */
	this.pdMinorAxis = {};
	this.pdMinorAxis[0] = 0.1;
	this.pdMinorAxis[1] = 0.1;
	this.pdMinorAxis[2] = 0.1;

	this.normal_color = "#FFFFFF";
	this.choice_color = "#007F7F";
	this.green_color = "#00FF00";
	this.yellow_color = "#FFFF00";
	this.red_color = "#FF0000";

	this.original_colors = {};
	this.original_colors.horizontal_line1_color = "#0000FF";
	this.original_colors.horizontal_line2_color = "#0000FF";
	this.original_colors.horizontal_line3_color = "#0000FF";
	this.original_colors.line1_color = this.normal_color;
	this.original_colors.line2_color = this.normal_color;
	this.original_colors.line3_color = this.normal_color;
	this.original_colors.line4_color = this.normal_color;

	this.colors = {};
	this.colors.horizontal_line1_color = "#0000FF";
	this.colors.horizontal_line2_color = "#0000FF";
	this.colors.horizontal_line3_color = "#0000FF";
	this.colors.line1_color = this.normal_color;
	this.colors.line2_color = this.normal_color;
	this.colors.line3_color = this.normal_color;
	this.colors.line4_color = this.normal_color;

	this.movingLine = 0; //侧边使用
	this.movingLines = {}; //A32前方标定使用的
	this.movingLines.line1 = 0;
	this.movingLines.line2 = 0;
	this.movingLines.line3 = 0;
	this.movingLines.line4 = 0;
	this.movingLines.line5 = 0; // sv_int_stat =4 时记录镂空边框的一个拖拽线

	this.movingPoints = {}; //A32前方标定使用的
	this.movingPoints.point1 = 0;
	this.movingPoints.point2 = 0;
	this.movingPoints.point3 = 0;
	this.movingPoints.point4 = 0;

	this.hollow = {}; //用于roi剪裁镂空
	this.hollow.enable = 0;
	this.hollow.style = 0; //剪裁类型 0:矩形  1：任意图形
	this.hollow.points_num = 10;
	this.hollow.points = [];
	var _init_xlist = [0.2, 0.4, 0.6, 0.8, 0.8, 0.8, 0.6, 0.4, 0.2, 0.2];
	var _init_ylist = [0.4, 0.4, 0.4, 0.4, 0.5, 0.6, 0.6, 0.6, 0.6, 0.5];

	for (var j = 0; j < this.hollow.points_num; j++ /* 10 个点 */) {
		this.hollow.points.push({});
		this.hollow.points[j].x = _init_xlist[j];
		this.hollow.points[j].y = _init_ylist[j]; //左上角开始，顺时针
	}

	this.hollow.roi_color = ["#00FF00", "#FFFF00", "#FF0000"]; // 绿色，黄色，红色
	this.hollow.smooth_scale = [0.0, 0.0, 0.0]; // 光滑系数
	this.hollow.points_is_dragging = Array(this.hollow.points_num).fill(false); // 标记哪个点被选中

	this.refreshCal = null;

	this.createCalibrationScreen();
};

CalibrationScreen.prototype = {
	createCalibrationScreen: function () {
		var thiz = this;
		var w = document.getElementById("img-solo").width,
			h = document.getElementById("img-solo").height;
		var offsetLeft = document.getElementById("img-solo").offsetLeft;
		var draw_canvas = this.draw_canvas;
		draw_canvas.width = w;
		draw_canvas.height = h;
		draw_canvas.style.setProperty("width", w + "px");
		draw_canvas.style.left = offsetLeft + "px";
		var video_div = draw_canvas;
		var ctx = draw_canvas.getContext("2d");
		ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);
		ctx.strokeStyle = "#FF0000";
		ctx.beginPath();
		ctx.lineWidth = 3;
		ctx.moveTo(0, 0);
		ctx.lineTo(0, draw_canvas.height);
		ctx.lineTo(draw_canvas.width, draw_canvas.height);
		ctx.lineTo(draw_canvas.width, 0);
		ctx.lineTo(0, 0);
		ctx.stroke();

		var trigger_d = 20;
		var trigger_r = 10;
		var isdown = false;

		var horizontal_line1;
		var horizontal_line2;
		var radius_scope = 10;

		var downPos = {}; //记录鼠标点击的位置
		downPos.x = 0;
		downPos.y = 0;
		var movingPoint = 0;

		if (
			(this.calibration_project != null &&
				this.calibration_project.indexOf("H-1.45") != -1) ||
			(this.calibration_project != null &&
				this.calibration_project.indexOf("H-1.99") != -1)
		) {
			//OW
			roiShowLevel = 2;
		}

		if (this.customer == "200032") {
			//LUIS版
			roiShowLevel = 1;
		}

		/* 计算dest点是否在以src为中心，trigger_r为半径的圆上 */
		function contrastXYpos(src_pos, dest_pos) {
			var d1 = dest_pos - src_pos;
			if (d1 < trigger_r && d1 > -trigger_r) return true;
			else return false;
		}

		function getDistance(x1, y1, x2, y2) {
			let dep = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));
			return dep;
		}

		/* 更新标定线 */
		function refreshCalibrationLines() {
			var ctx = draw_canvas.getContext("2d");
			ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);
			var point1_x;
			var point2_x;
			var point1_y;
			var point2_y;

			/*       画线，并标明端点    */
			function paint_line(
				point1_x,
				point1_y,
				point2_x,
				point2_y,
				linewidth,
				color
			) {
				// 绘制线段
				ctx.strokeStyle = color;
				ctx.beginPath();
				ctx.lineWidth = linewidth;
				ctx.moveTo(point1_x, point1_y);
				ctx.stroke();

				ctx.lineTo(point2_x, point2_y);
				ctx.stroke();

				//绘制点1
				ctx.beginPath();
				ctx.lineWidth = 3;
				ctx.arc(point1_x, point1_y, trigger_d / 3, 0, Math.PI * 2, true); //画出圆形
				ctx.fillStyle = "rgba(0,0,255,0.2)";
				ctx.closePath();
				ctx.stroke();
				ctx.fill();

				//绘制点2
				ctx.beginPath();
				ctx.lineWidth = 3;
				//var xx = (point2_x - point1_x) / (point2_y - point1_y) * (horizontal_line2 - point2_y) + point2_x;
				ctx.arc(point2_x, point2_y, trigger_d / 3, 0, Math.PI * 2, true);
				ctx.fillStyle = "rgba(0,0,255,0.2)";
				ctx.closePath();
				ctx.stroke();
				ctx.fill();
			}

			/*       画线，没有拖拽点    */
			function paint_line2(
				point1_x,
				point1_y,
				point2_x,
				point2_y,
				linewidth,
				color
			) {
				// 绘制线段
				ctx.strokeStyle = color;
				ctx.beginPath();
				ctx.lineWidth = linewidth;
				ctx.moveTo(point1_x, point1_y);
				ctx.stroke();

				ctx.lineTo(point2_x, point2_y);
				ctx.stroke();
			}

			/* 绘制椭圆 */
			function drawEllipse(x, y, width, height) {
				var k = width / 0.75 / 2,
					w = width / 2,
					h = height / 2;
				ctx.beginPath();
				ctx.moveTo(x, y - h);
				ctx.bezierCurveTo(x + k, y - h, x + k, y + h, x, y + h);
				ctx.bezierCurveTo(x - k, y + h, x - k, y - h, x, y - h);
				ctx.closePath();
				ctx.stroke();

				ctx.beginPath();
				ctx.lineWidth = 3;
				ctx.arc(x, y + h, trigger_d / 3, 0, Math.PI * 2, true);
				ctx.fillStyle = "rgba(0,0,255,0.2)";
				ctx.closePath();
				ctx.stroke();
				ctx.fill();
			}

			function paint_3bezier_curve(point1, point2, point3, point4, color) {
				ctx.beginPath();
				ctx.lineWidth = 2;
				ctx.strokeStyle = color;
				ctx.moveTo(point1.x, point1.y);
				ctx.bezierCurveTo(
					point2.x,
					point2.y,
					point3.x,
					point3.y,
					point4.x,
					point4.y
				);
				ctx.stroke();
			}

			function paint_3bezier_fit(rawpoints, points_num, scale, color) {
				var i, nexti, backi;

				var points = [];
				var pCenter = [];
				var pExtra = [];
				var stmidnmid = {};
				var offsetx, offsety, addx, addy, extraindex;

				for (i = 0; i < points_num; i++) points.push({});

				for (i = 0; i < points_num; i++) pCenter.push({});

				for (i = 0; i < 2 * points_num; i++) pExtra.push({});

				for (i = 0; i < points_num; i++) {
					points[i].x = parseInt(rawpoints[i].x * draw_canvas.width);
					points[i].y = parseInt(rawpoints[i].y * draw_canvas.height);
				}

				for (i = 0; i < points_num; i++) {
					nexti = (i + 1) % points_num;
					pCenter.push({});
					pCenter[i].x = (points[i].x + points[nexti].x) / 2;
					pCenter[i].y = (points[i].y + points[nexti].y) / 2;

					pCenter[i].x = parseInt(pCenter[i].x);
					pCenter[i].y = parseInt(pCenter[i].y);
				}

				for (i = 0; i < points_num; i++) {
					nexti = (i + 1) % points_num;
					backi = (i - 1 + points_num) % points_num;

					stmidnmid.x = (pCenter[i].x + pCenter[backi].x) / 2;
					stmidnmid.y = (pCenter[i].y + pCenter[backi].y) / 2;

					offsetx = points[i].x - stmidnmid.x;
					offsety = points[i].y - stmidnmid.y;

					extraindex = 2 * i;
					pExtra[extraindex].x = pCenter[backi].x + offsetx;
					pExtra[extraindex].y = pCenter[backi].y + offsety;
					addx = (pExtra[extraindex].x - points[i].x) * scale;
					addy = (pExtra[extraindex].y - points[i].y) * scale;

					pExtra[extraindex].x = parseInt(points[i].x + addx);
					pExtra[extraindex].y = parseInt(points[i].y + addy);

					extraindex = (extraindex + 1) % (2 * points_num);
					pExtra[extraindex].x = pCenter[i].x + offsetx;
					pExtra[extraindex].y = pCenter[i].y + offsety;
					addx = (pExtra[extraindex].x - points[i].x) * scale;
					addy = (pExtra[extraindex].y - points[i].y) * scale;
					pExtra[extraindex].x = parseInt(points[i].x + addx);
					pExtra[extraindex].y = parseInt(points[i].y + addy);
				}

				for (i = 0; i < points_num; i++) {
					var point1 = points[i];
					var point2 = pExtra[2 * i + 1];
					var point3 = pExtra[(2 * i + 2) % (2 * points_num)];
					var point4 = points[(i + 1) % points_num];

					paint_3bezier_curve(point1, point2, point3, point4, color);
				}
			}

			function paint_circle(point_center, radius, color) {
				ctx.beginPath();
				ctx.lineWidth = 2;
				ctx.strokeStyle = color;
				ctx.arc(point_center.x, point_center.y, radius, 0, 2 * Math.PI);
				ctx.stroke();
			}

			function paint_drawboard() {
				var i;
				var scale, points_num, points, color;

				scale = thiz.hollow.smooth_scale[0];
				points_num = thiz.hollow.points_num;
				points = thiz.hollow.points;
				color = "#FFFFFF";

				paint_3bezier_fit(points, points_num, scale, color);

				/* 绘制圆圈 */
				for (i = 0; i < points_num; i++) {
					var point_center = {};
					point_center.x = parseInt(draw_canvas.width * points[i].x);
					point_center.y = parseInt(draw_canvas.height * points[i].y);
					paint_circle(point_center, radius_scope, color);
				}
			}

			if (thiz.points.sv_int_state == 0 || thiz.points.sv_int_state == 4) {
				//水平版本
				if (
					(thiz.calibration_project != null &&
						thiz.calibration_project.indexOf("H-1.45") != -1) ||
					(thiz.calibration_project != null &&
						thiz.calibration_project.indexOf("H-1.99") != -1)
				) {
					ctx.strokeStyle = "#FF0000";
					ctx.beginPath();
					ctx.lineWidth = 3;
					ctx.moveTo(
						thiz.roi_limit.left * draw_canvas.width,
						thiz.roi_limit.top * draw_canvas.height
					);
					ctx.lineTo(
						thiz.roi_limit.right * draw_canvas.width,
						thiz.roi_limit.top * draw_canvas.height
					);
					ctx.lineTo(
						thiz.roi_limit.right * draw_canvas.width,
						thiz.roi_limit.bottom * draw_canvas.height
					);
					ctx.lineTo(
						thiz.roi_limit.left * draw_canvas.width,
						thiz.roi_limit.bottom * draw_canvas.height
					);
					ctx.lineTo(
						thiz.roi_limit.left * draw_canvas.width,
						thiz.roi_limit.top * draw_canvas.height
					);
					ctx.stroke();
				}

				if (thiz.points.sv_int_state == 4) {
					for (var j = 3; j > 0; j--) {
						point1_x =
							thiz.points["sv_dbl_point1" + j + "_x"] * draw_canvas.width;
						point1_y =
							thiz.points["sv_dbl_point1" + j + "_y"] * draw_canvas.height;
						point2_x =
							thiz.points["sv_dbl_point2" + j + "_x"] * draw_canvas.width;
						point2_y =
							thiz.points["sv_dbl_point2" + j + "_y"] * draw_canvas.height;
						var h = draw_canvas.height * thiz.pdMinorAxis[j - 1];
						ctx.strokeStyle = thiz.colors["line" + j + "_color"];
						drawEllipse(
							(point1_x + point2_x) / 2,
							point2_y,
							point2_x - point1_x,
							h * 2
						);
						ctx.clearRect(
							point1_x,
							point1_y - 1.5 * h,
							point2_x - point1_x,
							h * 1.5
						);
					}
				}

				if (1) {
					for (var j = 1; j <= 4; j++) {
						if (thiz.roiShowLevel > 0) {
							if (j == 2 && thiz.roiShowLevel > 0) {
								continue;
							} else if (j == 3 && thiz.roiShowLevel > 1) {
								continue;
							}
						}

						if (thiz.calibration_project == "ada39" && j == 3) {
							continue;
						}
						//确定一条直线的两个端点
						point1_x =
							thiz.points["sv_dbl_point1" + j + "_x"] * draw_canvas.width;
						point1_y =
							thiz.points["sv_dbl_point1" + j + "_y"] * draw_canvas.height;
						point2_x =
							thiz.points["sv_dbl_point2" + j + "_x"] * draw_canvas.width;
						point2_y =
							thiz.points["sv_dbl_point2" + j + "_y"] * draw_canvas.height;
						if (thiz.calibration_project == "ada39") {
							if (j != 2) {
								ctx.strokeStyle = normal_color;
							} else {
								ctx.strokeStyle = thiz.colors["line" + j + "_color"];
							}
							ctx.beginPath();
							ctx.lineWidth = 3;
							ctx.moveTo(point1_x, point1_y);

							ctx.lineTo(point2_x, point2_y);
							ctx.stroke();

							ctx.strokeStyle = thiz.colors["line" + j + "_color"];
							if (j == 1) {
								ctx.beginPath();
								ctx.lineWidth = 3;
								ctx.arc(
									point1_x,
									point1_y,
									trigger_d / 3,
									0,
									Math.PI * 2,
									true
								);
								ctx.fillStyle = "rgba(0,0,255,0.2)";
								ctx.closePath();
								ctx.stroke();
								ctx.fill();
							} else if (j == 4) {
								ctx.beginPath();
								ctx.lineWidth = 3;
								ctx.arc(
									point2_x,
									point2_y,
									trigger_d / 3,
									0,
									Math.PI * 2,
									true
								);
								ctx.fillStyle = "rgba(0,0,255,0.2)";
								ctx.closePath();
								ctx.stroke();
								ctx.fill();
							}
							continue;
						}

						//画直线
						ctx.strokeStyle = thiz.colors["line" + j + "_color"];
						ctx.beginPath();
						ctx.lineWidth = 3;
						ctx.moveTo(point1_x, point1_y);
						ctx.lineTo(point2_x, point2_y);
						ctx.stroke();

						//画左边的点
						ctx.beginPath();
						ctx.lineWidth = 3;
						ctx.arc(point1_x, point1_y, trigger_d / 3, 0, Math.PI * 2, true);
						ctx.fillStyle = "rgba(0,0,255,0.2)";
						ctx.closePath();
						ctx.stroke();
						ctx.fill();
						//画右边的点
						ctx.beginPath();
						ctx.lineWidth = 3;
						ctx.arc(point2_x, point2_y, trigger_d / 3, 0, Math.PI * 2, true);
						ctx.fillStyle = "rgba(0,0,255,0.2)";
						ctx.closePath();
						ctx.stroke();
						ctx.fill();
					}
				}

				//画侧边的线（腰),每条侧边线分三段（红黄绿）
				for (var j = 1; j < 4; j++) {
					var jj = j + 1;
					var point1s_x =
						thiz.points["sv_dbl_point1" + j + "_x"] * draw_canvas.width;
					var point1s_y =
						thiz.points["sv_dbl_point1" + j + "_y"] * draw_canvas.height;
					var point1e_x =
						thiz.points["sv_dbl_point1" + jj + "_x"] * draw_canvas.width;
					var point1e_y =
						thiz.points["sv_dbl_point1" + jj + "_y"] * draw_canvas.height;
					var point2s_x =
						thiz.points["sv_dbl_point2" + j + "_x"] * draw_canvas.width;
					var point2s_y =
						thiz.points["sv_dbl_point2" + j + "_y"] * draw_canvas.height;
					var point2e_x =
						thiz.points["sv_dbl_point2" + jj + "_x"] * draw_canvas.width;
					var point2e_y =
						thiz.points["sv_dbl_point2" + jj + "_y"] * draw_canvas.height;

					if (thiz.roiShowLevel > 0) {
						if (jj == 2) {
							if (thiz.roiShowLevel == 1) {
								ctx.strokeStyle = thiz.colors["line" + 3 + "_color"];
							} else {
								ctx.strokeStyle = thiz.colors["line" + 4 + "_color"];
							}
						} else if (jj == 3) {
							if (thiz.roiShowLevel > 1) {
								ctx.strokeStyle = thiz.colors["line" + 4 + "_color"];
							} else {
								ctx.strokeStyle = thiz.colors["line" + 3 + "_color"];
							}
						} else {
							ctx.strokeStyle = thiz.colors["line" + 4 + "_color"];
						}
					} else {
						ctx.strokeStyle = thiz.colors["line" + jj + "_color"];
					}

					ctx.beginPath();
					ctx.lineWidth = 3;
					ctx.moveTo(point1s_x, point1s_y);
					ctx.lineTo(point1e_x, point1e_y);
					ctx.stroke();
					ctx.beginPath();
					ctx.lineWidth = 3;
					ctx.moveTo(point2s_x, point2s_y);
					ctx.lineTo(point2e_x, point2e_y);
					ctx.stroke();
				}
			} else if (
				thiz.points.sv_int_state == 1 ||
				thiz.points.sv_int_state == 2
			) {
				//竖直版本

				horizontal_line1 = thiz.points.sv_dbl_point11_y * draw_canvas.height;
				//horizontal_line2 = horizontal_line1 + draw_canvas.height * 0.15;//
				horizontal_line3 = thiz.points.sv_dbl_point21_y * draw_canvas.height;

				var text_size = draw_canvas.height / 15;

				// V-line 绘制
				if (!thiz.freemode) {
					ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);
					ctx.strokeStyle = thiz.colors.horizontal_line1_color;
					ctx.fillStyle = ctx.strokeStyle;
					ctx.font = text_size + "px sans-serif";
					ctx.textAlign = "start";
					ctx.textBaseline = "top";
					if (thiz.lang == "RU") {
						var txt = "Макс";
					} else {
						var txt = "v-line";
					}
					ctx.fillText(txt, 0, horizontal_line1 - text_size);
					ctx.beginPath();
					ctx.lineWidth = 3;
					ctx.moveTo(0, horizontal_line1); //移动到位置0，horizontal_line1 的位置
					ctx.lineTo(draw_canvas.width, horizontal_line1); //画一条线
					ctx.stroke();

					/* ctx.strokeStyle=colors.horizontal_line2_color;
					ctx.fillStyle = ctx.strokeStyle;
					ctx.font = text_size+"px sans-serif";
					ctx.textAlign = "start";
					ctx.textBaseline = "top";
					var txt = "c-line";
					ctx.fillText(txt, 0, horizontal_line2-text_size);
					ctx.beginPath();
					ctx.moveTo(0, horizontal_line2);
					ctx.lineTo(draw_canvas.width, horizontal_line2);
					ctx.stroke();*/ //暂时不画蓝线

					// B-line 绘制
					ctx.strokeStyle = thiz.colors.horizontal_line3_color;
					ctx.fillStyle = ctx.strokeStyle;
					ctx.font = text_size + "px sans-serif";
					ctx.textAlign = "start";
					ctx.textBaseline = "top";
					if (thiz.lang == "RU") {
						var txt = "Мин";
					} else {
						var txt = "b-line";
					}
					ctx.fillText(txt, 0, horizontal_line3 - text_size);
					ctx.beginPath();
					ctx.lineWidth = 3;
					ctx.moveTo(0, horizontal_line3);
					ctx.lineTo(draw_canvas.width, horizontal_line3);
					ctx.stroke();
				}

				var direction = thiz.points.sv_int_state == 1 ? 1 : -1;
				for (var j = 1; j <= 4; j++) {
					index = j + direction;
					point1_x =
						thiz.points["sv_dbl_point1" + j + "_x"] * draw_canvas.width;
					point1_y =
						thiz.points["sv_dbl_point1" + j + "_y"] * draw_canvas.height;
					point2_x =
						thiz.points["sv_dbl_point2" + j + "_x"] * draw_canvas.width;
					point2_y =
						thiz.points["sv_dbl_point2" + j + "_y"] * draw_canvas.height;
					color1 = thiz.colors["line" + j + "_color"];

					paint_line(point1_x, point1_y, point2_x, point2_y, 3, color1); // 竖直线

					if (index > 4 || index < 1) continue;

					point3_x =
						thiz.points["sv_dbl_point1" + index + "_x"] * draw_canvas.width;
					point3_y =
						thiz.points["sv_dbl_point1" + index + "_y"] * draw_canvas.height;
					point4_x =
						thiz.points["sv_dbl_point2" + index + "_x"] * draw_canvas.width;
					point4_y =
						thiz.points["sv_dbl_point2" + index + "_y"] * draw_canvas.height;
					color2 = thiz.colors["line" + index + "_color"];

					paint_line(point1_x, point1_y, point3_x, point3_y, 3, color1); // 水平线
					paint_line(point4_x, point4_y, point2_x, point2_y, 3, color1); // 水平线
				}

				// 相应地改变四条竖线的长度
				/*
				for (var j = 1; j <= 4; j++) {
					point1_x = thiz.points['sv_dbl_point1' + j + '_x'] * draw_canvas.width;
					point1_y = thiz.points['sv_dbl_point1' + j + '_y'] * draw_canvas.height;
					point2_x = thiz.points['sv_dbl_point2' + j + '_x'] * draw_canvas.width;
					point2_y = thiz.points['sv_dbl_point2' + j + '_y'] * draw_canvas.height;
					//ctx.strokeStyle = thiz.colors['line' + j + '_color'];
					
					paint_line(point1_x, point1_y, point2_x, point2_y, 3, thiz.colors['line' + j + '_color']);
				}
				*/
			} else if (thiz.points.sv_int_state == 3) {
				//半圆版本
				point1_x = thiz.points.sv_dbl_point12_x * draw_canvas.width;
				point1_y = thiz.points.sv_dbl_point11_y * draw_canvas.height;
				point2_x =
					(2 * thiz.points.sv_dbl_point11_x - thiz.points.sv_dbl_point12_x) *
					draw_canvas.width;
				point2_y = thiz.points.sv_dbl_point11_y * draw_canvas.height;
				var centerX = thiz.points.sv_dbl_point11_x * draw_canvas.width;
				var centerY = thiz.points.sv_dbl_point11_y * draw_canvas.height;

				ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);
				/* 画直径 */
				ctx.strokeStyle = thiz.colors.line1_color;
				ctx.beginPath();
				ctx.lineWidth = 3;
				ctx.moveTo(point1_x, point1_y);
				ctx.lineTo(point2_x, point2_y);
				ctx.stroke();

				//画出圆心
				ctx.beginPath();
				ctx.lineWidth = 3;
				ctx.arc(centerX, centerY, trigger_d / 3, 0, Math.PI * 2, true);
				ctx.fillStyle = "rgba(0,0,255,0.2)";
				ctx.closePath();
				ctx.stroke();
				ctx.fill();

				//画出3段半圆弧
				for (var j = 2; j <= 4; j++) {
					//point12x-绿  point13-黄 point14-红
					ctx.beginPath();
					ctx.lineWidth = 3;
					ctx.arc(
						centerX,
						centerY,
						(thiz.points.sv_dbl_point11_x -
							thiz.points["sv_dbl_point1" + j + "_x"]) *
							draw_canvas.width,
						0,
						Math.PI * 1,
						true
					);
					ctx.strokeStyle = thiz.colors["line" + j + "_color"];
					ctx.stroke();
					ctx.closePath();

					ctx.beginPath();
					ctx.lineWidth = 3;
					ctx.arc(
						thiz.points["sv_dbl_point1" + j + "_x"] * draw_canvas.width,
						centerY,
						trigger_d / 3,
						0,
						Math.PI * 2,
						true
					);
					ctx.fillStyle = "rgba(0,0,255,0.2)";
					ctx.closePath();
					ctx.stroke();
					ctx.fill();
				}
			}

			if (thiz.hollow.enable == 1) {
				if (thiz.hollow.style == 0) {
					console.log("--------------------------------");
					point11_x = thiz.hollow.points[0].x * draw_canvas.width;
					point11_y = thiz.hollow.points[0].y * draw_canvas.height;
					point21_x = thiz.hollow.points[3].x * draw_canvas.width;
					point21_y = thiz.hollow.points[3].y * draw_canvas.height;
					point12_x = thiz.hollow.points[8].x * draw_canvas.width;
					point12_y = thiz.hollow.points[8].y * draw_canvas.height;
					point22_x = thiz.hollow.points[5].x * draw_canvas.width;
					point22_y = thiz.hollow.points[5].y * draw_canvas.height;
					paint_line2(point11_x, point11_y, point21_x, point21_y, 3, "#FFFFFF");
					paint_line2(point11_x, point11_y, point12_x, point12_y, 3, "#FFFFFF");
					paint_line2(point22_x, point22_y, point21_x, point21_y, 3, "#FFFFFF");
					paint_line2(point22_x, point22_y, point12_x, point12_y, 3, "#FFFFFF");
				} else if (thiz.hollow.style == 1) {
					console.log("--------------------------------");
					paint_drawboard();
				}
			}
		}

		var ada39_lineD = 0;
		function downEvent(pos_x, pos_y) {
			//movingPoint = 0;
			thiz.movingLine = "none";
			thiz.clearMovingLines();
			thiz.clearMovingPoints();

			if (!thiz.downEnable) {
				return;
			}

			if (thiz.hollow.enable == 1 && thiz.drawEnable == 1) {
				//上线 11  下线12  左线21  右线22
				if (thiz.hollow.style == 0) {
					//矩形
					if (
						contrastXYpos(
							thiz.hollow.points[0].y * draw_canvas.clientHeight,
							pos_y
						)
					) {
						//矩形的上线
						if (
							pos_x < thiz.hollow.points[3].x * draw_canvas.clientWidth &&
							pos_x > thiz.hollow.points[0].x * draw_canvas.clientWidth
						)
							thiz.movingLines.line5 = 11;
					}
					if (
						contrastXYpos(
							thiz.hollow.points[8].y * draw_canvas.clientHeight,
							pos_y
						)
					) {
						//矩形的下线
						if (
							pos_x < thiz.hollow.points[5].x * draw_canvas.clientWidth &&
							pos_x > thiz.hollow.points[8].x * draw_canvas.clientWidth
						)
							thiz.movingLines.line5 = 12;
					}
					if (
						contrastXYpos(
							thiz.hollow.points[0].x * draw_canvas.clientWidth,
							pos_x
						)
					) {
						//矩形的左线
						if (
							pos_y < thiz.hollow.points[8].y * draw_canvas.clientHeight &&
							pos_y > thiz.hollow.points[0].y * draw_canvas.clientHeight
						)
							thiz.movingLines.line5 = 21;
					}
					if (
						contrastXYpos(
							thiz.hollow.points[3].x * draw_canvas.clientWidth,
							pos_x
						)
					) {
						//矩形的右线
						if (
							pos_y < thiz.hollow.points[5].y * draw_canvas.clientHeight &&
							pos_y > thiz.hollow.points[3].y * draw_canvas.clientHeight
						)
							thiz.movingLines.line5 = 22;
					}

					console.log(thiz.movingLines.line5);
				} else if (thiz.hollow.style == 1) {
					//任意图形
					for (var i = 0; i < thiz.hollow.points_num; i++) {
						if (
							getDistance(
								draw_canvas.width * thiz.hollow.points[i].x,
								draw_canvas.height * thiz.hollow.points[i].y,
								pos_x,
								pos_y
							) < radius_scope
						) {
							thiz.hollow.points_is_dragging[i] = true;
							console.log(i);
						}
					}
					console.log(thiz.hollow.points_is_dragging);
				}
			}

			if (thiz.drawEnable == 0) {
				if (thiz.points.sv_int_state == 0 || thiz.points.sv_int_state == 4) {
					for (var i = 1; i <= 4; i++) {
						if (thiz.roiShowLevel > 0) {
							if (i == 2 && thiz.roiShowLevel > 0) {
								continue;
							} else if (j == i && thiz.roiShowLevel > 1) {
								continue;
							}
						}

						if (this.calibration_project == "ada39" && i == 3) continue;
						if (
							contrastXYpos(
								thiz.points["sv_dbl_point1" + i + "_y"] *
									draw_canvas.clientHeight,
								pos_y
							)
						) {
							thiz.colors["line" + i + "_color"] = thiz.choice_color; //选中线
							if (!(thiz.calibration_project == "ada39" && i == 3)) {
								if (
									contrastXYpos(
										thiz.hollow.points[0].y * draw_canvas.clientHeight,
										pos_y
									) ||
									contrastXYpos(
										thiz.hollow.points[8].y * draw_canvas.clientHeight,
										pos_y
									)
								) {
									if (
										pos_x < thiz.hollow.points[0].x * draw_canvas.clientWidth ||
										pos_x > thiz.hollow.points[3].x * draw_canvas.clientWidth
									)
										thiz.selectMovingLine(i);
								} else thiz.selectMovingLine(i);
							}
							if (thiz.freemode || i == 1 || i == 4) {
								//选中点
								for (var j = 1; j <= 2; j++) {
									if (thiz.calibration_project == "ada39") {
										if ((i == 1 && j == 2) || (i == 4 && j == 1)) {
											continue;
										}
									}
									if (
										contrastXYpos(
											thiz.points["sv_dbl_point" + j + "" + i + "_x"] *
												draw_canvas.clientWidth,
											pos_x
										)
									) {
										thiz.addMovingPoint(j * 10 + i);
									}
								}
							}
						}
						if (
							thiz.points.sv_int_state == 4 &&
							contrastXYpos(
								(thiz.points["sv_dbl_point1" + i + "_y"] +
									thiz.pdMinorAxis[i - 1]) *
									draw_canvas.clientHeight,
								pos_y
							)
						) {
							if (
								contrastXYpos(
									((thiz.points["sv_dbl_point1" + i + "_x"] +
										thiz.points["sv_dbl_point2" + i + "_x"]) /
										2) *
										draw_canvas.clientWidth,
									pos_x
								)
							) {
								thiz.addMovingPoint(i + 40);
							}
						}
					}

					if (!thiz.isMovingPointsEmpty()) {
						//选中点，就不能移动线
						thiz.clearMovingLines();
					} else if (thiz.isMovingPointsEmpty() && thiz.isMovingLinesEmpty()) {
						//线和点都没有选中
						isdown = false;
					} else {
						//选中线，线移动
						downPos.x = pos_x;
						downPos.y = pos_y;
						ada39_lineD =
							(thiz.points["sv_dbl_point12_y"] -
								thiz.points["sv_dbl_point11_y"]) /
							(thiz.points["sv_dbl_point24_y"] -
								thiz.points["sv_dbl_point11_y"]);
						refreshCalibrationLines();
					}
				} else if (
					thiz.points.sv_int_state == 1 ||
					thiz.points.sv_int_state == 2
				) {
					//horizontal_line2 = (thiz.points['sv_dbl_point11_y'] + 0.15) * draw_canvas.clientHeight;
					if (thiz.freemode) {
						for (k = 1; k <= 2; k++) {
							for (j = 1; j <= 4; j++) {
								if (
									contrastXYpos(
										thiz.points["sv_dbl_point" + k + j + "_x"] *
											draw_canvas.clientWidth,
										pos_x
									) &&
									contrastXYpos(
										thiz.points["sv_dbl_point" + k + j + "_y"] *
											draw_canvas.clientHeight,
										pos_y
									)
								) {
									console.log("sv_dbl_point" + k + j);
									thiz.addMovingPoint(10 * k + j);
								}
							}
						}
					}

					if (
						!thiz.freemode &&
						contrastXYpos(
							thiz.points["sv_dbl_point11_y"] * draw_canvas.clientHeight,
							pos_y
						)
					) {
						//y坐标判断是否line1
						for (j = 1; j <= 4; j++) {
							//再次x坐标，判断是否选中line1上的点
							if (
								contrastXYpos(
									thiz.points["sv_dbl_point1" + j + "_x"] *
										draw_canvas.clientWidth,
									pos_x
								)
							) {
								thiz.addMovingPoint(10 + j);
								thiz.colors["line" + j + "_color"] = thiz.choice_color;
							}
						}
						if (thiz.isMovingPointsEmpty()) {
							if (
								contrastXYpos(
									thiz.hollow.points[0].y * draw_canvas.clientHeight,
									pos_y
								) ||
								contrastXYpos(
									thiz.hollow.points[8].y * draw_canvas.clientHeight,
									pos_y
								)
							) {
								if (
									pos_x < thiz.hollow.points[0].x * draw_canvas.clientWidth ||
									pos_x > thiz.hollow.points[3].x * draw_canvas.clientWidth
								) {
									thiz.movingLine = "v-line";
									thiz.colors.horizontal_line1_color = thiz.choice_color;
								}
							} else {
								thiz.movingLine = "v-line";
								thiz.colors.horizontal_line1_color = thiz.choice_color;
							}
						}
					}
					if (
						!thiz.freemode &&
						contrastXYpos(
							thiz.points["sv_dbl_point21_y"] * draw_canvas.clientHeight,
							pos_y
						)
					) {
						//y坐标判断是否line3
						for (j = 1; j <= 4; j++) {
							//再次x坐标，判断是否选中line3上的点
							if (
								contrastXYpos(
									thiz.points["sv_dbl_point2" + j + "_x"] *
										draw_canvas.clientWidth,
									pos_x
								)
							) {
								thiz.addMovingPoint(20 + j);
								thiz.colors["line" + j + "_color"] = thiz.choice_color;
							}
						}
						if (thiz.isMovingPointsEmpty()) {
							if (
								contrastXYpos(
									thiz.hollow.points[0].y * draw_canvas.clientHeight,
									pos_y
								) ||
								contrastXYpos(
									thiz.hollow.points[8].y * draw_canvas.clientHeight,
									pos_y
								)
							) {
								if (
									pos_x < thiz.hollow.points[0].x * draw_canvas.clientWidth ||
									pos_x > thiz.hollow.points[3].x * draw_canvas.clientWidth
								) {
									thiz.movingLine = "b-line";
									thiz.colors.horizontal_line3_color = thiz.choice_color;
								}
							} else {
								thiz.movingLine = "b-line";
								thiz.colors.horizontal_line3_color = thiz.choice_color;
							}
						}
					}
					/*else if (contrastXYpos(horizontal_line2, pos_y)) {  //是否选中line2
					for (j = 1; j <= 4; j++) {						//是否选中line2上的点
						var point1_x = thiz.points['sv_dbl_point1' + j + '_x'] * draw_canvas.clientWidth;
						var point1_y = thiz.points['sv_dbl_point1' + j + '_y'] * draw_canvas.clientHeight;
						var point2_x = thiz.points['sv_dbl_point2' + j + '_x'] * draw_canvas.clientWidth;
						var point2_y = thiz.points['sv_dbl_point2' + j + '_y'] * draw_canvas.clientHeight;
						var xx = (point2_x - point1_x) / (point2_y - point1_y) * (horizontal_line2 - point2_y) + point2_x;
						if (contrastXYpos(xx, pos_x)) {
							thiz.addMovingPoint(20 + j);
							thiz.colors['line' + j + '_color'] = thiz.choice_color;
						}
					}
					if (movingPoint == 0) {
						thiz.movingLine = "none"
					}
					if (thiz.isMovingPointsEmpty()) {	
						thiz.movingLine = "b-line";
						thiz.colors.horizontal_line3_color = thiz.choice_color;//b-line可以移动到c-line的位置，所以应该能被选中，才能往下拉。
					}
				}
				else if (contrastXYpos(thiz.points['sv_dbl_point21_y'] * draw_canvas.clientHeight, pos_y)) {//选中b-line，最底下的线
					if (thiz.isMovingPointsEmpty()) {
						thiz.movingLine = "b-line";
						thiz.colors.horizontal_line3_color = thiz.choice_color;
					}
				}*/

					if (thiz.isMovingPointsEmpty() && thiz.movingLine == "none") {
						thiz.isdown = false;
					} else {
						downPos.x = pos_x;
						downPos.y = pos_y; //记录鼠标点击的位置
						refreshCalibrationLines();
					}
				} else if (thiz.points.sv_int_state == 3) {
					for (var i = 1; i <= 4; i++) {
						if (
							contrastXYpos(
								thiz.points.sv_dbl_point11_y * draw_canvas.clientHeight,
								pos_y
							)
						) {
							if (
								contrastXYpos(
									thiz.points["sv_dbl_point1" + i + "_x"] *
										draw_canvas.clientWidth,
									pos_x
								)
							) {
								thiz.addMovingPoint(i);
							}
						}
					}
					downPos.x = pos_x;
					downPos.y = pos_y;
					refreshCalibrationLines();
				}
			}
		}

		function moveEvent(pos_x, pos_y) {
			if (!thiz.downEnable) {
				return;
			}
			if (thiz.drawEnable == 0) {
				if (thiz.points.sv_int_state == 0 || thiz.points.sv_int_state == 4) {
					if (!thiz.isMovingPointsEmpty()) {
						//移动点
						var currentpoint_one = 0;
						var currentpoint = 0;
						for (var i = 1; i <= 4; i++) {
							if (thiz.movingPoints["point" + i] > 0) {
								if (currentpoint_one == 0) {
									currentpoint_one = thiz.movingPoints["point" + i] % 10;
								} else {
									if (pos_y < downPos.y) {
										if (thiz.movingPoints["point" + i] % 10 < currentpoint_one)
											currentpoint_one = thiz.movingPoints["point" + i] % 10;
									} else {
										if (thiz.movingPoints["point" + i] % 10 > currentpoint_one)
											currentpoint_one = thiz.movingPoints["point" + i] % 10;
									}
								}
							}
						}

						for (var i = 1; i <= 4; i++) {
							if (thiz.movingPoints["point" + i] % 10 == currentpoint_one) {
								if (currentpoint == 0) {
									currentpoint = thiz.movingPoints["point" + i];
								} else {
									if (pos_x < downPos.x) {
										if (thiz.movingPoints["point" + i] < currentpoint) {
											currentpoint = thiz.movingPoints["point" + i];
											thiz.movingPoints["point" + (i - 1)] = 0;
										} else thiz.movingPoints["point" + i] = 0;
									} else {
										if (thiz.movingPoints["point" + i] > currentpoint) {
											currentpoint = thiz.movingPoints["point" + i];
											thiz.movingPoints["point" + (i - 1)] = 0;
										} else thiz.movingPoints["point" + i] = 0;
									}
								}
							} else {
								thiz.movingPoints["point" + i] = 0;
							}
						}

						if (currentpoint > 0) {
							var dbPos_x = (pos_x * 1.0) / draw_canvas.clientWidth;
							var dbPos_y = (pos_y * 1.0) / draw_canvas.clientHeight;
							if (this.calibration_project == "ada39") {
								var dCabvas =
									0.7 /
									((draw_canvas.clientHeight * 1.0) / draw_canvas.clientWidth); //算法需要的保持比例（因为roi顶点用的是比例值，这里要和web画面换算下）
								if (currentpoint == 11) {
									var roiHeight =
										thiz.points["sv_dbl_point24_y"] -
										thiz.points["sv_dbl_point11_y"];
									var min = thiz.roi_limit.top;
									var max = thiz.roi_limit.bottom - roiHeight;
									thiz.points["sv_dbl_point11_y"] = dbPos_y;
									if (thiz.points["sv_dbl_point11_y"] < min)
										thiz.points["sv_dbl_point11_y"] = min;
									if (thiz.points["sv_dbl_point11_y"] > max)
										thiz.points["sv_dbl_point11_y"] = max;
									thiz.points["sv_dbl_point21_y"] =
										thiz.points["sv_dbl_point11_y"];
									thiz.points["sv_dbl_point14_y"] =
										thiz.points["sv_dbl_point11_y"] + roiHeight;
									thiz.points["sv_dbl_point24_y"] =
										thiz.points["sv_dbl_point14_y"];

									var roiWidth =
										thiz.points["sv_dbl_point24_x"] -
										thiz.points["sv_dbl_point11_x"];
									min = thiz.roi_limit.left;
									max = thiz.roi_limit.right - roiWidth;
									thiz.points["sv_dbl_point11_x"] = dbPos_x;
									if (thiz.points["sv_dbl_point11_x"] < min)
										thiz.points["sv_dbl_point11_x"] = min;
									if (thiz.points["sv_dbl_point11_x"] > max)
										thiz.points["sv_dbl_point11_x"] = max;
									thiz.points["sv_dbl_point14_x"] =
										thiz.points["sv_dbl_point11_x"];
									thiz.points["sv_dbl_point21_x"] =
										thiz.points["sv_dbl_point11_x"] + roiWidth;
									thiz.points["sv_dbl_point24_x"] =
										thiz.points["sv_dbl_point21_x"];
								} else if (currentpoint == 24) {
									if (
										Math.abs(pos_y - downPos.y) > Math.abs(pos_x - downPos.x)
									) {
										var min = thiz.points["sv_dbl_point11_y"];
										var max = thiz.roi_limit.bottom;
										thiz.points["sv_dbl_point24_y"] = dbPos_y;
										if (thiz.points["sv_dbl_point24_y"] < min)
											thiz.points["sv_dbl_point24_y"] = min;
										if (thiz.points["sv_dbl_point24_y"] > max)
											thiz.points["sv_dbl_point24_y"] = max;
										if (
											(thiz.points["sv_dbl_point24_y"] -
												thiz.points["sv_dbl_point11_y"]) /
												dCabvas +
												thiz.points["sv_dbl_point11_x"] >
											thiz.roi_limit.right
										) {
											thiz.points["sv_dbl_point24_y"] =
												(thiz.roi_limit.right -
													thiz.points["sv_dbl_point11_x"]) *
													dCabvas +
												thiz.points["sv_dbl_point11_y"];
										}
										thiz.points["sv_dbl_point24_x"] =
											(thiz.points["sv_dbl_point24_y"] -
												thiz.points["sv_dbl_point11_y"]) /
												dCabvas +
											thiz.points["sv_dbl_point11_x"];
										thiz.points["sv_dbl_point21_x"] =
											thiz.points["sv_dbl_point24_x"];
										thiz.points["sv_dbl_point14_y"] =
											thiz.points["sv_dbl_point24_y"];
									} else {
										var min = thiz.points["sv_dbl_point11_x"];
										var max = thiz.roi_limit.right;
										thiz.points["sv_dbl_point24_x"] = dbPos_x;
										if (thiz.points["sv_dbl_point24_x"] < min)
											thiz.points["sv_dbl_point24_x"] = min;
										if (thiz.points["sv_dbl_point24_x"] > max)
											thiz.points["sv_dbl_point24_x"] = max;
										if (
											(thiz.points["sv_dbl_point24_x"] -
												thiz.points["sv_dbl_point11_x"]) *
												dCabvas +
												thiz.points["sv_dbl_point11_y"] >
											thiz.roi_limit.bottom
										) {
											thiz.points["sv_dbl_point24_x"] =
												(thiz.roi_limit.bottom -
													thiz.points["sv_dbl_point11_y"]) /
													dCabvas +
												thiz.points["sv_dbl_point11_x"];
										}
										thiz.points["sv_dbl_point24_y"] =
											(thiz.points["sv_dbl_point24_x"] -
												thiz.points["sv_dbl_point11_x"]) *
												dCabvas +
											thiz.points["sv_dbl_point11_y"];
										thiz.points["sv_dbl_point21_x"] =
											thiz.points["sv_dbl_point24_x"];
										thiz.points["sv_dbl_point14_y"] =
											thiz.points["sv_dbl_point24_y"];
									}
								}
								thiz.points["sv_dbl_point12_y"] =
									thiz.points["sv_dbl_point11_y"] +
									(thiz.points["sv_dbl_point24_y"] -
										thiz.points["sv_dbl_point11_y"]) *
										ada39_lineD;
								thiz.points["sv_dbl_point22_y"] =
									thiz.points["sv_dbl_point12_y"];
							} else {
								var min = thiz.roi_limit.top;
								var max = thiz.roi_limit.bottom;
								thiz.points["sv_dbl_point" + currentpoint + "_y"] = dbPos_y;

								var step = 1;
								if (currentpoint % 10 == 1) {
									step += thiz.roiShowLevel;
								}

								if (
									typeof thiz.points[
										"sv_dbl_point" + (currentpoint - step) + "_y"
									] != "undefined"
								) {
									min =
										thiz.points["sv_dbl_point" + (currentpoint - step) + "_y"];
								}
								if (thiz.points["sv_dbl_point" + currentpoint + "_y"] < min)
									thiz.points["sv_dbl_point" + currentpoint + "_y"] = min;
								if (
									typeof thiz.points[
										"sv_dbl_point" + (currentpoint + step) + "_y"
									] != "undefined"
								) {
									max =
										thiz.points["sv_dbl_point" + (currentpoint + step) + "_y"];
								}
								if (thiz.points["sv_dbl_point" + currentpoint + "_y"] > max)
									thiz.points["sv_dbl_point" + currentpoint + "_y"] = max;

								var next_point;
								var min = thiz.roi_limit.left;
								var max = thiz.roi_limit.right;
								thiz.points["sv_dbl_point" + currentpoint + "_x"] = dbPos_x;

								if (
									typeof thiz.points[
										"sv_dbl_point" + (currentpoint - 10) + "_x"
									] != "undefined"
								) {
									min =
										thiz.points["sv_dbl_point" + (currentpoint - 10) + "_x"];
									next_point = currentpoint - 10;
								}

								if (thiz.points["sv_dbl_point" + currentpoint + "_x"] < min) {
									thiz.points["sv_dbl_point" + currentpoint + "_x"] = min;
								}

								if (
									typeof thiz.points[
										"sv_dbl_point" + (currentpoint + 10) + "_x"
									] != "undefined"
								) {
									max =
										thiz.points["sv_dbl_point" + (currentpoint + 10) + "_x"];
									next_point = currentpoint + 10;
								}
								if (thiz.points["sv_dbl_point" + currentpoint + "_x"] > max) {
									thiz.points["sv_dbl_point" + currentpoint + "_x"] = max;
								}

								thiz.points["sv_dbl_point" + next_point + "_y"] =
									thiz.points["sv_dbl_point" + currentpoint + "_y"];
								if (currentpoint % 10 == 1) {
									if (thiz.roiShowLevel == 1) {
										thiz.points["sv_dbl_point" + (currentpoint + 1) + "_y"] =
											thiz.points["sv_dbl_point" + currentpoint + "_y"];
										thiz.points["sv_dbl_point" + (next_point + 1) + "_y"] =
											thiz.points["sv_dbl_point" + currentpoint + "_y"];
										thiz.points["sv_dbl_point" + (currentpoint + 1) + "_x"] =
											thiz.points["sv_dbl_point" + currentpoint + "_x"];
									} else if (thiz.roiShowLevel == 2) {
										thiz.points["sv_dbl_point" + (currentpoint + 1) + "_y"] =
											thiz.points["sv_dbl_point" + currentpoint + "_y"];
										thiz.points["sv_dbl_point" + (next_point + 1) + "_y"] =
											thiz.points["sv_dbl_point" + currentpoint + "_y"];
										thiz.points["sv_dbl_point" + (currentpoint + 1) + "_x"] =
											thiz.points["sv_dbl_point" + currentpoint + "_x"];
										thiz.points["sv_dbl_point" + (currentpoint + 2) + "_y"] =
											thiz.points["sv_dbl_point" + currentpoint + "_y"];
										thiz.points["sv_dbl_point" + (next_point + 2) + "_y"] =
											thiz.points["sv_dbl_point" + currentpoint + "_y"];
										thiz.points["sv_dbl_point" + (currentpoint + 2) + "_x"] =
											thiz.points["sv_dbl_point" + currentpoint + "_x"];
									}
								}
							}

							if (thiz.points.sv_int_state == 4) {
								if (dbPos_y > max) dbPos_y = max;
								if (dbPos_y < min) dbPos_y = min;

								if (currentpoint == 41) {
									var majoraxis1 =
										(thiz.points.sv_dbl_point21_x -
											thiz.points.sv_dbl_point11_x) /
										2;
									if (dbPos_y > thiz.points.sv_dbl_point12_y)
										dbPos_y = thiz.points.sv_dbl_point12_y;

									thiz.pdMinorAxis[0] = dbPos_y - thiz.points.sv_dbl_point11_y;
									if (
										thiz.pdMinorAxis[0] > 0 &&
										thiz.pdMinorAxis[0] > majoraxis1
									)
										thiz.pdMinorAxis[0] = majoraxis1;
									if (
										thiz.pdMinorAxis[0] < 0 &&
										thiz.pdMinorAxis[0] < -majoraxis1
									)
										thiz.pdMinorAxis[0] = -majoraxis1;
								} else if (currentpoint == 42) {
									var majoraxis2 =
										(thiz.points.sv_dbl_point22_x -
											thiz.points.sv_dbl_point12_x) /
										2;
									if (dbPos_y > thiz.points.sv_dbl_point13_y)
										dbPos_y = thiz.points.sv_dbl_point13_y;
									if (
										dbPos_y <
										thiz.points.sv_dbl_point11_y + thiz.pdMinorAxis[0]
									)
										dbPos_y =
											thiz.points.sv_dbl_point11_y + thiz.pdMinorAxis[0];

									thiz.pdMinorAxis[1] = dbPos_y - thiz.points.sv_dbl_point12_y;
									if (
										thiz.pdMinorAxis[1] > 0 &&
										thiz.pdMinorAxis[1] > majoraxis2
									)
										thiz.pdMinorAxis[1] = majoraxis2;
									if (
										thiz.pdMinorAxis[1] < 0 &&
										thiz.pdMinorAxis[1] < -majoraxis2
									)
										thiz.pdMinorAxis[1] = -majoraxis2;
								} else if (currentpoint == 43) {
									var majoraxis3 =
										(thiz.points.sv_dbl_point23_x -
											thiz.points.sv_dbl_point13_x) /
										2;
									if (
										dbPos_y <
										thiz.points.sv_dbl_point12_y + thiz.pdMinorAxis[1]
									)
										dbPos_y =
											thiz.points.sv_dbl_point12_y + thiz.pdMinorAxis[1];
									thiz.pdMinorAxis[2] = dbPos_y - thiz.points.sv_dbl_point13_y;
									if (
										thiz.pdMinorAxis[2] > 0 &&
										thiz.pdMinorAxis[2] > majoraxis3
									)
										thiz.pdMinorAxis[2] = majoraxis3;
									if (
										thiz.pdMinorAxis[2] < 0 &&
										thiz.pdMinorAxis[2] < -majoraxis3
									)
										thiz.pdMinorAxis[2] = -majoraxis3;
								}
							}
							thiz.clearColor();
							thiz.colors["line" + (currentpoint % 10) + "_color"] =
								thiz.choice_color;
						}
					} else {
						//移动线
						var currentline = 0;
						if (pos_y < downPos.y) {
							//向上移
							for (var i = 1; i <= 4; i++) {
								if (thiz.movingLines["line" + i] > 0) {
									currentline = i;
									break;
								}
							}
						} else {
							//向下移
							for (var i = 4; i >= 1; i--) {
								if (thiz.movingLines["line" + i] > 0) {
									currentline = i;
									break;
								}
							}
						}

						if (currentline > 0) {
							thiz.clearColor();
							thiz.colors["line" + currentline + "_color"] = thiz.choice_color;
							thiz.clearMovingLines();
							thiz.selectMovingLine(currentline);

							if (this.calibration_project == "ada39") {
								var min = thiz.points["sv_dbl_point11_y"];
								var max = thiz.points["sv_dbl_point24_y"];
								thiz.points["sv_dbl_point12_y"] =
									(pos_y * 1.0) / draw_canvas.clientHeight;
								if (thiz.points["sv_dbl_point12_y"] < min)
									thiz.points["sv_dbl_point12_y"] = min;
								if (thiz.points["sv_dbl_point12_y"] > max)
									thiz.points["sv_dbl_point12_y"] = max;
								thiz.points["sv_dbl_point22_y"] =
									thiz.points["sv_dbl_point12_y"];
							} else {
								var min = thiz.roi_limit.top;
								var max = thiz.roi_limit.bottom;
								thiz.points["sv_dbl_point1" + currentline + "_y"] =
									(pos_y * 1.0) / draw_canvas.clientHeight;

								var step = 1;
								/*if(currentline == 1){
								step += this.roiShowLevel;
							}*/

								//对线的上下移动进行限制
								if (
									typeof thiz.points[
										"sv_dbl_point1" + (currentline - step) + "_y"
									] != "undefined"
								) {
									min =
										thiz.points["sv_dbl_point1" + (currentline - step) + "_y"];
									if (thiz.points.sv_int_state == 4) {
										console.log(
											min,
											thiz.pdMinorAxis[currentline - 2],
											currentline - 2
										);
										min = thiz.pdMinorAxis[currentline - 2] + min;
									}
								}

								if (thiz.points["sv_dbl_point1" + currentline + "_y"] < min)
									thiz.points["sv_dbl_point1" + currentline + "_y"] = min;

								if (
									typeof thiz.points[
										"sv_dbl_point1" + (currentline + step) + "_y"
									] != "undefined"
								) {
									max =
										thiz.points["sv_dbl_point1" + (currentline + step) + "_y"];
									if (thiz.points.sv_int_state == 4) {
										max = max - thiz.pdMinorAxis[currentline - 1];
									}
								}
								if (thiz.points["sv_dbl_point1" + currentline + "_y"] > max)
									thiz.points["sv_dbl_point1" + currentline + "_y"] = max;
								thiz.points["sv_dbl_point2" + currentline + "_y"] =
									thiz.points["sv_dbl_point1" + currentline + "_y"];

								if (currentline == 1) {
									if (this.roiShowLevel == 1) {
										thiz.points["sv_dbl_point2" + currentline + "_y"] =
											thiz.points["sv_dbl_point1" + currentline + "_y"];
										thiz.points["sv_dbl_point2" + (currentline + 1) + "_y"] =
											thiz.points["sv_dbl_point1" + currentline + "_y"];
										thiz.points["sv_dbl_point1" + (currentline + 1) + "_y"] =
											thiz.points["sv_dbl_point1" + currentline + "_y"];
									} else if (this.roiShowLevel == 2) {
										thiz.points["sv_dbl_point2" + currentline + "_y"] =
											thiz.points["sv_dbl_point1" + currentline + "_y"];
										thiz.points["sv_dbl_point2" + (currentline + 1) + "_y"] =
											thiz.points["sv_dbl_point1" + currentline + "_y"];
										thiz.points["sv_dbl_point1" + (currentline + 1) + "_y"] =
											thiz.points["sv_dbl_point1" + currentline + "_y"];
										thiz.points["sv_dbl_point2" + (currentline + 2) + "_y"] =
											thiz.points["sv_dbl_point1" + currentline + "_y"];
										thiz.points["sv_dbl_point1" + (currentline + 2) + "_y"] =
											thiz.points["sv_dbl_point1" + currentline + "_y"];
									}
								}
							}
						}
					}

					if (!thiz.freemode) {
						//限制点在同一直线上
						thiz.points.sv_dbl_point12_x =
							((thiz.points.sv_dbl_point11_x - thiz.points.sv_dbl_point14_x) *
								(thiz.points.sv_dbl_point14_y - thiz.points.sv_dbl_point12_y)) /
								(thiz.points.sv_dbl_point14_y - thiz.points.sv_dbl_point11_y) +
							thiz.points.sv_dbl_point14_x;
						thiz.points.sv_dbl_point22_x =
							((thiz.points.sv_dbl_point21_x - thiz.points.sv_dbl_point24_x) *
								(thiz.points.sv_dbl_point24_y - thiz.points.sv_dbl_point22_y)) /
								(thiz.points.sv_dbl_point24_y - thiz.points.sv_dbl_point21_y) +
							thiz.points.sv_dbl_point24_x;
						thiz.points.sv_dbl_point13_x =
							((thiz.points.sv_dbl_point11_x - thiz.points.sv_dbl_point14_x) *
								(thiz.points.sv_dbl_point14_y - thiz.points.sv_dbl_point13_y)) /
								(thiz.points.sv_dbl_point14_y - thiz.points.sv_dbl_point11_y) +
							thiz.points.sv_dbl_point14_x;
						thiz.points.sv_dbl_point23_x =
							((thiz.points.sv_dbl_point21_x - thiz.points.sv_dbl_point24_x) *
								(thiz.points.sv_dbl_point24_y - thiz.points.sv_dbl_point23_y)) /
								(thiz.points.sv_dbl_point24_y - thiz.points.sv_dbl_point21_y) +
							thiz.points.sv_dbl_point24_x;
					}
				} else if (
					thiz.points.sv_int_state == 1 ||
					thiz.points.sv_int_state == 2
				) {
					if (!thiz.isMovingPointsEmpty()) {
						var min = thiz.roi_limit.top;
						var max = thiz.roi_limit.bottom;
						var currentpoint = 0; //当前选中哪个点
						for (var i = 1; i <= 4; i++) {
							if (thiz.movingPoints["point" + i] > 0) {
								if (currentpoint == 0) {
									currentpoint = thiz.movingPoints["point" + i];
								} else {
									if (pos_x < downPos.x) {
										if (thiz.movingPoints["point" + i] < currentpoint) {
											currentpoint = thiz.movingPoints["point" + i];
											thiz.movingPoints["point" + (i - 1)] = 0;
										} else {
											thiz.movingPoints["point" + i] = 0;
										}
									} else {
										if (thiz.movingPoints["point" + i] > currentpoint) {
											currentpoint = thiz.movingPoints["point" + i];
											thiz.movingPoints["point" + (i - 1)] = 0;
										} else {
											thiz.movingPoints["point" + i] = 0;
										}
									}
								}
							}
						}

						if (currentpoint > 0) {
							/*if (currentpoint >= 20) {
							if (pos_x * 1.0 / draw_canvas.clientWidth > max)
								pos_x = draw_canvas.clientWidth * max;
							else if (pos_x * 1.0 / draw_canvas.clientWidth < min)
								pos_x = draw_canvas.clientWidth * min;
							min = -99.0;
							max = 99.0;
							horizontal_line2 = (thiz.points['sv_dbl_point11_y'] + 0.15) * draw_canvas.clientHeight;
							var point1_x = thiz.points['sv_dbl_point1' + (currentpoint - 20) + '_x'] * draw_canvas.clientWidth;
							var point1_y = thiz.points['sv_dbl_point1' + (currentpoint - 20) + '_y'] * draw_canvas.clientHeight;
							thiz.points['sv_dbl_point' + currentpoint + '_x'] = ((pos_x - point1_x) / (horizontal_line2 - point1_y) * (draw_canvas.clientHeight - horizontal_line2) + pos_x) * 1.0 / draw_canvas.clientWidth;
						}
						else {
							thiz.points['sv_dbl_point' + currentpoint + '_x'] = pos_x * 1.0 / draw_canvas.clientWidth;
						}*/
							thiz.points["sv_dbl_point" + currentpoint + "_x"] =
								(pos_x * 1.0) / draw_canvas.clientWidth;
							if (thiz.freemode) {
								thiz.points["sv_dbl_point" + currentpoint + "_y"] =
									(pos_y * 1.0) / draw_canvas.clientHeight;
							}

							thiz.clearColor();
							thiz.colors["line" + (currentpoint % 10) + "_color"] =
								thiz.choice_color;
							if (!thiz.freemode) {
								if (
									typeof thiz.points[
										"sv_dbl_point" + (currentpoint - 1) + "_x"
									] != "undefined"
								)
									min = thiz.points["sv_dbl_point" + (currentpoint - 1) + "_x"];
								if (thiz.points["sv_dbl_point" + currentpoint + "_x"] < min)
									thiz.points["sv_dbl_point" + currentpoint + "_x"] = min;
								if (
									typeof thiz.points[
										"sv_dbl_point" + (currentpoint + 1) + "_x"
									] != "undefined"
								)
									max = thiz.points["sv_dbl_point" + (currentpoint + 1) + "_x"];
								if (thiz.points["sv_dbl_point" + currentpoint + "_x"] > max)
									thiz.points["sv_dbl_point" + currentpoint + "_x"] = max;
							}
						}
					} else if (!thiz.freemode && thiz.movingLine == "v-line") {
						var tmp_f = (pos_y * 1.0) / draw_canvas.clientHeight;

						if (tmp_f > thiz.points["sv_dbl_point21_y"] - 0.15)
							tmp_f = thiz.points["sv_dbl_point21_y"] - 0.15;
						else if (tmp_f < 0.05) tmp_f = 0.05;
						for (var j = 1; j <= 4; j++) {
							thiz.points["sv_dbl_point1" + j + "_y"] = tmp_f;
						}
					} else if (!thiz.freemode && thiz.movingLine == "c-line") {
						var tmp_f = (pos_y * 1.0) / draw_canvas.clientHeight;
						if (tmp_f > 0.95) tmp_f = 0.95;
						else if (tmp_f < points["sv_dbl_point11_y"] + 0.15)
							tmp_f = thiz.points["sv_dbl_point11_y"] + 0.15;
						for (var j = 1; j <= 4; j++) {
							thiz.points["sv_dbl_point2" + j + "_y"] = tmp_f;
						}
					} else if (!thiz.freemode && thiz.movingLine == "b-line") {
						var tmp_f = (pos_y * 1.0) / draw_canvas.clientHeight;
						//console.log("b-line----------"+tmp_f);
						if (tmp_f < thiz.points["sv_dbl_point11_y"] + 0.15)
							tmp_f = thiz.points["sv_dbl_point11_y"] + 0.15;

						if (tmp_f > 0.99) tmp_f = 0.99;
						for (var j = 1; j <= 4; j++) {
							thiz.points["sv_dbl_point2" + j + "_y"] = tmp_f;
						}
					}
				} else if (thiz.points.sv_int_state == 3) {
					if (!thiz.isMovingPointsEmpty()) {
						//移动点
						var currentpoint = 0;
						for (var i = 1; i <= 4; i++) {
							if (thiz.movingPoints["point" + i] > 0) {
								if (currentpoint == 0) {
									//如果2 3点重合，那么先选中2
									currentpoint = thiz.movingPoints["point" + i];
								} else {
									if (pos_x < downPos.x) {
										//如果是左移
										if (currentpoint == 1)
											currentpoint = thiz.movingPoints["point" + i];
										//如果左移不能选中 1
										else thiz.movingPoints["point" + i] = 0; //此i 点不选中
									} else {
										//如果是右移
										currentpoint = thiz.movingPoints["point" + i]; //如果2 3 点重合，此时选中3
									}
								}
							}
						}
						console.log("currentpoint:" + currentpoint);
						var xmin, xmax;
						if (currentpoint >= 0) {
							// 2 3 4 1 布局，所有每个点单独处理
							thiz.colors["line" + currentpoint + "_color"] = thiz.choice_color;

							if (currentpoint == 2) {
								//点2、3、4可以左右移动，改变半径
								thiz.points.sv_dbl_point12_x =
									(pos_x * 1.0) / draw_canvas.clientWidth;

								var toplimit =
									(thiz.points.sv_dbl_point11_y * draw_canvas.clientHeight) /
									draw_canvas.clientWidth;
								xmin = thiz.points.sv_dbl_point11_x - toplimit;
								if (thiz.points.sv_dbl_point12_x < xmin)
									thiz.points.sv_dbl_point12_x = xmin;

								xmax = thiz.points.sv_dbl_point13_x;
								if (thiz.points.sv_dbl_point12_x > xmax)
									thiz.points.sv_dbl_point12_x = xmax;
							} else if (currentpoint == 3) {
								thiz.points.sv_dbl_point13_x =
									(pos_x * 1.0) / draw_canvas.clientWidth;

								xmin = thiz.points.sv_dbl_point12_x;
								if (thiz.points.sv_dbl_point13_x < xmin)
									thiz.points.sv_dbl_point13_x = xmin;

								xmax = thiz.points.sv_dbl_point14_x;
								if (thiz.points.sv_dbl_point13_x > xmax)
									thiz.points.sv_dbl_point13_x = xmax;
							} else if (currentpoint == 4) {
								thiz.points.sv_dbl_point14_x =
									(pos_x * 1.0) / draw_canvas.clientWidth;

								xmin = thiz.points.sv_dbl_point13_x;
								if (thiz.points.sv_dbl_point14_x < xmin)
									thiz.points.sv_dbl_point14_x = xmin;

								xmax = thiz.points.sv_dbl_point11_x;
								if (thiz.points.sv_dbl_point14_x > xmax)
									thiz.points.sv_dbl_point14_x = xmax;
							} else {
								thiz.points.sv_dbl_point11_y =
									(pos_y * 1.0) / draw_canvas.clientHeight;
								//thiz.circle['sv_dbl_point' + currentpoint + '_x'] = pos_x * 1.0 / draw_canvas.clientWidth;
								var raduis =
									(thiz.points.sv_dbl_point11_x -
										thiz.points.sv_dbl_point12_x) *
									draw_canvas.clientWidth;
								var ymin =
									thiz.roi_limit.top +
									(raduis * 1.0) / draw_canvas.clientHeight;
								var ymax = thiz.roi_limit.bottom;

								/*if(thiz.circle.sv_dbl_point1_x < thiz.circle.sv_dbl_point2_x)
								thiz.circle.sv_dbl_point1_x = thiz.circle.sv_dbl_point2_x;
							if( (2*thiz.circle.sv_dbl_point1_x - thiz.circle.sv_dbl_point4_x) > thiz.roi_limit.right )
								thiz.circle.sv_dbl_point1_x = thiz.roi_limit.right - (thiz.circle.sv_dbl_point1_x - thiz.circle.sv_dbl_point4_x);*/

								if (thiz.points.sv_dbl_point11_y > ymax)
									thiz.points.sv_dbl_point11_y = ymax;
								if (thiz.points.sv_dbl_point11_y < ymin)
									thiz.points.sv_dbl_point11_y = ymin;
							}
						}
					}
				}
			}

			if (thiz.hollow.enable == 1 && thiz.drawEnable == 1) {
				if (thiz.hollow.style == 0) {
					var hollowLine = thiz.movingLines.line5;
					if (hollowLine > 0) {
						thiz.clearColor();
						thiz.movingLines.line5 = hollowLine;
						switch (hollowLine) {
							case 11:
								thiz.hollow.points[0].y =
									(pos_y * 1.0) / draw_canvas.clientHeight;
								thiz.hollow.points[3].y = thiz.hollow.points[0].y;
								break;
							case 12:
								thiz.hollow.points[8].y =
									(pos_y * 1.0) / draw_canvas.clientHeight;
								thiz.hollow.points[5].y = thiz.hollow.points[8].y;
								break;
							case 21:
								thiz.hollow.points[0].x =
									(pos_x * 1.0) / draw_canvas.clientWidth;
								thiz.hollow.points[8].x = thiz.hollow.points[0].x;
								break;
							case 22:
								thiz.hollow.points[3].x =
									(pos_x * 1.0) / draw_canvas.clientWidth;
								thiz.hollow.points[5].x = thiz.hollow.points[3].x;
								break;
							default:
								break;
						}
					}
				} else if (thiz.hollow.style == 1) {
					for (i = 0; i < thiz.hollow.points_num; i++) {
						if (thiz.hollow.points_is_dragging[i]) {
							break;
						}
					}

					if (i >= thiz.hollow.points_num) {
						return;
					}

					thiz.hollow.points[i].x = pos_x / draw_canvas.width;
					thiz.hollow.points[i].y = pos_y / draw_canvas.height;

					thiz.hollow.points[i].x = Math.min(thiz.hollow.points[i].x, 1.0);
					thiz.hollow.points[i].y = Math.min(thiz.hollow.points[i].y, 1.0);
				}
			}
			refreshCalibrationLines();
		}

		function upEvent() {
			if (!thiz.downEnable) {
				return;
			}
			if (
				thiz.hollow.enable == 1 &&
				thiz.drawEnable == 1 &&
				thiz.hollow.style == 1
			) {
				for (var i = 0; i < thiz.hollow.points_num; i++) {
					thiz.hollow.points_is_dragging[i] = false;
				}
			}
			isdown = false;
			thiz.clearColor();
			refreshCalibrationLines();
		}

		var ctx0 = draw_canvas.getContext("2d");
		ctx0.clearRect(0, 0, draw_canvas.clientWidth, draw_canvas.clientHeight);
		this.moveEvt = moveEvent;
		this.refreshCal = refreshCalibrationLines;

		// PC 端
		video_div.onmousedown = function (e) {
			isdown = true;
			var doe = document.documentElement || document.body,
				scrollY = window.scrollY || doe.scrollTop; //滚动条滚动的距离
			var ev = e || window.event;
			var mouseY =
				ev.clientY + scrollY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = ev.clientX - offsetLeft;
			downEvent(mouseX, mouseY);
		};
		video_div.onmouseup = function (e) {
			upEvent();
		};
		video_div.onmouseout = function (e) {
			upEvent();
		};
		video_div.onmousemove = function (e) {
			if (!isdown) return;
			var doe = document.documentElement || document.body,
				scrollY = window.scrollY || doe.scrollTop; //滚动条滚动的距离
			var ev = e || window.event;
			var mouseY =
				ev.clientY + scrollY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = ev.clientX - offsetLeft;
			moveEvent(mouseX, mouseY);
		};

		//移动端
		//阻止浏览器的默认行为
		function stopDefault(e) {
			if (e && e.preventDefault) e.preventDefault(); //阻止默认浏览器动作(W3C)
			else window.event.returnValue = false; //IE中阻止函数器默认动作的方式
			return false;
		}
		video_div.ontouchstart = function (e) {
			isdown = true;
			var ev = e || window.event;
			var mouseY =
				e.touches[0].pageY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = e.touches[0].pageX - offsetLeft;
			downEvent(mouseX, mouseY);
			if (isdown) stopDefault(e);
		};
		video_div.ontouchend = function (e) {
			upEvent();
			stopDefault(e);
		};
		video_div.ontouchcancel = function (e) {
			upEvent();
			stopDefault(e);
		};
		video_div.ontouchmove = function (e) {
			if (!isdown) return;
			stopDefault(e);
			var ev = e || window.event;
			var mouseY =
				e.touches[0].pageY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = e.touches[0].pageX - offsetLeft;
			moveEvent(mouseX, mouseY);
		};

		return this;
	},

	toFixedPoints: function () {
		this.points.sv_dbl_point11_x = this.points.sv_dbl_point11_x.toFixed(6);
		this.points.sv_dbl_point11_y = this.points.sv_dbl_point11_y.toFixed(6);
		this.points.sv_dbl_point12_x = this.points.sv_dbl_point12_x.toFixed(6);
		this.points.sv_dbl_point12_y = this.points.sv_dbl_point12_y.toFixed(6);
		this.points.sv_dbl_point13_x = this.points.sv_dbl_point13_x.toFixed(6);
		this.points.sv_dbl_point13_y = this.points.sv_dbl_point13_y.toFixed(6);
		this.points.sv_dbl_point14_x = this.points.sv_dbl_point14_x.toFixed(6);
		this.points.sv_dbl_point14_y = this.points.sv_dbl_point14_y.toFixed(6);
		this.points.sv_dbl_point21_x = this.points.sv_dbl_point21_x.toFixed(6);
		this.points.sv_dbl_point21_y = this.points.sv_dbl_point21_y.toFixed(6);
		this.points.sv_dbl_point22_x = this.points.sv_dbl_point22_x.toFixed(6);
		this.points.sv_dbl_point22_y = this.points.sv_dbl_point22_y.toFixed(6);
		this.points.sv_dbl_point23_x = this.points.sv_dbl_point23_x.toFixed(6);
		this.points.sv_dbl_point23_y = this.points.sv_dbl_point23_y.toFixed(6);
		this.points.sv_dbl_point24_x = this.points.sv_dbl_point24_x.toFixed(6);
		this.points.sv_dbl_point24_y = this.points.sv_dbl_point24_y.toFixed(6);
	},

	clearColor: function () {
		this.colors.horizontal_line1_color =
			this.original_colors.horizontal_line1_color;
		//this.colors.horizontal_line2_color = this.original_colors.horizontal_line2_color;
		this.colors.horizontal_line3_color =
			this.original_colors.horizontal_line3_color;
		this.colors.line1_color = this.original_colors.line1_color;
		this.colors.line2_color = this.original_colors.line2_color;
		this.colors.line3_color = this.original_colors.line3_color;
		this.colors.line4_color = this.original_colors.line4_color;
	},
	selectMovingLine: function (index) {
		this.movingLines["line" + index] = 1;
	},
	clearMovingLines: function () {
		for (var i = 1; i <= 5; i++) {
			this.movingLines["line" + i] = 0;
		}
	},
	isMovingLinesEmpty: function () {
		for (var i = 1; i <= 5; i++) {
			if (this.movingLines["line" + i] > 0) return false;
		}
		return true;
	},
	addMovingPoint: function (num) {
		for (var i = 1; i <= 4; i++) {
			if (this.movingPoints["point" + i] == 0) {
				this.movingPoints["point" + i] = num;
				break;
			}
		}
	},
	clearMovingPoints: function () {
		for (var i = 1; i <= 5; i++) {
			this.movingPoints["point" + i] = 0;
		}
	},
	isMovingPointsEmpty: function () {
		for (var i = 1; i <= 5; i++) {
			if (this.movingPoints["point" + i] > 0) return false;
		}
		return true;
	},
	getStyle: function () {
		return this.points.sv_int_state;
	},
	getPoints: function () {
		this.toFixedPoints();
		var ret = new Array();
		for (var i = 1; i <= 4; i++) {
			for (var j = 1; j <= 2; j++) {
				ret[(j - 1) * 8 + (i - 1) * 2] =
					this.points["sv_dbl_point" + j + "" + i + "_x"] * 1;
				ret[(j - 1) * 8 + (i - 1) * 2 + 1] =
					this.points["sv_dbl_point" + j + "" + i + "_y"] * 1;
			}
		}
		return ret;
	},
	setDrawEnable: function (drawEnable) {
		this.drawEnable = drawEnable;
	},
	setHollow: function (arg) {
		pdHollow = arg;

		this.hollow.smooth_scale[0] = pdHollow.Scale;
		this.hollow.style = pdHollow.style;
		for (var j = 0; j < 10; j++) {
			this.hollow.points[j].x = pdHollow.points[2 * j];
			this.hollow.points[j].y = pdHollow.points[2 * j + 1];
		}

		this.hollow.enable = pdHollow.enable;
		if (this.hollow.enable == 0) {
			this.quit();
		}
		this.show();
	},
	getHollow: function () {
		var pdHollow = {};

		pdHollow.pdGreenScale = this.hollow.smooth_scale[0];

		pdHollow.points = [];
		for (var j = 0; j < 10; j++) {
			pdHollow.points.push(this.hollow.points[j].x);
			pdHollow.points.push(this.hollow.points[j].y);
		}

		pdHollow.style = this.hollow.style;
		pdHollow.enable = this.hollow.enable;

		return pdHollow;
	},
	setPoints: function (points) {
		for (var i = 1; i <= 4; i++) {
			for (var j = 1; j <= 2; j++) {
				this.points["sv_dbl_point" + j + "" + i + "_x"] =
					points[(j - 1) * 8 + (i - 1) * 2];
				this.points["sv_dbl_point" + j + "" + i + "_y"] =
					points[(j - 1) * 8 + (i - 1) * 2 + 1];
			}
		}
	},
	setState: function (sv_int_state) {
		this.points.sv_int_state = sv_int_state;
	},
	setMinorAxis: function (minorAxis) {
		this.pdMinorAxis[0] = minorAxis[0];
		this.pdMinorAxis[1] = minorAxis[1];
		this.pdMinorAxis[2] = minorAxis[2];
		this.freemode = 1;
	},
	setFreeMode: function (enable) {
		this.freemode = enable;
	},
	getMinorAxis: function () {
		var ret = new Array();
		for (var i = 0; i < 3; i++) ret[i] = this.pdMinorAxis[i].toFixed(6) * 1.0;

		return ret;
	},
	setlang: function (lang) {
		this.lang = lang;
	},
	show: function () {
		if (this.roiShowLevel > 0) {
			this.points.sv_dbl_point12_x = points.sv_dbl_point11_x;
			this.points.sv_dbl_point12_y = points.sv_dbl_point11_y;
			this.points.sv_dbl_point22_x = points.sv_dbl_point21_x;
			this.points.sv_dbl_point22_y = points.sv_dbl_point21_y;
		}
		if (this.roiShowLevel > 1) {
			this.points.sv_dbl_point13_x = points.sv_dbl_point11_x;
			this.points.sv_dbl_point13_y = points.sv_dbl_point11_y;
			this.points.sv_dbl_point23_x = points.sv_dbl_point21_x;
			this.points.sv_dbl_point23_y = points.sv_dbl_point21_y;
		}

		var min = this.roi_limit.top;
		var max = this.roi_limit.bottom;

		if (this.points.sv_int_state == 2) {
			//竖直
			this.original_colors.line1_color = this.normal_color;
			this.original_colors.line2_color = this.green_color;
			this.original_colors.line3_color = this.yellow_color;
			this.original_colors.line4_color = this.red_color;
		} else if (this.points.sv_int_state == 1) {
			//竖直
			this.original_colors.line1_color = this.red_color;
			this.original_colors.line2_color = this.yellow_color;
			this.original_colors.line3_color = this.green_color;
			this.original_colors.line4_color = this.normal_color;
		} else if (this.points.sv_int_state == 0 || this.points.sv_int_state == 4) {
			//水平
			if (
				(this.calibration_project != null &&
					this.calibration_project.indexOf("H-1.45") != -1) ||
				(this.calibration_project != null &&
					this.calibration_project.indexOf("H-1.99") != -1)
			) {
				//俯视
				this.original_colors.line1_color = this.normal_color;
				this.original_colors.line2_color = this.green_color;
				this.original_colors.line3_color = this.yellow_color;
				this.original_colors.line4_color = this.red_color;
				//this.freemode = true;
			} else {
				this.original_colors.line1_color = this.normal_color;
				this.original_colors.line2_color = this.green_color;
				this.original_colors.line3_color = this.yellow_color;
				this.original_colors.line4_color = this.red_color;
			}
		} else if (this.points.sv_int_state == 3) {
			//半圆
			this.original_colors.line1_color = this.normal_color;
			this.original_colors.line2_color = this.green_color;
			this.original_colors.line3_color = this.yellow_color;
			this.original_colors.line4_color = this.red_color;
		}
		this.clearColor();
		this.downEnable = true;
		this.refreshCal();
	},
	quit: function () {
		var ctx = this.draw_canvas.getContext("2d");
		ctx.clearRect(0, 0, this.draw_canvas.width, this.draw_canvas.height);
		this.downEnable = false;
	},
	reviseMouseX: function (arg) {
		this.widthpading = arg;
	},
	reset: function () {
		this.createCalibrationScreen();
	},
};

/**
 * 绘制测距模式参数设置图
 * @param {number} roistyle - ROI 区域类型
 */
var CalibrationDiv = function (roistyle) {
	this.roiStyle = roistyle;
	this.items = new Array();
	this.dataitems = new Array();
	this.caltop;
	this.button_height;
	this.pointIndexs;
	this.lineIndexs;
	this.createDiv();
	this.createPointInput(screen);
	this.createLineInput(screen);
};

CalibrationDiv.prototype = {
	getTop: function (e) {
		// 获取元素的绝对位置
		var offset = e.offsetTop;
		if (e.offsetParent != null) offset += this.getTop(e.offsetParent);
		return offset;
	},
	createDiv: function () {
		var camwin_div = document.getElementById("cam-win-1x");
		var selRoistyle = document.getElementById("select-roistyle");
		var calibrationinfo = document.getElementById("calibration_button");
		var submitboard = document.getElementById("btn_calibrationboard_submit");
		var calimodediv = document.getElementById("calibration-mode");
		var hollow = document.getElementById("hollow_onoff");
		this.caltop =
			this.getTop(camwin_div) +
			calimodediv.offsetHeight +
			hollow.offsetHeight +
			camwin_div.offsetHeight +
			selRoistyle.offsetHeight +
			calibrationinfo.offsetHeight +
			submitboard.offsetHeight; //元素的绝对位置+元素的高度
		this.button_height = camwin_div.offsetWidth / 30;

		for (var i = 0; i < 3; i++) {
			//画出红黄绿矩形
			var tmp_div = document.createElement("div");
			tmp_div.style.position = "absolute";
			if (i == 0) {
				tmp_div.style.background = "#00ff00";
			} else if (i == 1) {
				tmp_div.style.background = "#ffff00";
			} else if (i == 2) {
				tmp_div.style.background = "#ff0000";
			}
			if (this.roiStyle == 0) {
				tmp_div.style.top =
					this.caltop + this.button_height * (3 + i * 4) + "px";
				tmp_div.style.height = this.button_height * 4 + "px";
				tmp_div.style.width = "50%";
				tmp_div.style.left = "25%";
			} else {
				tmp_div.style.top = this.caltop + this.button_height * 3 + "px";
				tmp_div.style.height = this.button_height * 5.5 + "px";
				tmp_div.style.width = "20%";
				if (this.roiStyle == 2) {
					//左侧红，右侧绿
					tmp_div.style.left = 20 + i * 20 + "%";
				} else if (this.roiStyle == 1) {
					//左侧绿，右侧红
					tmp_div.style.right = 20 + i * 20 + "%";
				}
			}
			this.items.push(tmp_div);
			document.getElementById("win-img").appendChild(tmp_div);
		}
	},

	createPointInput: function (screen) {
		if (this.roiStyle == 0) {
			this.pointIndexs = ["24"]; //水平模式四个点
		} else if (this.roiStyle == 1 || this.roiStyle == 2) {
			this.pointIndexs = ["11", "12", "13", "14", "21", "22", "23", "24"]; //竖直模式8个点
		}

		for (index of this.pointIndexs) {
			//创建用于移动点的输入框
			var tmp_input = document.createElement("input");
			tmp_input.setAttribute("id", index + "-input");
			tmp_input.setAttribute("maxlength", 8);
			tmp_input.style.position = "absolute";
			tmp_input.style.fontSize = this.button_height + "px";
			tmp_input.style.width = this.button_height * 4 + "px";
			tmp_input.style.height = this.button_height * 2 + "px";
			if (this.roiStyle == 0) {
				if (index == "11") {
					tmp_input.style.top = this.caltop + this.button_height * 2.5 + "px";
					tmp_input.style.right = "70%";
				} else if (index == "21") {
					tmp_input.style.top = this.caltop + this.button_height * 2.5 + "px";
					tmp_input.style.left = "70%";
				} else if (index == "14") {
					tmp_input.style.top = this.caltop + this.button_height * 14 + "px";
					tmp_input.style.right = "70%";
				} else if (index == "24") {
					tmp_input.style.top = this.caltop + this.button_height * 14 + "px";
					tmp_input.style.left = "70%";
				}
			} else if (this.roiStyle == 1 || this.roiStyle == 2) {
				if (index == "11") {
					tmp_input.style.top = this.caltop + this.button_height * 2.5 + "px";
					tmp_input.style.right = "78%";
				} else if (index == "12") {
					tmp_input.style.top = this.caltop + this.button_height * 2.5 + "px";
					tmp_input.style.right = "52%";
				} else if (index == "13") {
					tmp_input.style.top = this.caltop + this.button_height * 2.5 + "px";
					tmp_input.style.left = "52%";
				} else if (index == "14") {
					tmp_input.style.top = this.caltop + this.button_height * 2.5 + "px";
					tmp_input.style.left = "78%";
				} else if (index == "21") {
					tmp_input.style.top = this.caltop + this.button_height * 8 + "px";
					tmp_input.style.right = "78%";
				} else if (index == "22") {
					tmp_input.style.top = this.caltop + this.button_height * 8 + "px";
					tmp_input.style.right = "52%";
				} else if (index == "23") {
					tmp_input.style.top = this.caltop + this.button_height * 8 + "px";
					tmp_input.style.left = "52%";
				} else if (index == "24") {
					tmp_input.style.top = this.caltop + this.button_height * 8 + "px";
					tmp_input.style.left = "78%";
				}
			}

			this.items.push(tmp_input);
			this.dataitems.push(tmp_input);
			document.getElementById("win-img").appendChild(tmp_input);
		}
	},

	createLineInput: function () {
		if (this.roiStyle == 0) {
			this.lineIndexs = ["1", "2", "3"];
		} else if (this.roiStyle == 1 || this.roiStyle == 2) {
			this.lineIndexs = ["1"];
		}

		var tmp_input = document.createElement("input");
		for (index of this.lineIndexs) {
			//创建用于移动线的输入框
			var tmp_input = document.createElement("input");
			tmp_input.setAttribute("id", index + "-y-input");
			tmp_input.setAttribute("maxlength", 8);
			tmp_input.style.position = "absolute";
			tmp_input.style.fontSize = this.button_height + "px";
			tmp_input.style.left = "50%";
			tmp_input.style.marginLeft = "-" + (this.button_height * 3) / 2 + "px";
			tmp_input.style.width = this.button_height * 4 + "px";
			tmp_input.style.height = this.button_height * 2 + "px";
			if (this.roiStyle == 0) {
				tmp_input.style.top =
					this.caltop +
					this.button_height * (2.5 + 4 * (parseInt(index) - 1)) +
					"px";
			} else if (this.roiStyle == 1 || this.roiStyle == 2) {
				tmp_input.style.top =
					this.caltop + this.button_height * (2 + 4 * parseInt(index)) + "px";
			}
			this.items.push(tmp_input);
			this.dataitems.push(tmp_input);
			document.getElementById("win-img").appendChild(tmp_input);
		}
	},

	setMeasureItemsValue: function (points) {
		//console.log(points);
		if (points) {
			var i = 0;
			for (index of this.pointIndexs) {
				document.getElementById(index + "-input").value = points[i++];
			}
			for (index of this.lineIndexs) {
				document.getElementById(index + "-y-input").value = points[i++];
			}
		} else {
			alert(
				"setMeasureItemsValue wrong return data : " + JSON.stringify(points)
			);
		}
	},

	displayMeasureItems: function (display) {
		for (item of this.items) {
			if (this.roiStyle == 0) {
				item.style.display = display;
			} else {
				item.style.display = "none";
			}
		}
	},

	setInputChange: function (screen) {
		function getPoints(retdata) {
			$.ajax({
				type: "GET",
				url: "/config",
				async: false,
				success: function (data, status) {
					//console.log(JSON.stringify(data));
					const pd =
						data.algConfig[window.ch === "ch0" ? "algChn0" : "algChn1"].pd;
					retdata.pdRoiStyle = pd.pdRoiStyle;
					retdata.pdRoiOutline = pd.pdRoiOutline;
					retdata.inputdata = [
						pd.pdDetectWBorder,
						pd.pdDetectGreenFBorder,
						pd.pdDetectYellowFBorder,
						pd.pdDetectRedFBorder,
					];
				}.bind(this),
				error: function (XMLHttpRequest) {
					//this.view.showErrorInfo("get-config-fail", JSON.parse(XMLHttpRequest.responseText));
				}.bind(this),
				dataType: "json",
			});
		}

		var data = new Array();
		for (var i = 0; i < this.dataitems.length; i++) {
			//先记录其他input的value
			data[i] = parseInt(this.dataitems[i].value, 10);
		}

		for (tmp_input of this.dataitems) {
			//所有距离变化做同样处理，post距离后，重新填充距离，并且绘制roi所有点
			tmp_input.onkeydown = function (retdata) {
				if (window.event.keyCode == 13) {
					switch (this.id) {
						case "24-input":
							data[0] = parseInt(this.value, 10);
							break;
						case "1-y-input":
							data[1] = parseInt(this.value, 10);
							break;
						case "2-y-input":
							data[2] = parseInt(this.value, 10);
							break;
						case "3-y-input":
							data[3] = parseInt(this.value, 10);
							break;
						default:
					}
					var json_str;
					var json = {
						accessApi: { password: "" },
						algConfig: {
							[window.ch === "ch0" ? "algChn0" : "algChn1"]: {
								pdWorkMode: 1,
								pdDetectWBorder: data[0],
								pdDetectRedFBorder: data[3],
								pdDetectYellowFBorder: data[2],
								pdDetectGreenFBorder: data[1],
							},
						},
					};
					json_str = JSON.stringify(json);
					console.log(json_str);
					$.ajax({
						type: "POST",
						url: "/config",
						data: json_str,
						success: function (data) {
							getPoints(retdata); //roi直接生成在图片上，所以不用在webui上绘制roi
							/*screen.setState(retdata.pdRoiStyle);
							console.log(retdata.pdRoiOutline);					
							screen.setPoints(retdata.pdRoiOutline);
							screen.clearColor();
							screen.refreshCal();
							console.log(retdata.pdRoiStyle+" -------"+retdata.pdRoiOutline+"--------"+retdata.inputdata);*/
							var lineIndexs = ["1", "2", "3"];
							var pointIndexs = ["24"];
							if (retdata.inputdata) {
								var i = 0;
								for (index of pointIndexs) {
									document.getElementById(index + "-input").value =
										retdata.inputdata[i++];
								}
								for (index of lineIndexs) {
									document.getElementById(index + "-y-input").value =
										retdata.inputdata[i++];
								}
							} else {
								alert(
									"setMeasureItemsValue wrong return data : " +
										JSON.stringify(retdata.inputdata)
								);
							}
						}.bind(this),
						error: function (XMLHttpRequest) {
							//this.showErrorInfo("set calibrationDistance-fail",JSON.parse(XMLHttpRequest.responseText));
						}.bind(this),
						dataType: "json",
					});
				}
			};
		}
	},
};

var APCDetectionScreen = function (draw_canvas) {
	this.moveEvt = null;
	//this.customer = customer;
	this.draw_canvas = draw_canvas;
	this.downEnable = false;
	this.freemode = false; //非自由模式，左边的点限制在同一直线上，右边的点限制在同一直线上
	this.direction = 0;
	this.widthpading = 0;

	this.apcPoints = {};
	this.apcPoints.sv_dbl_point11_x = 0.1;
	this.apcPoints.sv_dbl_point11_y = 0.2;
	this.apcPoints.sv_dbl_point21_x = 0.8;
	this.apcPoints.sv_dbl_point21_y = 0.2;
	this.apcPoints.sv_dbl_point12_x = 0.1;
	this.apcPoints.sv_dbl_point12_y = 0.9;
	this.apcPoints.sv_dbl_point22_x = 0.8;
	this.apcPoints.sv_dbl_point22_y = 0.9;
	this.apcPoints.sv_dbl_pointd_x = 0.5;
	this.apcPoints.sv_dbl_pointd_y = 0.5;

	this.roi_limit = {};
	this.roi_limit.left = 0.0;
	this.roi_limit.right = 1.0;
	this.roi_limit.top = 0.0;
	this.roi_limit.bottom = 1.0;

	this.normal_color = "#00FF00";
	this.choice_color = "#007F7F";

	this.original_colors = {};
	this.original_colors.line_color = this.normal_color;

	this.colors = {};
	this.colors.line_color = this.normal_color;
	this.colors.yellow_color = "#d26512";

	this.movingLine = 0; //侧边使用
	this.movingLines = {}; //A32前方标定使用的
	this.movingLines.line1 = 0;
	this.movingLines.line2 = 0;

	this.refreshCal = null;

	this.createCalibrationScreen();
};

APCDetectionScreen.prototype = {
	createCalibrationScreen: function () {
		var thiz = this;
		var w = document.getElementById("img-solo").width,
			h = document.getElementById("img-solo").height;
		var offsetLeft = document.getElementById("img-solo").offsetLeft;
		var draw_canvas = this.draw_canvas;
		draw_canvas.width = w;
		draw_canvas.height = h;
		draw_canvas.style.setProperty("width", w + "px");
		draw_canvas.style.left = offsetLeft + "px";
		var video_div = draw_canvas;
		var ctx = draw_canvas.getContext("2d");
		ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);
		ctx.strokeStyle = "#FF0000";
		ctx.beginPath();
		ctx.lineWidth = 3;
		ctx.moveTo(0, 0);
		ctx.lineTo(0, draw_canvas.height);
		ctx.lineTo(draw_canvas.width, draw_canvas.height);
		ctx.lineTo(draw_canvas.width, 0);
		ctx.lineTo(0, 0);
		ctx.stroke();

		var trigger_d = 20;
		var trigger_r = 10;
		var isdown = false;

		var downPos = {}; //记录鼠标点击的位置
		downPos.x = 0;
		downPos.y = 0;

		function contrastXYpos(src_pos, dest_pos) {
			var d1 = dest_pos - src_pos;
			if (d1 < trigger_r && d1 > -trigger_r) return true;
			else return false;
		}

		function refreshCalibrationLines() {
			var ctx = draw_canvas.getContext("2d");
			ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);

			/*       画线，没有拖拽点    */
			function paint_line2(
				point1_x,
				point1_y,
				point2_x,
				point2_y,
				linewidth,
				color
			) {
				// 绘制线段
				ctx.strokeStyle = color;
				ctx.beginPath();
				ctx.lineWidth = linewidth;
				ctx.moveTo(point1_x, point1_y);
				ctx.stroke();

				ctx.lineTo(point2_x, point2_y);
				ctx.stroke();
			}

			point1_x = thiz.apcPoints.sv_dbl_point11_x * draw_canvas.width;
			point1_y = thiz.apcPoints.sv_dbl_point11_y * draw_canvas.height;
			point2_x = thiz.apcPoints.sv_dbl_point12_x * draw_canvas.width;
			point2_y = thiz.apcPoints.sv_dbl_point12_y * draw_canvas.height;
			point3_x = thiz.apcPoints.sv_dbl_point21_x * draw_canvas.width;
			point3_y = thiz.apcPoints.sv_dbl_point21_y * draw_canvas.height;
			point4_x = thiz.apcPoints.sv_dbl_point22_x * draw_canvas.width;
			point4_y = thiz.apcPoints.sv_dbl_point22_y * draw_canvas.height;
			paint_line2(
				point1_x,
				point1_y,
				point2_x,
				point2_y,
				3,
				thiz.colors.line_color
			);
			paint_line2(
				point1_x,
				point1_y,
				point3_x,
				point3_y,
				3,
				thiz.colors.line_color
			);
			paint_line2(
				point2_x,
				point2_y,
				point4_x,
				point4_y,
				3,
				thiz.colors.line_color
			);
			paint_line2(
				point3_x,
				point3_y,
				point4_x,
				point4_y,
				3,
				thiz.colors.line_color
			);

			if (thiz.direction == 0 || thiz.direction == 1) {
				divider1_x = thiz.apcPoints.sv_dbl_point11_x * draw_canvas.width;
				divider1_y = thiz.apcPoints.sv_dbl_pointd_y * draw_canvas.height;
				divider2_x = thiz.apcPoints.sv_dbl_point22_x * draw_canvas.width;
				divider2_y = thiz.apcPoints.sv_dbl_pointd_y * draw_canvas.height;
				paint_line2(
					divider1_x,
					divider1_y,
					divider2_x,
					divider2_y,
					3,
					thiz.colors.line_color
				);
				if (thiz.direction == 0) {
					paint_line2(
						divider1_x,
						divider1_y,
						point2_x,
						point2_y,
						3,
						thiz.colors.yellow_color
					);
					paint_line2(
						divider2_x,
						divider2_y,
						point4_x,
						point4_y,
						3,
						thiz.colors.yellow_color
					);
					paint_line2(
						point2_x,
						point2_y,
						point4_x,
						point4_y,
						3,
						thiz.colors.yellow_color
					);
				} else {
					paint_line2(
						point1_x,
						point1_y,
						point3_x,
						point3_y,
						3,
						thiz.colors.yellow_color
					);
					paint_line2(
						divider1_x,
						divider1_y,
						point1_x,
						point1_y,
						3,
						thiz.colors.yellow_color
					);
					paint_line2(
						divider2_x,
						divider2_y,
						point3_x,
						point3_y,
						3,
						thiz.colors.yellow_color
					);
				}
			} else if (thiz.direction == 2 || thiz.direction == 3) {
				divider1_y = thiz.apcPoints.sv_dbl_point11_y * draw_canvas.height;
				divider1_x = thiz.apcPoints.sv_dbl_pointd_x * draw_canvas.width;
				divider2_x = thiz.apcPoints.sv_dbl_pointd_x * draw_canvas.width;
				divider2_y = thiz.apcPoints.sv_dbl_point22_y * draw_canvas.height;
				paint_line2(
					divider1_x,
					divider1_y,
					divider2_x,
					divider2_y,
					3,
					thiz.colors.line_color
				);
				if (thiz.direction == 3) {
					paint_line2(
						divider1_x,
						divider1_y,
						point3_x,
						point3_y,
						3,
						thiz.colors.yellow_color
					);
					paint_line2(
						divider2_x,
						divider2_y,
						point4_x,
						point4_y,
						3,
						thiz.colors.yellow_color
					);
					paint_line2(
						point3_x,
						point3_y,
						point4_x,
						point4_y,
						3,
						thiz.colors.yellow_color
					);
				} else {
					paint_line2(
						point1_x,
						point1_y,
						divider1_x,
						divider1_y,
						3,
						thiz.colors.yellow_color
					);
					paint_line2(
						point2_x,
						point2_y,
						divider2_x,
						divider2_y,
						3,
						thiz.colors.yellow_color
					);
					paint_line2(
						point1_x,
						point1_y,
						point2_x,
						point2_y,
						3,
						thiz.colors.yellow_color
					);
				}
			}
		}

		function downEvent(pos_x, pos_y) {
			thiz.movingLine = "none";
			thiz.clearMovingLines();

			if (!thiz.downEnable) {
				return;
			}

			if (1) {
				//上线 11  下线12  左线21  右线22
				for (i = 1; i <= 2; i++) {
					if (
						contrastXYpos(
							thiz.apcPoints["sv_dbl_point1" + i + "_y"] *
								draw_canvas.clientHeight,
							pos_y
						)
					) {
						//矩形的上下横线
						if (
							pos_x <
								thiz.apcPoints.sv_dbl_point21_x * draw_canvas.clientWidth &&
							pos_x > thiz.apcPoints.sv_dbl_point11_x * draw_canvas.clientWidth
						)
							thiz.movingLines.line1 = 10 + i;
					}
					if (
						contrastXYpos(
							thiz.apcPoints["sv_dbl_point" + i + "1_x"] *
								draw_canvas.clientWidth,
							pos_x
						)
					) {
						if (
							pos_y <
								thiz.apcPoints.sv_dbl_point12_y * draw_canvas.clientHeight &&
							pos_y > thiz.apcPoints.sv_dbl_point11_y * draw_canvas.clientHeight
						)
							thiz.movingLines.line1 = 20 + i;
					}
				}
				console.log(thiz.movingLines.line1);
				if (thiz.direction == 0 || thiz.direction == 1) {
					if (
						contrastXYpos(
							thiz.apcPoints.sv_dbl_pointd_y * draw_canvas.clientHeight,
							pos_y
						)
					) {
						//矩形的上下横线
						if (
							pos_x <
								thiz.apcPoints.sv_dbl_point22_x * draw_canvas.clientWidth &&
							pos_x > thiz.apcPoints.sv_dbl_point11_x * draw_canvas.clientWidth
						)
							thiz.movingLines.line2 = 31;
					}
				} else if (thiz.direction == 2 || thiz.direction == 3) {
					if (
						contrastXYpos(
							thiz.apcPoints.sv_dbl_pointd_x * draw_canvas.clientWidth,
							pos_x
						)
					) {
						if (
							pos_y <
								thiz.apcPoints.sv_dbl_point22_y * draw_canvas.clientHeight &&
							pos_y > thiz.apcPoints.sv_dbl_point11_y * draw_canvas.clientHeight
						)
							thiz.movingLines.line2 = 32;
					}
				}
				console.log(thiz.movingLines.line2);
			}
		}

		function moveEvent(pos_x, pos_y) {
			if (!thiz.downEnable) {
				return;
			}
			if (1) {
				var frameLine = thiz.movingLines.line1;
				var dividerLine = thiz.movingLines.line2;
				if (frameLine > 0) {
					//如果中间分界线与边框线重合，优先选中边框线
					thiz.clearColor();
					thiz.movingLines.line1 = frameLine;

					switch (frameLine) {
						case 11:
							thiz.apcPoints.sv_dbl_point11_y =
								(pos_y * 1.0) / draw_canvas.clientHeight;
							if (
								(thiz.direction == 0 || thiz.direction == 1) &&
								thiz.apcPoints.sv_dbl_point11_y > thiz.apcPoints.sv_dbl_pointd_y
							)
								thiz.apcPoints.sv_dbl_point11_y =
									thiz.apcPoints.sv_dbl_pointd_y;
							thiz.apcPoints.sv_dbl_point21_y = thiz.apcPoints.sv_dbl_point11_y;
							break;
						case 12:
							thiz.apcPoints.sv_dbl_point12_y =
								(pos_y * 1.0) / draw_canvas.clientHeight;
							if (
								(thiz.direction == 0 || thiz.direction == 1) &&
								thiz.apcPoints.sv_dbl_point12_y < thiz.apcPoints.sv_dbl_pointd_y
							)
								thiz.apcPoints.sv_dbl_point12_y =
									thiz.apcPoints.sv_dbl_pointd_y;
							thiz.apcPoints.sv_dbl_point22_y = thiz.apcPoints.sv_dbl_point12_y;
							break;
						case 21:
							thiz.apcPoints.sv_dbl_point11_x =
								(pos_x * 1.0) / draw_canvas.clientWidth;
							if (
								(thiz.direction == 2 || thiz.direction == 3) &&
								thiz.apcPoints.sv_dbl_point11_x > thiz.apcPoints.sv_dbl_pointd_x
							)
								thiz.apcPoints.sv_dbl_point11_x =
									thiz.apcPoints.sv_dbl_pointd_x;
							thiz.apcPoints.sv_dbl_point12_x = thiz.apcPoints.sv_dbl_point11_x;
							break;
						case 22:
							thiz.apcPoints.sv_dbl_point21_x =
								(pos_x * 1.0) / draw_canvas.clientWidth;
							if (
								(thiz.direction == 2 || thiz.direction == 3) &&
								thiz.apcPoints.sv_dbl_point21_x < thiz.apcPoints.sv_dbl_pointd_x
							)
								thiz.apcPoints.sv_dbl_point21_x =
									thiz.apcPoints.sv_dbl_pointd_x;
							thiz.apcPoints.sv_dbl_point22_x = thiz.apcPoints.sv_dbl_point21_x;
							break;
						default:
							break;
					}
				} else if (dividerLine > 0) {
					thiz.clearColor();
					thiz.movingLines.line2 = dividerLine;
					switch (dividerLine) {
						case 31:
							thiz.apcPoints.sv_dbl_pointd_y =
								(pos_y * 1.0) / draw_canvas.clientHeight;
							if (
								thiz.apcPoints.sv_dbl_pointd_y < thiz.apcPoints.sv_dbl_point11_y
							)
								thiz.apcPoints.sv_dbl_pointd_y =
									thiz.apcPoints.sv_dbl_point11_y;
							if (
								thiz.apcPoints.sv_dbl_pointd_y > thiz.apcPoints.sv_dbl_point22_y
							)
								thiz.apcPoints.sv_dbl_pointd_y =
									thiz.apcPoints.sv_dbl_point22_y;
							break;
						case 32:
							thiz.apcPoints.sv_dbl_pointd_x =
								(pos_x * 1.0) / draw_canvas.clientWidth;
							if (
								thiz.apcPoints.sv_dbl_pointd_x < thiz.apcPoints.sv_dbl_point11_x
							)
								thiz.apcPoints.sv_dbl_pointd_x =
									thiz.apcPoints.sv_dbl_point11_x;
							if (
								thiz.apcPoints.sv_dbl_pointd_x > thiz.apcPoints.sv_dbl_point22_x
							)
								thiz.apcPoints.sv_dbl_pointd_x =
									thiz.apcPoints.sv_dbl_point22_x;
							break;
						default:
							break;
					}
				}
			}
			refreshCalibrationLines();
		}

		function upEvent() {
			if (!thiz.downEnable) {
				return;
			}
			isdown = false;
			thiz.clearColor();
			refreshCalibrationLines();
		}

		var ctx0 = draw_canvas.getContext("2d");
		ctx0.clearRect(0, 0, draw_canvas.clientWidth, draw_canvas.clientHeight);
		this.moveEvt = moveEvent;
		this.refreshCal = refreshCalibrationLines;

		// PC 端
		video_div.onmousedown = function (e) {
			isdown = true;
			var doe = document.documentElement || document.body,
				scrollY = window.scrollY || doe.scrollTop; //滚动条滚动的距离
			var ev = e || window.event;
			var mouseY =
				ev.clientY + scrollY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = ev.clientX - offsetLeft;
			downEvent(mouseX, mouseY);
		};
		video_div.onmouseup = function (e) {
			upEvent();
		};
		video_div.onmouseout = function (e) {
			upEvent();
		};
		video_div.onmousemove = function (e) {
			if (!isdown) return;
			var doe = document.documentElement || document.body,
				scrollY = window.scrollY || doe.scrollTop; //滚动条滚动的距离
			var ev = e || window.event;
			var mouseY =
				ev.clientY + scrollY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = ev.clientX - offsetLeft;
			moveEvent(mouseX, mouseY);
		};

		//移动端
		//阻止浏览器的默认行为
		function stopDefault(e) {
			if (e && e.preventDefault) e.preventDefault(); //阻止默认浏览器动作(W3C)
			else window.event.returnValue = false; //IE中阻止函数器默认动作的方式
			return false;
		}
		video_div.ontouchstart = function (e) {
			isdown = true;
			var ev = e || window.event;
			var mouseY =
				e.touches[0].pageY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = e.touches[0].pageX - offsetLeft;
			downEvent(mouseX, mouseY);
			if (isdown) stopDefault(e);
		};
		video_div.ontouchend = function (e) {
			upEvent();
			stopDefault(e);
		};
		video_div.ontouchcancel = function (e) {
			upEvent();
			stopDefault(e);
		};
		video_div.ontouchmove = function (e) {
			if (!isdown) return;
			stopDefault(e);
			var ev = e || window.event;
			var mouseY =
				e.touches[0].pageY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = e.touches[0].pageX - offsetLeft;
			moveEvent(mouseX, mouseY);
		};

		return this;
	},

	toFixedPoints: function () {
		this.apcPoints.sv_dbl_point11_x =
			this.apcPoints.sv_dbl_point11_x.toFixed(6);
		this.apcPoints.sv_dbl_point11_y =
			this.apcPoints.sv_dbl_point11_y.toFixed(6);
		this.apcPoints.sv_dbl_point21_x =
			this.apcPoints.sv_dbl_point21_x.toFixed(6);
		this.apcPoints.sv_dbl_point21_y =
			this.apcPoints.sv_dbl_point21_y.toFixed(6);
		this.apcPoints.sv_dbl_point12_x =
			this.apcPoints.sv_dbl_point12_x.toFixed(6);
		this.apcPoints.sv_dbl_point12_y =
			this.apcPoints.sv_dbl_point12_y.toFixed(6);
		this.apcPoints.sv_dbl_point22_x =
			this.apcPoints.sv_dbl_point22_x.toFixed(6);
		this.apcPoints.sv_dbl_point22_y =
			this.apcPoints.sv_dbl_point22_y.toFixed(6);
		this.apcPoints.sv_dbl_pointd_x = this.apcPoints.sv_dbl_pointd_x.toFixed(6);
		this.apcPoints.sv_dbl_pointd_y = this.apcPoints.sv_dbl_pointd_y.toFixed(6);
	},

	clearColor: function () {
		this.colors.line_color = this.original_colors.line_color;
	},
	selectMovingLine: function (index) {
		this.movingLines["line" + index] = 1;
	},
	clearMovingLines: function () {
		for (var i = 1; i <= 2; i++) {
			this.movingLines["line" + i] = 0;
		}
	},
	isMovingLinesEmpty: function () {
		for (var i = 1; i <= 2; i++) {
			if (this.movingLines["line" + i] > 0) return false;
		}
		return true;
	},

	getFramePoints: function () {
		this.toFixedPoints();
		var ret = new Array();
		ret[0] = this.apcPoints.sv_dbl_point11_x * 1;
		ret[1] = this.apcPoints.sv_dbl_point11_y * 1;
		ret[2] = this.apcPoints.sv_dbl_point22_x * 1;
		ret[3] = this.apcPoints.sv_dbl_point22_y * 1;
		return ret;
	},

	getDividerPoint: function () {
		var ret = new Array();
		ret[0] = this.apcPoints.sv_dbl_pointd_x * 1;
		ret[1] = this.apcPoints.sv_dbl_pointd_y * 1;
		return ret;
	},
	setPoints: function (points, divider) {
		this.apcPoints.sv_dbl_point11_x = points[0];
		this.apcPoints.sv_dbl_point11_y = points[1];
		this.apcPoints.sv_dbl_point21_x = points[2];
		this.apcPoints.sv_dbl_point21_y = points[1];
		this.apcPoints.sv_dbl_point12_x = points[0];
		this.apcPoints.sv_dbl_point12_y = points[3];
		this.apcPoints.sv_dbl_point22_x = points[2];
		this.apcPoints.sv_dbl_point22_y = points[3];
		this.apcPoints.sv_dbl_pointd_x = divider[0];
		this.apcPoints.sv_dbl_pointd_y = divider[1];
	},

	setDirection: function (direction) {
		this.direction = direction;
	},

	show: function () {
		this.original_colors.line_color = this.normal_color;

		this.clearColor();
		this.downEnable = true;
		this.refreshCal();
	},
	quit: function () {
		var ctx = this.draw_canvas.getContext("2d");
		ctx.clearRect(0, 0, this.draw_canvas.width, this.draw_canvas.height);
		this.downEnable = false;
	},
	reviseMouseX: function (arg) {
		this.widthpading = arg;
	},
};

/**
 * 绘制A32的ROi标定区域：画板模式
 * @param {number} hollow - 是否开启镂空ROI区域？0 否, 1 是
 * @param {canvas} draw_canvas - canvas html 画布对象
 */
var CalibrationDrawBoard = function (draw_canvas) {
	this.widthpading = 0;
	this.moveEvt = null;
	//this.calibration_project = calibration_project;
	//this.customer = customer;
	this.draw_canvas = draw_canvas;

	this.points_num = 10;
	this.points = [];
	for (var i = 0; i < 3; i++ /* 绿，黄，红色 */) {
		this.points.push([]);
		for (var j = 0; j < this.points_num; j++ /* 10 个点 */) {
			this.points[i].push({});
		}
	}

	this.roi_color = ["#00FF00", "#FFFF00", "#FF0000"]; // 绿色，黄色，红色
	this.smooth_scale = [0.0, 0.0, 0.0]; // 光滑系数
	this.roi_choice = 2; // 选择的ROI区域(红色)
	this.isHideOther = false; // 隐藏未选中的区域
	this.points_is_dragging = Array(this.points_num).fill(false); // 标记哪个点被选中

	/* 初始化值
		每一个区域有10个点，上下两两重合
	0.2	*————————*————————*————————*
		| 						   | 
	0.3	* 		   Green           *
		| 		 		   		   |
	0.4	*————————*————————*————————* 
Y		| 						   | 
	0.5	* 		   Yellow          *
		| 		 		   		   |
	0.6	*————————*————————*————————* 
		| 						   | 
	0.7	* 		     Red           *
		| 		 		   		   |
	0.8	*————————*————————*————————* 
		0.2		0.4		 0.6	  0.8
					  X
	*/

	for (var i = 0; i < 3; i++) {
		var _init_xlist = [0.2, 0.4, 0.6, 0.8, 0.8, 0.8, 0.6, 0.4, 0.2, 0.2];
		var _init_ylist = [0.2, 0.2, 0.2, 0.2, 0.3, 0.4, 0.4, 0.4, 0.4, 0.3];
		for (var j = 0; j < 10; j++) {
			this.points[i][j].x = _init_xlist[j];
			this.points[i][j].y = _init_ylist[j] + i * 0.2;
		}
	}

	this.refreshCal = null;
	this.createCalibrationDrawBoard();
};

CalibrationDrawBoard.prototype = {
	createCalibrationDrawBoard: function () {
		var thiz = this;
		var w = document.getElementById("img-solo").width;
		var h = document.getElementById("img-solo").height;
		var offsetLeft = document.getElementById("img-solo").offsetLeft;
		var draw_canvas = this.draw_canvas;
		var radius_scope = 10;

		draw_canvas.width = w;
		draw_canvas.height = h;
		draw_canvas.style.setProperty("width", w + "px");
		draw_canvas.style.left = offsetLeft + "px";

		var video_div = draw_canvas;
		var ctx = draw_canvas.getContext("2d");
		ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);

		var isdown = false;

		/* 更新标定线 */
		function refreshCalibrationLines() {
			paint_drawboard();
		}

		function getDistance(x1, y1, x2, y2) {
			let dep = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));
			return dep;
		}

		function downEvent(pos_x, pos_y) {
			for (var i = 0; i < thiz.points_num; i++) {
				var choice = thiz.roi_choice;
				if (
					getDistance(
						w * thiz.points[choice][i].x,
						h * thiz.points[choice][i].y,
						pos_x,
						pos_y
					) < radius_scope
				) {
					thiz.points_is_dragging[i] = true;
					break;
				}
			}
		}

		function upEvent() {
			for (var i = 0; i < thiz.points_num; i++) {
				thiz.points_is_dragging[i] = false;
			}
		}

		function moveEvent(pos_x, pos_y) {
			var i;
			var choice = thiz.roi_choice;
			for (i = 0; i < thiz.points_num; i++) {
				if (thiz.points_is_dragging[i]) {
					break;
				}
			}

			if (i >= thiz.points_num) {
				return;
			}

			thiz.points[choice][i].x = pos_x / w;
			thiz.points[choice][i].y = pos_y / h;

			thiz.points[choice][i].x = Math.min(thiz.points[choice][i].x, 1.0);
			thiz.points[choice][i].y = Math.min(thiz.points[choice][i].y, 1.0);

			paint_drawboard();
		}

		var ctx0 = draw_canvas.getContext("2d");
		ctx0.clearRect(0, 0, draw_canvas.clientWidth, draw_canvas.clientHeight);
		this.moveEvt = moveEvent;
		this.refreshCal = refreshCalibrationLines;

		// PC 端
		video_div.onmousedown = function (e) {
			isdown = true;
			var doe = document.documentElement || document.body,
				scrollY = window.scrollY || doe.scrollTop; //滚动条滚动的距离
			var ev = e || window.event;
			var mouseY =
				ev.clientY + scrollY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = ev.clientX - offsetLeft;
			downEvent(mouseX, mouseY);
		};
		video_div.onmouseup = function (e) {
			upEvent();
		};
		video_div.onmouseout = function (e) {
			upEvent();
		};
		video_div.onmousemove = function (e) {
			if (!isdown) return;
			var doe = document.documentElement || document.body;
			var scrollY = window.scrollY || doe.scrollTop; //滚动条滚动的距离
			var ev = e || window.event;
			var mouseY =
				ev.clientY + scrollY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = ev.clientX - offsetLeft;
			moveEvent(mouseX, mouseY);
		};

		//移动端
		//阻止浏览器的默认行为
		function stopDefault(e) {
			if (e && e.preventDefault) e.preventDefault(); //阻止默认浏览器动作(W3C)
			else window.event.returnValue = false; //IE中阻止函数器默认动作的方式
			return false;
		}
		video_div.ontouchstart = function (e) {
			isdown = true;
			var ev = e || window.event;
			var mouseY =
				e.touches[0].pageY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = e.touches[0].pageX - offsetLeft;
			downEvent(mouseX, mouseY);
			if (isdown) stopDefault(e);
		};
		video_div.ontouchend = function (e) {
			upEvent();
			stopDefault(e);
		};
		video_div.ontouchcancel = function (e) {
			upEvent();
			stopDefault(e);
		};
		video_div.ontouchmove = function (e) {
			if (!isdown) return;
			stopDefault(e);
			var ev = e || window.event;
			var mouseY =
				e.touches[0].pageY - document.getElementById("cam-win-1x").offsetTop; //firefox不支持offsetY，所以用通过这样算出
			var mouseX = e.touches[0].pageX - offsetLeft;
			moveEvent(mouseX, mouseY);
		};

		function paint_drawboard() {
			var i;
			var choice, scale, points_num, points, color;
			var roi_list = [0, 1, 2, thiz.roi_choice];

			var ctx = draw_canvas.getContext("2d");
			ctx.beginPath();
			ctx.clearRect(0, 0, draw_canvas.width, draw_canvas.height);
			ctx.stroke();

			/* 画四次,保证选择的颜色再最上方 */
			for (i = 0; i < 4; i++) {
				if (thiz.isHideOther == true && i < 3) {
					continue;
				}

				choice = roi_list[i];
				scale = thiz.smooth_scale[choice];
				points_num = thiz.points_num;
				points = thiz.points[choice];
				color = thiz.roi_color[choice];

				paint_3bezier_fit(points, points_num, scale, color);
			}

			/* 绘制圆圈 */
			for (i = 0; i < points_num; i++) {
				var point_center = {};
				point_center.x = parseInt(w * points[i].x);
				point_center.y = parseInt(h * points[i].y);
				paint_circle(point_center, radius_scope, color);
			}
		}

		function paint_3bezier_fit(rawpoints, points_num, scale, color) {
			var i, nexti, backi;

			var points = [];
			var pCenter = [];
			var pExtra = [];
			var stmidnmid = {};
			var offsetx, offsety, addx, addy, extraindex;

			for (i = 0; i < points_num; i++) points.push({});

			for (i = 0; i < points_num; i++) pCenter.push({});

			for (i = 0; i < 2 * points_num; i++) pExtra.push({});

			for (i = 0; i < points_num; i++) {
				points[i].x = parseInt(rawpoints[i].x * w);
				points[i].y = parseInt(rawpoints[i].y * h);
			}

			for (i = 0; i < points_num; i++) {
				nexti = (i + 1) % points_num;
				pCenter.push({});
				pCenter[i].x = (points[i].x + points[nexti].x) / 2;
				pCenter[i].y = (points[i].y + points[nexti].y) / 2;

				pCenter[i].x = parseInt(pCenter[i].x);
				pCenter[i].y = parseInt(pCenter[i].y);
			}

			for (i = 0; i < points_num; i++) {
				nexti = (i + 1) % points_num;
				backi = (i - 1 + points_num) % points_num;

				stmidnmid.x = (pCenter[i].x + pCenter[backi].x) / 2;
				stmidnmid.y = (pCenter[i].y + pCenter[backi].y) / 2;

				offsetx = points[i].x - stmidnmid.x;
				offsety = points[i].y - stmidnmid.y;

				extraindex = 2 * i;
				pExtra[extraindex].x = pCenter[backi].x + offsetx;
				pExtra[extraindex].y = pCenter[backi].y + offsety;
				addx = (pExtra[extraindex].x - points[i].x) * scale;
				addy = (pExtra[extraindex].y - points[i].y) * scale;

				pExtra[extraindex].x = parseInt(points[i].x + addx);
				pExtra[extraindex].y = parseInt(points[i].y + addy);

				extraindex = (extraindex + 1) % (2 * points_num);
				pExtra[extraindex].x = pCenter[i].x + offsetx;
				pExtra[extraindex].y = pCenter[i].y + offsety;
				addx = (pExtra[extraindex].x - points[i].x) * scale;
				addy = (pExtra[extraindex].y - points[i].y) * scale;
				pExtra[extraindex].x = parseInt(points[i].x + addx);
				pExtra[extraindex].y = parseInt(points[i].y + addy);
			}

			for (i = 0; i < points_num; i++) {
				var point1 = points[i];
				var point2 = pExtra[2 * i + 1];
				var point3 = pExtra[(2 * i + 2) % (2 * points_num)];
				var point4 = points[(i + 1) % points_num];

				paint_3bezier_curve(point1, point2, point3, point4, color);
			}
		}

		function paint_3bezier_curve(point1, point2, point3, point4, color) {
			ctx.beginPath();
			ctx.lineWidth = 2;
			ctx.strokeStyle = color;
			ctx.moveTo(point1.x, point1.y);
			ctx.bezierCurveTo(
				point2.x,
				point2.y,
				point3.x,
				point3.y,
				point4.x,
				point4.y
			);
			ctx.stroke();

			// ctx.beginPath();
			// ctx.arc(point2.x, point2.y, 2, 0, 2*Math.PI);
			// ctx.stroke();

			// ctx.beginPath();
			// ctx.arc(point3.x, point3.y, 2, 0, 2*Math.PI);
			// ctx.stroke();
		}

		function paint_circle(point_center, radius, color) {
			ctx.beginPath();
			ctx.lineWidth = 2;
			ctx.strokeStyle = color;
			ctx.arc(point_center.x, point_center.y, radius, 0, 2 * Math.PI);
			ctx.stroke();
		}

		return this;
	},

	toFixedPoints: function () {},

	clearColor: function () {},
	selectMovingLine: function (index) {},
	clearMovingLines: function () {},
	isMovingLinesEmpty: function () {},
	addMovingPoint: function (num) {},
	clearMovingPoints: function () {},
	isMovingPointsEmpty: function () {},
	getStyle: function () {},
	getPoints: function () {},
	setDrawEnable: function (drawEnable) {},
	setHollowEnable: function (hollowEnable) {},
	setPoints: function (points) {},
	setState: function (sv_int_state) {},
	setMinorAxis: function (minorAxis) {},
	setFreeMode: function (enable) {},
	getMinorAxis: function () {},

	getRoiChoice: function () {
		return this.roi_choice;
	},
	setRoiChoice: function (arg) {
		this.roi_choice = arg;
	},

	getIsHideOther: function () {
		return this.isHideOther;
	},

	setIsHideOther: function (arg) {
		this.isHideOther = arg;
	},

	getPdRoiBoard: function () {
		var pdRoiBoard = {};

		pdRoiBoard.pdGreenScale = this.smooth_scale[0];
		pdRoiBoard.pdYellowScale = this.smooth_scale[1];
		pdRoiBoard.pdRedScale = this.smooth_scale[2];
		pdRoiBoard.pdGreenPoint = [];
		for (var j = 0; j < 10; j++) {
			pdRoiBoard.pdGreenPoint.push(
				this.points[0][j].x < 0 ? 0 : this.points[0][j].x
			);
			pdRoiBoard.pdGreenPoint.push(
				this.points[0][j].y < 0 ? 0 : this.points[0][j].y
			);
		}

		pdRoiBoard.pdYellowPoint = [];
		for (var j = 0; j < 10; j++) {
			pdRoiBoard.pdYellowPoint.push(
				this.points[1][j].x < 0 ? 0 : this.points[1][j].x
			);
			pdRoiBoard.pdYellowPoint.push(
				this.points[1][j].y < 0 ? 0 : this.points[1][j].y
			);
		}

		pdRoiBoard.pdRedPoint = [];
		for (var j = 0; j < 10; j++) {
			pdRoiBoard.pdRedPoint.push(
				this.points[2][j].x < 0 ? 0 : this.points[2][j].x
			);
			pdRoiBoard.pdRedPoint.push(
				this.points[2][j].y < 0 ? 0 : this.points[2][j].y
			);
		}
		return pdRoiBoard;
	},
	setPdRoiBoard: function (arg) {
		pdRoiBoard = arg;
		this.smooth_scale[0] = pdRoiBoard.pdGreenScale;
		this.smooth_scale[1] = pdRoiBoard.pdYellowScale;
		this.smooth_scale[2] = pdRoiBoard.pdRedScale;

		for (var j = 0; j < 10; j++) {
			this.points[0][j].x = pdRoiBoard.pdGreenPoint[2 * j];
			this.points[0][j].y = pdRoiBoard.pdGreenPoint[2 * j + 1];
		}

		for (var j = 0; j < 10; j++) {
			this.points[1][j].x = pdRoiBoard.pdYellowPoint[2 * j];
			this.points[1][j].y = pdRoiBoard.pdYellowPoint[2 * j + 1];
		}

		for (var j = 0; j < 10; j++) {
			this.points[2][j].x = pdRoiBoard.pdRedPoint[2 * j];
			this.points[2][j].y = pdRoiBoard.pdRedPoint[2 * j + 1];
		}
	},
	setlang: function (lang) {
		this.lang = lang;
	},
	show: function () {
		this.clearColor();
		this.downEnable = true;
		this.refreshCal();
		console.log("calibration drawboard show");
	},
	quit: function () {
		var ctx = this.draw_canvas.getContext("2d");
		ctx.beginPath();
		ctx.clearRect(0, 0, this.draw_canvas.width, this.draw_canvas.height);
		ctx.stroke();
		for (var i = 0; i < this.points_num; i++) {
			this.points_is_dragging[i] = false;
		}
		console.log("calibration drawboard quit");
	},
	reviseMouseX: function (arg) {
		this.widthpading = arg;
		console.log("calibration drawboard reviseMouseX");
	},
	reset: function () {
		this.createCalibrationDrawBoard();
	},
};
