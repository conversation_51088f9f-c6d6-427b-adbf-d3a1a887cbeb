
function InitCustomControl(){
	$(".menu li a").each(function() {
		$(this).click(function(){	
			$(this).parents(".menu").find("a").each(function() {
				$(this).removeClass("menu_checked");
			});
			$(this).addClass("menu_checked");
		});
	});

	//select实现
	$(".custom-select").each(function() {
		var classes = $(this).attr("class");
		var id = $(this).attr("id");
		var placeholder = $(this).attr("value");
		$(this).find("option").each(function() {
	        if($(this).attr("value") === placeholder){
				placeholder = $(this).text();
	        }
	    });
	    var template = '<div class="' + classes + '">';
	    template += '<span tabindex="0" hidefocus="true" id="'+id+'_trigger" class="custom-select-trigger">' + placeholder + '</span>';
	    template += '<div class="custom-options">';
	    $(this).find("option").each(function() {
	        template += '<span class="custom-option ' + $(this).attr("class")+' "style="'+$(this).attr("style")+'" data-value="' + $(this).attr("value") +'" data-desc="' + $(this).attr("data-desc") + '">' + $(this).html() + '</span>';
	    });
	    template += '</div></div>';

	    $(this).wrap('<div class="custom-select-wrapper"></div>');
	    $(this).hide();
	    $(this).after(template);
		
		$(this).change(function(){
			var id = $(this).attr("id");
			var v = $(this).val();
			$("#"+id+" option").each(function(){
		        if($(this).val()==v){
					$("#"+id+"_trigger").text($(this).text());
		        }
		    });	
		});
	});
	$(".custom-option:first-of-type").hover(function() {
	    $(this).parents(".custom-options").addClass("option-hover");
	}, function() {
	    $(this).parents(".custom-options").removeClass("option-hover");
	});
	$(".custom-select-trigger").on("click", function() {
	    $(this).parents(".custom-select").toggleClass("opened");
	});
	$(".custom-option").on("click", function() {
	    $(this).parents(".custom-select-wrapper").find("select").val($(this).data("value"));
	    $(this).parents(".custom-options").find(".custom-option").removeClass("selection");
	    $(this).addClass("selection");
	    $(this).parents(".custom-select").removeClass("opened");
	    $(this).parents(".custom-select").find(".custom-select-trigger").text($(this).text());
		$(this).parents(".custom-select-wrapper").find("select").trigger("change");
	});


	var custom_select = {
	  quit: function(){
		$(this).parents(".custom-select").removeClass("opened");
	  }
	};

	$(".custom-select-trigger").blur(function(){
		setTimeout($.proxy(custom_select.quit, $(this)), 200);//这里不延迟执行的话，会触发不了.custom-option的click
  	});
	$('.rangeinput_input').on('input propertychange',function(){
		var min = $(this).attr("min")*1;
		var max = $(this).attr("max")*1;
		var real_value = (this.value - min)*100/(max-min);
		$(this).css( 'background-size', real_value + '% 100%' );
		$(this).parents(".rangeinput").find(".rangeinput_value").html(this.value);
	});
	$('.rangeinput_input').each(function(){
		var min = $(this).attr("min")*1;
		var max = $(this).attr("max")*1;
		var real_value = (this.value - min)*100/(max-min);
		$(this).css( 'background-size', real_value + '% 100%' );
		$(this).parents(".rangeinput").find(".rangeinput_value").html(this.value);
	});	
	
}

InitCustomControl();


//显示遮罩
function showCoverDiv(){
	var mybg = document.getElementById("mybg");
	if(mybg){
		mybg.style.display = "block";
	}else{
	    var procbg = document.createElement("div"); //首先创建一个div
		procbg.setAttribute("id","mybg"); //定义该div的id
		procbg.style.background = "#000000";
		procbg.style.width = "100%";
		procbg.style.height = "100%";
		procbg.style.position = "fixed";
		procbg.style.top = "0";
		procbg.style.left = "0";
		procbg.style.zIndex = "500";
		procbg.style.opacity = "0.6";
		procbg.style.filter = "Alpha(opacity=70)";
		document.body.appendChild(procbg);
	}
}

//取消遮罩
function hideCoverDiv() {
    var mybg = document.getElementById("mybg");
    if(mybg)
        mybg.style.display = "none";
}

function initDialog(prefix,title){
	if(!document.getElementById("dialogLink"))
	{
		var link = document.createElement("link");
		link.setAttribute("id","dialogLink");
		link.rel="stylesheet";
		link.type="text/css";
		link.href="css/dialog.css?ver=1";
		document.head.appendChild(link);
	}

	var dialog_div = document.getElementById(prefix+"_dialog");
	if(dialog_div){
		document.body.removeChild(dialog_div);
	}

	dialog_div = document.createElement("div");
	dialog_div.setAttribute("id",prefix+"_dialog"); //定义该div的id
	dialog_div.setAttribute("class","dialog"); 
	var dialog_p = document.createElement("p");
	dialog_p.setAttribute("id",prefix+"_title");
    dialog_p.setAttribute("class","dialog_title");
	dialog_p.innerHTML=title;
	dialog_div.appendChild(dialog_p);
	
	return dialog_div;
}

function createDialogButtonBox(prefix,rightbtn_text,rightbtn_fun,leftbtn_text,leftbtn_fun){
	var btns_div = document.createElement("div");

	var dialog_rightbtn = document.createElement("a");
	dialog_rightbtn.setAttribute("id",prefix+"_rightbtn");
	dialog_rightbtn.setAttribute("class","dialog-btn");
	dialog_rightbtn.onfocus="this.blur();";
	dialog_rightbtn.innerHTML=rightbtn_text;
	dialog_rightbtn.addEventListener("click",rightbtn_fun);
	btns_div.appendChild(dialog_rightbtn);
	

	if(leftbtn_text){
		var dialog_leftbtn = document.createElement("a");
		dialog_leftbtn.setAttribute("id",prefix+"_leftbtn");
		dialog_leftbtn.setAttribute("class","dialog-cancelbtn");
		dialog_leftbtn.onfocus="this.blur();";
		dialog_leftbtn.innerHTML=leftbtn_text;
		dialog_leftbtn.addEventListener("click",leftbtn_fun);
		btns_div.appendChild(dialog_leftbtn);
	}

	return btns_div;
}

function removeDialog(prefix){
	var dialog_div = document.getElementById(prefix+"_dialog");
	if(dialog_div){
		document.body.removeChild(dialog_div);
	}
}

function createNormalDialog(title,btn_texts,btn_clicks){
	showCoverDiv();
	var prefix="normalDialog";
	var dialog_div = initDialog(prefix,title);

	var btns_div = createDialogButtonBox(
		prefix,
		btn_texts[0],
		function(e){
			btn_clicks[0]();
			this.removeEventListener(e.type,arguments.callee,false);//接触本次函数的绑定，不然没用一次弹窗，这按键都会多个处理函数
		},
		btn_texts[1],
		function(e){
			btn_clicks[1]();
			this.removeEventListener(e.type,arguments.callee,false);//接触本次函数的绑定，不然没用一次弹窗，这按键都会多个处理函数
		}
	);

	dialog_div.appendChild(btns_div);
	document.body.appendChild(dialog_div);
}

function removeNormalDialog(hidecover){
	removeDialog("normalDialog");
	if(hidecover)
        hideCoverDiv();
}


function createInputDialog(title,btn_texts,btn_clicks){
	showCoverDiv();
	var prefix="inputDialog";
	var dialog_div = initDialog(prefix,title);

	var dialog_input = document.createElement("input");
	dialog_input.setAttribute("id",prefix+"_input");
    dialog_input.setAttribute("maxlength",16);
	dialog_input.style.marginLeft="10px";
	dialog_input.style.marginBottom="10px";
	dialog_div.appendChild(dialog_input);

	var btns_div = createDialogButtonBox(
		prefix,
		btn_texts[0],
		function(e){
			if(dialog_input.value)
				btn_clicks[0](dialog_input.value);
			else
				btn_clicks[0]("valueIsEmpty");
			this.removeEventListener(e.type,arguments.callee,false);//接触本次函数的绑定，不然没用一次弹窗，这按键都会多个处理函数
		},
		btn_texts[1],
		function(e){
			btn_clicks[1]();
			this.removeEventListener(e.type,arguments.callee,false);//接触本次函数的绑定，不然没用一次弹窗，这按键都会多个处理函数
		}
	);

	dialog_div.appendChild(btns_div);
	document.body.appendChild(dialog_div);
}

function removeInputDialog(hidecover){
	removeDialog("inputDialog");
	if(hidecover)
        hideCoverDiv();
}

function switchCssStyle(customer){
	if(customer == "200026")
	{
		document.body.style.setProperty("--theme_color", "var(--axion_color)");
		document.body.style.setProperty("--theme_light_color", "var(--axion_light_color)");
		document.body.style.setProperty("--theme_dark_color", "var(--axion_dark_color)");
		document.body.style.setProperty("--checkbox_off", "var(--axion_checkbox_off)");
		document.body.style.setProperty("--checkbox_on", "var(--axion_checkbox_on)");
		document.body.style.setProperty("--switch_on", "var(--axion_switch_on)");
	}
	else if(customer == "201894")
	{
		document.body.style.setProperty("--theme_color", "var(--WE_color)");
		document.body.style.setProperty("--theme_light_color", "var(--WE_light_color)");
		document.body.style.setProperty("--theme_dark_color", "var(--WE_dark_color)");
		document.body.style.setProperty("--checkbox_off", "var(--WE_checkbox_off)");
		document.body.style.setProperty("--checkbox_on", "var(--WE_checkbox_on)");
		document.body.style.setProperty("--switch_on", "var(--WE_switch_on)");
	}
	else if(customer == "201851")
	{
		document.body.style.setProperty("--theme_color", "var(--VUE_color)");
		document.body.style.setProperty("--theme_light_color", "var(--VUE_light_color)");
		document.body.style.setProperty("--theme_dark_color", "var(--VUE_dark_color)");
		document.body.style.setProperty("--checkbox_off", "var(--VUE_checkbox_off)");
		document.body.style.setProperty("--checkbox_on", "var(--VUE_checkbox_on)");		
		document.body.style.setProperty("--switch_on", "var(--VUE_switch_on)");			
	}
	else if(customer == "201623")
	{
		document.body.style.setProperty("--theme_color", "var(--proxicam_color)");
		document.body.style.setProperty("--theme_light_color", "var(--proxicam_light_color)");
		document.body.style.setProperty("--theme_dark_color", "var(--proxicam_dark_color)");
		document.body.style.setProperty("--checkbox_off", "var(--proxicam_checkbox_off)");
		document.body.style.setProperty("--checkbox_on", "var(--proxicam_checkbox_on)");		
		document.body.style.setProperty("--switch_on", "var(--proxicam_switch_on)");			
	}
	else if(customer == "202461")
	{
		document.body.style.setProperty("--theme_color", "var(--T2S_color)");
		document.body.style.setProperty("--theme_light_color", "var(--T2S_light_color)");
		document.body.style.setProperty("--theme_dark_color", "var(--T2S_dark_color)");
		document.body.style.setProperty("--checkbox_off", "var(--T2S_checkbox_off)");
		document.body.style.setProperty("--checkbox_on", "var(--T2S_checkbox_on)");		
		document.body.style.setProperty("--switch_on", "var(--T2S_switch_on)");			
	}
	else if(customer == "202661")
	{
		document.body.style.setProperty("--theme_color", "var(--AME_color)");
		document.body.style.setProperty("--theme_light_color", "var(--AME_light_color)");
		document.body.style.setProperty("--theme_dark_color", "var(--AME_dark_color)");
		document.body.style.setProperty("--checkbox_off", "var(--AME_checkbox_off)");
		document.body.style.setProperty("--checkbox_on", "var(--AME_checkbox_on)");		
		document.body.style.setProperty("--switch_on", "var(--AME_switch_on)");			
	}
}

function handleInput(event) {
	$(event.target).val($(event.target).val().replace(/[^\u00C0-\u017F\d\w\~\@\#\$\^\~\!\@\#\$\%\^\&\*\(\)\_\-\+\=\{\[\}\]\:\;\"\'\|\\\<\,\>\.\?\/]/g,''));
}


