/**
 * Created by <PERSON> on 2017/9/26.
 */
var hostname = window.location.hostname;
var hostport = window.location.port;

function SrsRtcPlayerAsync() {
	var self = {};

	self.pc = new RTCPeerConnection();
	self.pc.onconnectionstatechange = function (event) {
		console.log("connection state change: ", self.pc.connectionState);
	};

	self.pc.onicecandidate = async (ev) => {
		console.log("=======>" + JSON.stringify(ev.candidate));
	};

	datachannel = self.pc.createDataChannel("chat");

	datachannel.onopen = function (event) {
		console.log("datachannel onopen: ", event.data);
	};
	datachannel.onmessage = function (event) {
		console.log("receive message: ", event.data);
	};
	datachannel.onerror = function (event) {
		console.log("datachannel error: ", event.data);
	};
	datachannel.onclose = function (event) {
		console.log("datachannel close: ");
	};

	self.play = async function (url) {
		var conf = self.__internal.prepareUrl(url);
		self.pc.addTransceiver("audio", { direction: "sendrecv" });
		self.pc.addTransceiver("video", { direction: "sendrecv" });

		var offer = await self.pc.createOffer();
		await self.pc.setLocalDescription(offer);
		var session = await new Promise(function (resolve, reject) {
			// @see https://github.com/rtcdn/rtcdn-draft
			var data = {
				api: conf.apiUrl,
				tid: conf.tid,
				streamurl: conf.streamUrl,
				clientip: null,
				sdp: offer.sdp,
			};
			//   console.log('Generated offer: ', data);
			//text/plain application/json
			$.ajax({
				type: "POST",
				url: conf.apiUrl,
				data: offer.sdp + "}",
				contentType: "text/plain",
				dataType: "json",
				crossDomain: true,
			})
				.done(function (data) {
					if (data.code) {
						reject(data);
						return;
					}
					console.log("Got sdp: ", data.sdp);
					resolve(data);
				})
				.fail(function (reason) {
					reject(reason);
				});
		});
		await self.pc.setRemoteDescription(
			new RTCSessionDescription({ type: "answer", sdp: session.sdp })
		);
		session.simulator =
			conf.schema +
			"//" +
			conf.urlObject.server +
			":" +
			conf.port +
			"/rtc/v1/nack/";

		return session;
	};

	// Close the player.
	self.close = function () {
		if (datachannel) {
			datachannel.close();
			datachannel = null;
		}
		self.pc && self.pc.close();
		self.pc = null;
	};

	// The callback when got remote track.
	// Note that the onaddstream is deprecated, @see https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/onaddstream
	self.ontrack = function (event) {
		self.stream.addTrack(event.track);
	};

	// Internal APIs.
	self.__internal = {
		defaultPath: "/rtc/v1/play/",
		prepareUrl: function (webrtcUrl) {
			var urlObject = self.__internal.parse(webrtcUrl);

			// 兼容定焦设备https环境下的webrtc播放
			if (hostport === "8443") var schema = "https:";
			else var schema = "http:";

			// var schema = urlObject.user_query.schema;
			// schema = schema ? schema + ':' : window.location.protocol;

			var port = urlObject.port || 1985;
			if (schema === "https:") {
				port = urlObject.port || 443;
			}

			// @see https://github.com/rtcdn/rtcdn-draft
			var api = urlObject.user_query.play || self.__internal.defaultPath;
			if (api.lastIndexOf("/") !== api.length - 1) {
				api += "/";
			}

			apiUrl = schema + "//" + urlObject.server + ":" + port + api;

			for (var key in urlObject.user_query) {
				if (key !== "api" && key !== "play") {
					apiUrl += "&" + key + "=" + urlObject.user_query[key];
				}
			}
			// Replace /rtc/v1/play/&k=v to /rtc/v1/play/?k=v
			var apiUrl = apiUrl.replace(api + "&", api + "?");

			var streamUrl = urlObject.url;

			return {
				apiUrl: apiUrl,
				streamUrl: streamUrl,
				schema: schema,
				urlObject: urlObject,
				port: port,
				tid: Number(parseInt(new Date().getTime() * Math.random() * 100))
					.toString(16)
					.substr(0, 7),
			};
		},
		parse: function (url) {
			// @see: http://stackoverflow.com/questions/10469575/how-to-use-location-object-to-parse-url-without-redirecting-the-page-in-javascri
			var a = document.createElement("a");

			if (hostport === "8443")
				a.href = url
					.replace("rtmp://", "https://")
					.replace("webrtc://", "https://")
					.replace("rtc://", "https://");
			else
				a.href = url
					.replace("rtmp://", "http://")
					.replace("webrtc://", "http://")
					.replace("rtc://", "http://");

			var vhost = a.hostname;
			var app = a.pathname.substr(1, a.pathname.lastIndexOf("/") - 1);
			var stream = a.pathname.substr(a.pathname.lastIndexOf("/") + 1);

			// parse the vhost in the params of app, that srs supports.
			app = app.replace("...vhost...", "?vhost=");
			if (app.indexOf("?") >= 0) {
				var params = app.substr(app.indexOf("?"));
				app = app.substr(0, app.indexOf("?"));

				if (params.indexOf("vhost=") > 0) {
					vhost = params.substr(params.indexOf("vhost=") + "vhost=".length);
					if (vhost.indexOf("&") > 0) {
						vhost = vhost.substr(0, vhost.indexOf("&"));
					}
				}
			}

			// when vhost equals to server, and server is ip,
			// the vhost is __defaultVhost__
			if (a.hostname === vhost) {
				var re = /^(\d+)\.(\d+)\.(\d+)\.(\d+)$/;
				if (re.test(a.hostname)) {
					vhost = "__defaultVhost__";
				}
			}

			// parse the schema
			var schema = "rtmp";
			if (url.indexOf("://") > 0) {
				schema = url.substr(0, url.indexOf("://"));
			}

			var port = a.port;
			if (!port) {
				if (schema === "http") {
					port = 80;
				} else if (schema === "https") {
					port = 443;
				} else if (schema === "rtmp") {
					port = 1935;
				}
			}

			var ret = {
				url: url,
				schema: schema,
				server: a.hostname, //a.hostname
				port: port,
				vhost: vhost,
				app: app,
				stream: stream,
				user_query: {
					schema,
				},
			};
			self.__internal.fill_query(a.search, ret);

			// For webrtc API, we use 443 if page is https, or schema specified it.
			if (!ret.port) {
				if (schema === "webrtc" || schema === "rtc") {
					if (ret.user_query.schema === "https") {
						ret.port = 443;
					} else if (window.location.href.indexOf("https://") === 0) {
						ret.port = 443;
					} else {
						// For WebRTC, SRS use 1985 as default API port.
						ret.port = 1985;
					}
				}
			}

			return ret;
		},
		fill_query: function (query_string, obj) {
			// pure user query object.
			obj.user_query = {};

			if (query_string.length === 0) {
				return;
			}

			// split again for angularjs.
			if (query_string.indexOf("?") >= 0) {
				query_string = query_string.split("?")[1];
			}

			var queries = query_string.split("&");
			for (var i = 0; i < queries.length; i++) {
				var elem = queries[i];

				var query = elem.split("=");
				obj[query[0]] = query[1];
				obj.user_query[query[0]] = query[1];
			}

			// alias domain for vhost.
			if (obj.domain) {
				obj.vhost = obj.domain;
			}
		},
	};

	self.pc = new RTCPeerConnection();
	// Create a stream to add track to the stream, @see https://webrtc.org/getting-started/remote-streams
	self.stream = new MediaStream();

	// https://developer.mozilla.org/en-US/docs/Web/API/RTCPeerConnection/ontrack
	self.pc.ontrack = function (event) {
		if (self.ontrack) {
			self.ontrack(event);
		}
	};

	return self;
}

var WebappController = function (model, view) {
	this.model = model;
	this.view = view;
	this.previewEnable = false;
	this.previewFps = 12;
	this.lastRefreshTime = null;
	this.lastTime = 0;
	this.submitting = false;
	this.WatchStatusLock = false;
	this.webRTC = null;
	this.init();
};

WebappController.prototype = {
	init: function () {
		window.requestAnimationFrame =
			window.requestAnimationFrame ||
			window.mozRequestAnimationFrame ||
			window.webkitRequestAnimationFrame ||
			window.msRequestAnimationFrame ||
			function (callback, element) {
				var currTime = new Date().getTime();
				var timeToCall = Math.max(0, 16 - (currTime - this.lastTime));
				var id = window.setTimeout(function () {
					callback(currTime + timeToCall);
				}, timeToCall);
				this.lastTime = currTime + timeToCall;
				return id;
			}.bind(this);
		this.setupHandlers().enable();
	},

	setupHandlers: function () {
		this.logoutHandler = this.logout.bind(this);
		this.CancelCalibrationHandler = this.CancelCalibration.bind(this);
		this.getImageParamHandler = this.getImageParam.bind(this);
		this.setImageParamHandler = this.setImageParam.bind(this);
		this.submitConfigsHandler = this.submitConfigs.bind(this);
		this.submitSliAlgParamHandler = this.submitSliAlgParam.bind(this);
		this.submitSensitivityHandler = this.submitSensitivity.bind(this);
		this.submitShelterEventHandler = this.submitShelterEvent.bind(this);
		this.changeStreamTypeEventHandler = this.changeStreamTypeEvent.bind(this);
		this.submitImageProcessHandler = this.submitImageProces.bind(this);
		this.cancelConfigsHandler = this.cancelConfigs.bind(this);
		this.saveConfigsHandler = this.saveConfigs.bind(this);
		this.restoreFactoryHandler = this.restoreFactory.bind(this);
		this.calibrationHandler = this.calibration.bind(this);
		this.guiMaskHandler = this.guiMask.bind(this);
		this.apcSetConfigHandler = this.apcSetConfig.bind(this);
		this.changeCaliModeHandler = this.changeCaliMode.bind(this);
		this.selLangHandler = this.selLang.bind(this);
		this.submitCalibrationBoardHandler = this.submitCalibrationBoard.bind(this);
		this.setIconPosHandler = this.setIcon.bind(this);
		this.registerHandler = this.register.bind(this);
		this.ptzHandler = this.ptz.bind(this);
		this.afCalHandler = this.afCalibrate.bind(this);
		this.changePasswordHandler = this.changePassword.bind(this);
		this.changeTimeHandler = this.changeTime.bind(this);
		this.formateSdHandler = this.formateSd.bind(this);
		this.refreshConfigsHandler = this.refreshConfigs.bind(this);
		this.getpdDataHandler = this.getpdData.bind(this);
		this.getAPCDataHandler = this.getAPCData.bind(this);
		this.switchCamHandler = this.switchCam.bind(this);
		this.refreshDevInfoHandler = this.refreshDevInfo.bind(this);
		this.scanSsidHandler = this.scanSsid.bind(this);
		this.addScanSsidHandler = this.addScanSsid.bind(this);
		this.delScanSsidHandler = this.delScanSsid.bind(this);
		this.pingNetworkHandler = this.pingNetwork.bind(this);
		this.operateBlkDevHandler = this.operateBlkDev.bind(this);
		this.cvbsOutputHandler = this.cvbsOutput.bind(this);
		this.rebootHandler = this.reboot.bind(this);
		this.queryHandler = this.query.bind(this);
		this.queryLogHandler = this.queryLog.bind(this);
		this.queryImgHandler = this.queryImg.bind(this);
		this.showStatPageHandler = this.showStatPage.bind(this);
		this.showLogPageHandler = this.showLogPage.bind(this);
		this.showRecordPageHandler = this.showRecordPage.bind(this);
		this.showAlarmListPageHandler = this.showAlarmListPage.bind(this);
		this.getAlarmPagesHandler = this.getAlarmPages.bind(this);
		this.getRecordPagesHandler = this.getRecordPages.bind(this);
		this.requestPlaybackHandler = this.requestPlayback.bind(this);
		this.deleteEventHandler = this.deleteEvent.bind(this);

		return this;
	},

	enable: function () {
		this.view.logoutEvent.attach(this.logoutHandler);
		this.view.CancelCalibration.attach(this.CancelCalibrationHandler);
		this.view.getImageParamEvent.attach(this.getImageParamHandler);
		this.view.setImageParamEvent.attach(this.setImageParamHandler);
		this.view.submitConfigsEvent.attach(this.submitConfigsHandler);
		this.view.submitSliAlgParamEvent.attach(this.submitSliAlgParamHandler);
		this.view.submitSensitivityEvent.attach(this.submitSensitivityHandler);
		this.view.submitShelterEvent.attach(this.submitShelterEventHandler);
		this.view.changeStreamTypeEvent.attach(this.changeStreamTypeEventHandler);
		this.view.submitImageProcessEvent.attach(this.submitImageProcessHandler);
		this.view.cancelConfigsEvent.attach(this.cancelConfigsHandler);
		this.view.saveConfigsEvent.attach(this.saveConfigsHandler);
		this.view.restoreFactoryEvent.attach(this.restoreFactoryHandler);
		this.view.calibrationEvent.attach(this.calibrationHandler);
		this.view.guiMask.attach(this.guiMaskHandler);
		this.view.apcSetConfigEvent.attach(this.apcSetConfigHandler);
		this.view.changeCaliModeEvent.attach(this.changeCaliModeHandler);
		this.view.selLangEvent.attach(this.selLangHandler);
		this.view.submitCalibrationBoardEvnet.attach(
			this.submitCalibrationBoardHandler
		);
		this.view.setIconPosEvent.attach(this.setIconPosHandler);
		this.view.registerEvent.attach(this.registerHandler);
		this.view.ptzEvent.attach(this.ptzHandler);
		this.view.afCalEvent.attach(this.afCalHandler);
		this.view.changePasswordEvent.attach(this.changePasswordHandler);
		this.view.changeTimeEvent.attach(this.changeTimeHandler);
		this.view.formatSdEvent.attach(this.formateSdHandler);
		this.view.refreshConfigsEvent.attach(this.refreshConfigsHandler);
		this.view.getpdDataEvent.attach(this.getpdDataHandler);
		this.view.getAPCDataEvent.attach(this.getAPCDataHandler);
		this.view.switchCamEvent.attach(this.switchCamHandler);
		this.view.refreshDevInfoEvent.attach(this.refreshDevInfoHandler);
		this.view.scanSsidEvent.attach(this.scanSsidHandler);
		this.view.addScanSsidEvent.attach(this.addScanSsidHandler);
		this.view.delScanSsidEvent.attach(this.delScanSsidHandler);
		this.view.pingNetworkEvent.attach(this.pingNetworkHandler);
		this.view.operateBlkDevEvent.attach(this.operateBlkDevHandler);
		this.view.cvbsOutputEvent.attach(this.cvbsOutputHandler);
		this.view.rebootEvent.attach(this.rebootHandler);
		this.view.queryEvent.attach(this.queryHandler);
		this.view.queryLogEvent.attach(this.queryLogHandler);
		this.view.queryImgEvent.attach(this.queryImgHandler);
		this.view.showStatPage.attach(this.showStatPageHandler);
		this.view.showLogPage.attach(this.showLogPageHandler);
		this.view.showRecordPage.attach(this.showRecordPageHandler);
		this.view.showAlarmListPage.attach(this.showAlarmListPageHandler);
		this.view.getAlarmPages.attach(this.getAlarmPagesHandler);
		this.view.getRecordPages.attach(this.getRecordPagesHandler);
		this.view.refreshRecordEvent.attach(this.showRecordPageHandler);
		this.view.requestPlaybackEvent.attach(this.requestPlaybackHandler);
		this.view.deleteEvent.attach(this.deleteEventHandler);

		return this;
	},

	logout: function () {
		console.log("logout:" + this.view.hardware);
		if (this.view.hardware == "DMS31V2" || this.view.hardware == "DMS885N") {
			$.ajax({
				type: "POST",
				url: "/calibration",
				data: '{"accessApi": {"password":""},"mode": 0}',
				success: function (data, status) {
					this.view.jumpToLoginPage();
				}.bind(this),
				error: function (XMLHttpRequest) {},
				dataType: "json",
			});
		}
		$.ajax({
			type: "POST",
			url: "/logout",
			data: "",
			success: function (data, status) {
				this.view.jumpToLoginPage();
			}.bind(this),
			error: function (XMLHttpRequest) {},
			dataType: "json",
		});
	},

	CancelCalibration: function () {
		console.log("cancel calibration");
		if (this.view.hardware == "DMS31V2" || this.view.hardware == "DMS885N") {
			$.ajax({
				type: "POST",
				url: "/calibration",
				data: '{"accessApi": {"password":""},"mode": 0}',
				success: function (data, status) {
					this.view.jumpToLoginPage();
				}.bind(this),
				error: function (XMLHttpRequest) {},
				dataType: "json",
			});
		}
		this.view.jumpToLoginPage();
	},

	getImageParam: function () {
		console.log("getImageParam");
		$.ajax({
			type: "GET",
			url: "/image_param",
			async: false,
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.view.refreshImageParam(data);
			}.bind(this),
			error: function (XMLHttpRequest) {
				//this.view.showErrorInfo("get-config-fail", JSON.parse(XMLHttpRequest.responseText));
			}.bind(this),
			dataType: "json",
		});
		return this;
	},

	setImageParam: function (sender, args) {
		console.log("setImageParam:" + JSON.stringify(args));
		$.ajax({
			type: "POST",
			url: "/image_param",
			data: JSON.stringify(args),
			success: function (data, status) {
				//console.log(JSON.stringify(data));
				//this.view.refreshImageParam(data);
			}.bind(this),
			error: function (XMLHttpRequest) {
				console.log(JSON.parse(XMLHttpRequest.responseText));
				//this.view.showErrorInfo("get-config-fail", JSON.parse(XMLHttpRequest.responseText));
			}.bind(this),
			dataType: "json",
		});
		return this;
	},

	wsInit: function () {
		var ws = null;
		var fps = 0;
		var wsServer = this.view.imgwsUrl;
		var lockReconnect = false;
		createWebSocket(wsServer);

		window.onbeforeunload = function () {
			ws.close();
		};
		window.onunload = function (e) {
			ws.close();
		};
		document.addEventListener("visibilitychange", function () {
			//ws.close();
		});

		var intervalId = setInterval(function () {
			//console.log(fps);
			//$("#displayFPS").text(fps);
			fps = 0;
		}, 1000);

		var heartCheck = {
			timeout: 5000, //  心跳检测时长
			timeoutObj: null, // 定时变量
			reset: function () {
				// 重置心跳
				clearTimeout(this.timeoutObj);
				return this;
			},
			start: function () {
				// 开启心跳
				var self = this;
				this.timeoutObj = setTimeout(function () {
					// 心跳时间内收不到消息，主动触发连接关闭，开始重连
					ws.close(); //如果onclose会执行reconnect，执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
				}, this.timeout);
			},
		};

		function reconnect(url) {
			if (lockReconnect) return;
			console.log("reconnecting...");
			lockReconnect = true;
			setTimeout(function () {
				createWebSocket(url);
				lockReconnect = false;
			}, 2000); //没连接上会一直重连，设置延迟避免请求过多,2秒重连一次
		}

		function createWebSocket(url) {
			try {
				if ("WebSocket" in window) {
					ws = new WebSocket(url);
				} else if ("MozWebSocket" in window) {
					ws = new MozWebSocket(url);
				} else {
					_alert("当前浏览器不支持websocket协议,建议使用现代浏览器", 3000);
				}
				initEventHandle();
			} catch (e) {
				reconnect(url);
			}
		}

		// 初始化事件函数
		function initEventHandle() {
			ws.onclose = function (evt) {
				console.log(
					"connection close..." +
						evt.code +
						" " +
						evt.reason +
						" " +
						evt.wasClean
				);
				reconnect(wsServer);
			};
			ws.onerror = function (evt) {
				reconnect(wsServer);
			};
			ws.onopen = function (evt) {
				console.log(evt);
				ws.send("get_view");
				heartCheck.reset().start(); //心跳检测重置
			};
			ws.onmessage = function (evt) {
				//如果获取到消息，心跳检测重置
				heartCheck.reset().start(); //拿到任何消息都说明当前连接是正常的
				var reader = new FileReader();
				reader.onload = function (evt) {
					var url = evt.target.result;
					document.getElementById("img-solo").src = url;
				};
				reader.readAsDataURL(evt.data);
				fps += 1;
			};
		}
	},

	refreshPreview: function (timestamp) {
		this.lastRefreshTime = this.lastRefreshTime || timestamp;
		if (timestamp - this.lastRefreshTime > 1000 / this.previewFps) {
			this.view.refreshPreview();
			this.lastRefreshTime = timestamp;
		}
		this.previewEnable && requestAnimationFrame(this.refreshPreview.bind(this));
	},

	startPreview: function (type) {
		console.log("startPreview");
		this.previewEnable = true;
		//this.wsInit();
		this.view.showStreamType(type);

		if (type == 0) {
			$("#img-solo").show();
			$("#rtc_media_player").hide();
			if (this.webRTC) {
				this.webRTC.close();
				this.webRTC = null;
			}
			requestAnimationFrame(this.refreshPreview.bind(this));
		} else {
			if (hostport === "8443")
				var url =
					"webrtc://" + window.location.hostname + ":1989/live/livestream";
			else
				var url =
					"webrtc://" + window.location.hostname + ":1988/live/livestream";
			$("#rtc_media_player").show();
			$("#img-solo").hide();
			$("#rtc_media_player").prop("muted", true);
			if (this.webRTC) {
				this.webRTC.close();
				this.webRTC = null;
			}
			this.webRTC = new SrsRtcPlayerAsync();
			console.log("sdk:", this.webRTC);
			console.log("sdk.stream:", this.webRTC.stream);

			$("#rtc_media_player").prop("srcObject", this.webRTC.stream);

			this.webRTC
				.play(url)
				.then(() => {
					//监听视频流是否成功播放
					console.log("success");
				})
				.catch(() => {
					this.webRTC.close();
					this.webRTC = null;
					//监听视频流是否成功播放
					console.log("fail");
				});
		}

		return this;
	},

	stopPreview: function () {
		console.log("stopPreview");
		this.previewEnable = false;
		return this;
	},

	queryDeviceStatus: function () {
		this.model.setSatusJson({});
		$.ajax({
			type: "POST",
			url: "query_status",
			data: '{"module":"base"}',
			success: function (data, status) {
				console.log("query_status_base:" + JSON.stringify(data));
				this.model.appendBaseStatus(data);
				this.model.isStatusJsonComplete() &&
					this.view.refreshStatInfo(this.model.getStatusJson());
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"query-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
			}.bind(this),
			dataType: "json",
		});
		$.ajax({
			type: "POST",
			url: "query_status",
			data: '{"module":"rtsp"}',
			success: function (data, status) {
				//console.log("query_status_rtsp:"+JSON.stringify(data));
				this.model.appendRtspStatus(data);
				this.model.isStatusJsonComplete() &&
					this.view.refreshStatInfo(this.model.getStatusJson());
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"query-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
			}.bind(this),
			dataType: "json",
		});

		if (this.model.getIsSupportRecord()) {
			$.ajax({
				type: "POST",
				url: "query_status",
				data: '{"module":"storage"}',
				success: function (data, status) {
					console.log("query_status_storage:" + JSON.stringify(data));
					this.model.appendStorageStatus(data);
					this.model.isStatusJsonComplete() &&
						this.model.isStatusJsonHasStorage() &&
						this.view.refreshStatInfo(this.model.getStatusJson());
				}.bind(this),
				error: function (XMLHttpRequest) {
					this.view.showErrorInfo(
						"query-fail",
						JSON.parse(XMLHttpRequest.responseText)
					);
				}.bind(this),
				dataType: "json",
			});
		}
		if (
			this.model.getMachineType() == "DMS31V2" ||
			this.model.getMachineType() == "ADA32C4" ||
			this.model.getMachineType() == "DMS885N"
		) {
			$.ajax({
				type: "POST",
				url: "query_status",
				data: '{"module":"cellular"}',
				success: function (data, status) {
					//console.log("query_status_storage:"+JSON.stringify(data));
					this.model.appendCellStatus(data);
					this.model.isStatusJsonComplete() &&
						this.view.refreshStatInfo(this.model.getStatusJson());
				}.bind(this),
				error: function (XMLHttpRequest) {
					//this.view.showErrorInfo("query-fail", JSON.parse(XMLHttpRequest.responseText));
				}.bind(this),
				dataType: "json",
			});
			$.ajax({
				type: "POST",
				url: "query_status",
				data: '{"module":"cmsServer"}',
				success: function (data, status) {
					//console.log("query_status_storage:"+JSON.stringify(data));
					this.model.appendCmsServer(data);
					this.model.isStatusJsonComplete() &&
						this.view.refreshStatInfo(this.model.getStatusJson());
				}.bind(this),
				error: function (XMLHttpRequest) {
					//this.view.showErrorInfo("query-fail", JSON.parse(XMLHttpRequest.responseText));
				}.bind(this),
				dataType: "json",
			});
		}
	},

	startWatchStatus: function () {
		this.WatchStatusLock = true;
		this.queryDeviceStatus();
		this.queryDeviceStatus();
		this.queryDeviceStatus();
		this.queryDeviceStatus();
		this.queryDeviceStatus();
		this.queryDeviceStatus();
		this.queryDeviceStatus();
		this.queryDeviceStatus();
		this.timerWatchStatus = setInterval(
			this.queryDeviceStatus.bind(this),
			2000
		);
		return this;
	},

	stopWatchStatus: function () {
		this.timerWatchStatus &&
			clearInterval(this.timerWatchStatus) &&
			(this.timerWatchStatus = null);
		this.WatchStatusLock = false;
		return this;
	},

	requestDeviceTime: function () {
		// $.ajax({
		// 	type: "GET",
		// 	url: "/get_devtime",
		// 	success: function (data, status) {
		// 		console.log(data.epoch);
		// 		var utcSec = this.nowTime.getTimezoneOffset() * 60;
		// 		var devUTChour = this.model.getConfigJson().systemConfig.time.UTChour;
		// 		var devUTCmin = this.model.getConfigJson().systemConfig.time.UTCminute;
		// 		this.nowTime &&
		// 			this.nowTime.setTime(
		// 				(1 * data.epoch + utcSec + 3600 * devUTChour + 60 * devUTCmin) *
		// 					1000
		// 			);
		// 	}.bind(this),
		// 	error: function (XMLHttpRequest) {
		// 		this.view.showErrorInfo(
		// 			"query-fail",
		// 			JSON.parse(XMLHttpRequest.responseText)
		// 		);
		// 	}.bind(this),
		// 	dataType: "json",
		// });
	},

	refreshDeviceTime: function () {
		var timestamp = Math.round(this.nowTime.getTime() / 1000);
		timestamp % 10 == 0 && this.requestDeviceTime();
		this.nowTime.setTime((timestamp + 1) * 1000);
		this.view.refreshDeviceTime(timestamp);
	},

	startDeviceTime: function () {
		console.log("startDeviceTime");
		this.nowTime = new Date();
		console.log(this.nowTime);
		this.timerDeviceTime = setInterval(this.refreshDeviceTime.bind(this), 1000);

		return this;
	},

	stopDeviceTime: function () {
		this.timerDeviceTime &&
			clearInterval(this.timerDeviceTime) &&
			(this.timerDeviceTime = null);
		return this;
	},

	submitSliAlgParam(sender, args) {
		if (this.submitting) {
			return;
		}
		this.submitting = true;
		this.view.showLoading();
		console.log("configs:" + JSON.stringify(args));
		$.ajax({
			type: "POST",
			url: "/config",
			data: JSON.stringify(args),
			success: function (data) {
				this.view.showSuccessInfo("setting-success");
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"setting-failure",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			dataType: "json",
		});
	},

	submitSensitivity(sender, args) {
		if (this.submitting) {
			return;
		}
		this.submitting = true;
		this.view.showLoading();
		console.log("configs:" + JSON.stringify(args));
		$.ajax({
			type: "POST",
			url: "/config",
			data: JSON.stringify(args),
			success: function (data) {
				this.view.showSuccessInfo("setting-success");
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"setting-failure",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			dataType: "json",
		});
	},
	submitShelterEvent(sender, args) {
		if (this.submitting) {
			return;
		}
		this.submitting = true;
		this.view.showLoading();
		console.log("configs:" + JSON.stringify(args));
		$.ajax({
			type: "POST",
			url: "/config",
			data: JSON.stringify(args),
			success: function (data) {
				this.view.showSuccessInfo("setting-success");
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"setting-failure",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			dataType: "json",
		});
	},

	changeStreamTypeEvent(sender, args) {
		var type = args;
		console.log("stream type: " + type);
		this.startPreview(type);
	},

	submitImageProces(sender, args) {
		if (this.submitting) return;

		this.submitting = true;
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/config",
			data: JSON.stringify(args),
			success: function (data) {
				this.view.showSuccessInfo("setting-success");
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"setting-failure",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			dataType: "json",
		});
	},

	submitConfigs: function (sender, args) {
		if (this.submitting) {
			return;
		}
		if (args.hasOwnProperty("networkConfig")) {
			var host = window.location.host.slice(
					0,
					window.location.host.lastIndexOf(":8080")
				),
				ipaddr = args.networkConfig.ethernet.ipAddress,
				dhcp = args.networkConfig.ethernet.enableDHCP;
		}
		if (args.hasOwnProperty("algConfig")) {
			if (
				(this.model.getMachineType() == "DMS31V2" ||
					this.model.getMachineType() == "DMS885N") &&
				this.model.getConfigJson().mediaConfig.imageProcess.mirrorEnable
			) {
				var tmp =
					args.algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleLeft;
				args.algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleLeft =
					args.algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleRight;
				args.algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleRight =
					tmp;
			}
		}		

		this.submitting = true;
		this.view.showLoading();
		console.log("configs:" + JSON.stringify(args));
		$.ajax({
			type: "POST",
			url: "/config",
			data: JSON.stringify({ accessApi: { password: "" }, ...args }),
			success: function (data) {
				this.view.showSuccessInfo("setting-success");
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"setting-failure",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
				this.submitting = false;
			}.bind(this),
			dataType: "json",
		});

		if (args.hasOwnProperty("networkConfig")) {
			if (
				ipaddr != host &&
				!dhcp &&
				this.model.getMachineType().indexOf("IPC") == 0
			) {
				// IPC 才跳转，其他平台 有线网 无线网无法断定
				this.view.showSuccessInfo("rediret-address");
				window.location.href = "http://" + ipaddr + ":8080/config.html";
			}

			if (
				dhcp &&
				this.model.getMachineType().indexOf("IPC") == 0 && // IPC 才跳转，其他平台 有线网 无线网无法断定
				!this.model.getConfigJson().networkConfig.ethernet.enableDHCP
			) {
				console.log("please use searching tool to find it.");
				this.view.showUseDhcpInfo("setting-success");
				this.view.hideLoading();
			}
		}
	},

	cancelConfigs: function (sender, args) {
		console.log("cancelConfigs");
		this.view.refreshConfigs(this.model.getConfigJson());
	},

	saveConfigs: function (sender, args) {
		console.log("saveConfigs:" + args.saveConfigs);
		var jsonbody = {
			command: "setFactoryMode",
			factoryStat: args.saveConfigs ? 0 : 1,
		};
		console.log(JSON.stringify(jsonbody));
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: JSON.stringify(jsonbody),
			success: function () {
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"set-factory-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	restoreFactory: function (sender, args) {
		console.log("restoreFactory:" + args.type);
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/restore_factory",
			data: JSON.stringify(args),
			success: function () {
				this.view.hideLoading();
				if (args.type == "soft") {
					this.view.showSuccessInfo("restore-success");
				} else {
					this.view.showRestoring();
				}
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"restore-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	setIcon: function (sender, args) {
		var json_str;
		var json = {
			accessApi: { password: "" },
			algConfig: { pdsConf: [{ iconLocation: args }] },
		};
		json_str = JSON.stringify(json);
		console.log(json_str);
		$.ajax({
			type: "POST",
			url: "/config",
			data: json_str,
			success: function () {
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	submitCalibrationBoard: function (sender, args) {
		this.view.showLoading();
		var json_str;
		if (args.mode == "measure") {
			var json = { accessApi: { password: "" }, QRCodePosition: args.params };
			json_str = JSON.stringify(json);
			console.log(json_str);
			$.ajax({
				type: "POST",
				url: "/qrcode_calibration",
				data: json_str,
				success: function (data) {
					//二维码标定成功，填充默认的实际距离
					this.view.hideLoading();
					var points = [2000, 5000, 3000, 2000]; //默认距离
					this.view.setDistance(points);
					if (this.view.CalibrationJson.bCalibrated != "undefine")
						this.view.CalibrationJson.bCalibrated = true;
					this.view.showSuccessInfo("setting-success");
					this.view.hideLoading();
				}.bind(this),
				error: function (XMLHttpRequest) {
					this.view.showErrorInfo(
						"set calibrationBoard-fail",
						JSON.parse(XMLHttpRequest.responseText)
					);
					this.view.hideLoading();
				}.bind(this),
				dataType: "json",
			});
		} else if (args.mode == "developer" || args.mode == "getTrackPos") {
			var json = { accessApi: { password: "" }, TrackPosition: args.params };
			json_str = JSON.stringify(json);
			console.log(json_str);
			$.ajax({
				type: "POST",
				url: "/TrackPosition",
				data: json_str,
				success: function (data) {
					//二维码标定成功，填充默认的实际距离
					this.view.hideLoading();
				}.bind(this),
				error: function (XMLHttpRequest) {
					//this.view.showErrorInfo("set calibrationBoard-fail",JSON.parse(XMLHttpRequest.responseText));
					this.view.hideLoading();
				}.bind(this),
				dataType: "json",
			});
		}
	},

	changeCaliMode: function (sender, mode) {
		var json_str;
		var json;
		if (mode == "measure")
			json = {
				accessApi: { password: "" },
				algConfig: {
					[window.ch === "ch0" ? "algChn0" : "algChn1"]: {
						pd: { pdWorkMode: 1 },
					},
				},
			};
		//切换为measure模式，需要先 post pdWorkMode 置1
		else
			json = {
				accessApi: { password: "" },
				algConfig: {
					[window.ch === "ch0" ? "algChn0" : "algChn1"]: {
						pd: { pdWorkMode: 0 },
					},
				},
			}; //切换为measure模式，需要先 post pdWorkMode 置0

		json_str = JSON.stringify(json);
		console.log(json_str);
		$.ajax({
			type: "POST",
			url: "/config",
			data: json_str,
			success: function () {
				//$("#show-info").trigger("click");
			}.bind(this),
			error: function (XMLHttpRequest) {
				// this.view.showErrorInfo("change calibration mode error", JSON.parse(XMLHttpRequest.responseText));
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	selLang: function (sender, lang) {
		var json_str;
		var json;
		json = { accessApi: { password: "" }, systemConfig: { language: lang } };
		json_str = JSON.stringify(json);
		console.log(json_str);
		$.ajax({
			type: "POST",
			url: "/config",
			data: json_str,
			success: function () {}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	apcSetConfig: function (sender, args) {
		this.view.showLoading();
		var json_str;
		var json = {
			accessApi: { password: "" },
			algConfig: {
				apcConf: {
					apcDirection: args.direciton,
					apcDivider: args.diviPoints,
					astApcDetectionPoints: args.framePoints,
				},
			},
		};
		json_str = JSON.stringify(json);
		console.log(json_str);
		$.ajax({
			type: "POST",
			url: "/config",
			data: json_str,
			success: function () {
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"set apcConfig-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	guiMask: function (sender, args) {
		this.view.showLoading();
		var json_str;
		var json = {
			accessApi: { password: "" },
			mediaConfig: {
				[window.ch === "ch0" ? "chn0" : "chn1"]: { showGuiMask: args },
			},
		};
		json_str = JSON.stringify(json);
		$.ajax({
			type: "POST",
			url: "/config",
			data: json_str,
			success: function () {
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	calibration: function (sender, args) {
		console.log("calibration: " + args.mode + " chn " + args.opChn);
		this.view.showLoading();
		var json_str;
		//console.log(args.pdHollow.points);
		if (args.mode === 32) {
			if (0 == args.opChn) {
				if (args.roistyle <= 4)
					var json = {
						accessApi: {
							password: "",
						},
						algConfig: {
							[window.ch === "ch0" ? "algChn0" : "algChn1"]: {
								pd: {
									pdRoiStyle: args.roistyle,
									pdRoiOutline: [
										...args.params,
										...this.model
											.getConfigJson()
											.algConfig.pd.pdRoiOutline.slice(16)
											.map((item) => (item < 0 ? 0 : item)),
									],
									pdHollow: parseInt(args.pdHollow.enable, 10),
									pdRoiEllipseB: args.axis,
								},
							},
						},
					};
				else
					var json = {
						accessApi: { password: "" },
						algConfig: {
							[window.ch === "ch0" ? "algChn0" : "algChn1"]: {
								pd: {
									pdRoiStyle: args.roistyle,
									pdRoiBoard: args.pdRoiBoard,
								},
							},
						},
					};
			} else {
				var json = {
					accessApi: { password: "" },
					algConfig: {
						irAlgConfig: {
							pdsConf: { pdRoiStyle: args.roistyle, pdRoiOutline: args.params },
						},
					},
				};
			}

			json_str = JSON.stringify(json);
			console.log(json_str);
			$.ajax({
				type: "POST",
				url: "/config",
				data: json_str,
				success: function () {
					this.view.hideLoading();
				}.bind(this),
				error: function (XMLHttpRequest) {
					this.view.showErrorInfo(
						"set pdRoiOutline-fail",
						JSON.parse(XMLHttpRequest.responseText)
					);
					this.view.hideLoading();
				}.bind(this),
				dataType: "json",
			});
		} else {
			json_str = '{"accessApi": {"password":""},"mode": ' + args.mode + "}";

			$.ajax({
				type: "POST",
				url: "/calibration",
				data: json_str,
				success: function () {
					if (args.mode === 1) {
						$("#calibration_start_div").show();
						$("#calibration_pre_div").hide();
						$("#index_menu").hide();
						$("#btn-logout").hide();
					} else if (args.mode == 0) {
						//this.view.jumpToLoginPage();
					} else {
						//setTimeout(function(){ alert("calibration success"); }, 6000);
						this.view.showCalibrationResult("calibration-success");
						$("#calibration_button").hide();
						$("#calibration_pre_div").show();
						$("#index_menu").show();
						$("#btn-logout").show();
					}
					this.view.hideLoading();
				}.bind(this),
				error: function (XMLHttpRequest) {
					this.view.showErrorInfo(
						"calibration-fail",
						JSON.parse(XMLHttpRequest.responseText)
					);
					var json = JSON.parse(XMLHttpRequest.responseText);
					if (args.mode == 2) {
						if (json.errorType == 2) {
							//超时错误退出标定，左右角超出范围不退出标定(errorType = 7)
							$("#calibration_button").hide();
							$("#calibration_pre_div").show();
							$("#index_menu").show();
							$("#btn-logout").show();
						}
					}
					this.view.hideLoading();
				}.bind(this),
				dataType: "json",
			});
		}
	},

	register: function (sender, args) {
		var post_data = {};
		post_data.accessApi = {};
		post_data.accessApi.password = "";
		post_data.command = args.command;
		if (args.command == "addUser") {
			$("#newUserBtn").attr("disabled", true);
		}
		if (typeof args.userName != "undefined") {
			post_data.userName = args.userName;
		}
		if (typeof args.userList != "undefined") {
			post_data.userList = args.userList;
		}
		//console.log("register:"+args.type);
		this.view.showLoading();

		var json_str = JSON.stringify(post_data);
		$.ajax({
			type: "POST",
			url: "/face_recognition",
			data: json_str,
			success: function (data, status) {
				if (
					args.command == "modifyUser" ||
					args.command == "addUser" ||
					args.command == "deleteUser" ||
					args.command == "batchDelete"
				) {
					if (typeof data.result != "undefined") {
						if (data.result == "success") {
							this.register(null, { command: "getUsers" });
						} else {
							createNormalDialog(
								"fail",
								["Confirm"],
								[
									function () {
										removeNormalDialog("hidecover");
									},
								]
							);
						}
					} else {
						this.register(null, { command: "getUsers" });
					}

					if (args.command == "addUser") {
						$("#newUserBtn").attr("disabled", false);
						window.localStorage.setItem("freshUserList", "true");
						location.reload();
					}
				} else if (args.command == "frLogin") {
					console.log("testlogin");
				} else {
					//undefined时也传入，这样在删除最后一个数据是，列表会清空，否则会残留
					var register_d = this;
					var lang = $("#sel-lang").val();
					//通过模板定义的id获取模板
					var tpl = $("#userListTemplate").html();
					//预编译模板
					var template = Handlebars.compile(tpl);
					//传入需要填充的数据匹配
					var html = template(data.Userlist);
					//插入模板到ul中
					$("#userList").html(html);
					var divArr = $("#userList div ");
					$.each(divArr, function (i, n) {
						var divNode = $(this);
						divNode.find(".username_delete_img").click(function () {
							register_d.register(null, {
								command: "deleteUser",
								userName: divNode.find(".username").text(),
							});
						});
						divNode.find(".username_edit_img").click(function () {
							createInputDialog(
								register_d.view.model.getKeyLang("edit", lang),
								[
									register_d.view.model.getKeyLang("confirm", lang),
									register_d.view.model.getKeyLang("cancel", lang),
								],
								[
									function (name) {
										if (name == "valueIsEmpty") {
											removeInputDialog();
											createNormalDialog(
												"empty",
												[register_d.view.model.getKeyLang("confirm", lang)],
												[
													function () {
														removeNormalDialog("hidecover");
													},
												]
											);
										} else {
											var reg = new RegExp(
												"[\\s`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】\\\\'；：\"\"'。，、？]"
											);
											if (reg.test(name) || name.indexOf("\\") >= 0) {
												removeInputDialog();
												createNormalDialog(
													register_d.view.model.getKeyLang("Illega", lang),
													[register_d.view.model.getKeyLang("confirm", lang)],
													[
														function () {
															removeNormalDialog("hidecover");
														},
													]
												);
											} else {
												var namedOK = true;
												$.each(divArr, function (i, n) {
													var divNode = $(this);
													var haduser = divNode.find(".username").text();
													if (haduser == name) {
														namedOK = false;
														register_d.view.$popupShowInfo
															.find("h1")
															.text(
																register_d.view.model.getKeyLang(
																	"Named Error",
																	lang
																)
															);
														register_d.view.$popupShowInfo
															.find("h3")
															.text(
																register_d.view.model.getKeyLang(
																	"Name Exists",
																	lang
																)
															);
														register_d.view.$popupShowInfo.find("p").text("");
														register_d.view.$popupShowInfo
															.find("a")
															.click(function () {
																removeInputDialog("hidecover");
															});
														$("#show-info").trigger("click");
													}
												});
												if (namedOK) {
													removeInputDialog("hidecover");
													register_d.register(null, {
														command: "modifyUser",
														userName:
															divNode.find(".username").text() + "/" + name,
													});
												}
											}
										}
									},
									function () {
										removeInputDialog("hidecover");
									},
								]
							);
						});
					});
					if (typeof data.Userlist != "undefined") {
						$("#deleteUserBtn").show();
					} else {
						$("#deleteUserBtn").hide();
					}
				}

				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				if (args.command == "frLogin") {
					this.view.showRegRes("AF", JSON.parse(XMLHttpRequest.responseText));
				} else if (args.command == "modifyUser") {
					this.view.showRegRes(
						"Error-MU",
						JSON.parse(XMLHttpRequest.responseText)
					);
				} else if (args.command == "addUser") {
					$("#newUserBtn").attr("disabled", false);
					this.view.showRegRes(
						"Error-AU",
						JSON.parse(XMLHttpRequest.responseText)
					);
				} else if (args.command == "deleteUser") {
					this.view.showRegRes(
						"Error-DU",
						JSON.parse(XMLHttpRequest.responseText)
					);
				} else if (args.command == "batchDelete") {
					this.view.showRegRes(
						"Error-BD",
						JSON.parse(XMLHttpRequest.responseText)
					);
				}
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	ptz: function (sender, args) {
		$.ajax({
			type: "POST",
			url: "/ptz",
			data: JSON.stringify(args),
			success: function (data, status) {}.bind(this),
			error: function (XMLHttpRequest) {
				console.log(
					"showErrorInfo:" +
						JSON.stringify(JSON.parse(XMLHttpRequest.responseText))
				);
			}.bind(this),
			dataType: "json",
		});
	},

	afCalibrate: function (sender, args) {
		$.ajax({
			type: "POST",
			url: "/af_cal",
			data: JSON.stringify(args),
			success: function (data, status) {}.bind(this),
			error: function (XMLHttpRequest) {
				console.log(
					"showErrorInfo:" +
						JSON.stringify(JSON.parse(XMLHttpRequest.responseText))
				);
			}.bind(this),
			dataType: "json",
		});
	},

	requestConfigs: function () {
		$.ajax({
			type: "GET",
			url: "/config",
			async: false,
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.model.setConfigJson(data);
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"get-config-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	refreshQueryInfo: function (sender, args) {
		console.log("refreshQueryInfo");
		$.ajax({
			type: "GET",
			url: "/config",
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.model.setConfigJson(data);
				if (
					data.hasOwnProperty("ipcIdentification") &&
					data.ipcIdentification.hasOwnProperty("hardware")
				) {
					if (data.ipcIdentification.hardware != null) {
						if (
							data.ipcIdentification.hardware.indexOf("ADA42V1") == -1 &&
							//&& data.ipcIdentification.hardware.indexOf("ADA32V2") == -1
							data.ipcIdentification.hardware.indexOf("DMS31V2") == -1 &&
							data.ipcIdentification.hardware.indexOf("ADA47V1") == -1 &&
							data.ipcIdentification.hardware.indexOf("ADA32C4") == -1
						) {
							$("#li_recordlist").hide();
						} else {
							$("#li_recordlist").show();
						}
					}
				}
				if (
					data.hasOwnProperty("ipcIdentification") &&
					data.ipcIdentification.hasOwnProperty("customer")
				) {
					this.view.customer = data.ipcIdentification.customer;
					if (data.ipcIdentification.customer == "202018") {
						$("#li_alarmlist").css("display", "block");
					}

					if (this.view.customer == "200032") {
						this.model.lang = luis_lang;
					}
				}
				$("#format-sdcard")
					.html(this.view.formatTemplate(this.model.getConfigJson()))
					.trigger("create");
				this.view.changeChildrenLang("#format-sdcard");
				this.view
					.createChildren()
					.setupHandlers()
					.enable()
					.changeLang()
					.changeTheme();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"get-config-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
			}.bind(this),
			dataType: "json",
		});
	},

	getCellularStat: function () {
		$.ajax({
			type: "POST",
			url: "query_status",
			data: '{"module":"cellular"}',
			success: function (data, status) {
				this.model.setCellularJson(data);
			}.bind(this),
			error: function (XMLHttpRequest) {
				//this.view.showErrorInfo("query-fail", JSON.parse(XMLHttpRequest.responseText));
			}.bind(this),
			dataType: "json",
		});
	},

	refreshConfigs: function (sender, args) {
		console.log("refreshConfigs");
		this.view.showLoading();
		$.ajax({
			type: "GET",
			url: "/config",
			success: function (data, status) {
				if (
					data.hasOwnProperty("ipcIdentification") &&
					data.ipcIdentification.hasOwnProperty("customer")
				) {
					this.view.customer = data.ipcIdentification.customer;
					if (this.view.customer == "200032") {
						this.model.lang = luis_lang;
					}
				}
				if (args == "only") {
					if (data != null) {
						if (data.systemConfig != null) {
							this.view.webuiFull = data.systemConfig.webuiFull;
						}
					}
					if (
						data.hasOwnProperty("ipcIdentification") &&
						data.ipcIdentification.hasOwnProperty("hardware")
					) {
						this.view.hardware = data.ipcIdentification.hardware;
						// this.view.board = data.ipcIdentification.board;
						if (data.ipcIdentification.hardware != null) {
							console.log(data);
							$("#index-menu")
								.html(this.view.indexMenuTemplate(data))
								.trigger("create");
							$("#calibration-info")
								.html(this.view.calibrationPreTemplate(data))
								.trigger("create");
							// if (data.ipcIdentification.hardware == "ADA42V1") {
							// 	$("#cam-win-1x").hide();
							// 	$("#cam-win-4x").show();
							// 	$("#td-btn-calibration").hide();
							// 	$("#draw_canvas").hide();
							// }

							// if (data.ipcIdentification.hardware == "ADA42PTZV1") {
							// 	$("#cam-win-1x").hide();
							// 	$("#cam-win-2x").show();
							// 	$("#td-btn-calibration").hide();
							// 	$(".btn-trackpos").hide();
							// 	$(".btn-fullscreen").hide();
							// 	$("#draw_canvas").hide();
							// }

							//红外摄像头和云台跟踪摄像头
							// if (
							// 	data.ipcIdentification.board == 25 ||
							// 	data.ipcIdentification.board == 26
							// ) {
							// 	if (0 != data.mediaConfig.voStream.split) {
							// 		$("#cam-win-1x").hide();
							// 		$("#cam-win-2x").show();
							// 		$("#td-btn-calibration").hide();
							// 		$("#draw_canvas").hide();
							// 	} else {
							// 		$("#cam-win-2x").hide();
							// 	}
							// 	this.view.voSplit = data.mediaConfig.voStream.split;
							// 	$(".control-wrapper").hide();
							// } else {
							// 	this.view.voSplit = data.mediaConfig.voStream.split;
							// 	$("#cam-win-2x").hide();
							// 	$(".control-wrapper").hide();
							// }
						}
					}
					if (data.hasOwnProperty("algConfig")) {
						const pd =
							data.algConfig[window.ch === "ch0" ? "algChn0" : "algChn1"].pd;
						this.view.pdRoiOutline = pd.pdRoiOutline;
						this.view.pdRoiStyle = pd.pdRoiStyle;
						this.view.pdMinorAxis = pd.pdRoiEllipseB;
						this.view.pdRoiBoard = pd.pdRoiBoard;
						// this.view.pdHollow = pd.pdHollow;
						this.view.pdHollow.points = pd.pdRoiOutline;
						this.view.lastRoiStyle = this.view.pdRoiStyle;
						this.view.CalibrationJson = {};
						this.view.CalibrationJson.pdWorkMode = pd.pdWorkMode;
						this.view.CalibrationJson.bCalibrated = pd.bCalibrated;
						this.view.CalibrationJson.pdDetectWBorder = pd.pdDetectWBorder;
						this.view.CalibrationJson.pdDetectRedFBorder =
							pd.pdDetectRedFBorder;
						this.view.CalibrationJson.pdDetectYellowFBorder =
							pd.pdDetectYellowFBorder;
						this.view.CalibrationJson.pdDetectGreenFBorder =
							pd.pdDetectGreenFBorder;
						if (pd.pdWorkMode == 1) this.view.calimode = "measure";
						else this.view.calimode = "normal";
					}

					this.view.sversion = data.ipcIdentification.sversion;

					$("#div-lang")
						.html(this.view.selLangTemplate(data))
						.trigger("create");
					this.view
						.createChildren()
						.setupHandlers()
						.enable()
						.changeLang()
						.changeTheme()
						.iosSelectDeal(); //ios safari浏览器bug，不能隐藏option，手动移除不需要的option
					this.view.hideLoading();
					this.model.setConfigJson(data);

					$(".rangeinput_input").on("input propertychange", function () {
						var min = $(this).attr("min") * 1;
						var max = $(this).attr("max") * 1;
						var real_value = ((this.value - min) * 100) / (max - min);
						$(this).css("background-size", real_value + "% 100%");
						$(this)
							.parents(".rangeinput")
							.find(".rangeinput_value")
							.html(this.value);
					});
					$("#dig-zoom").on("input propertychange", function () {
						let real_value = this.value;
						let zoomJson;
						zoomJson = {
							zoom: {
								digZoom: Number(real_value),
								optZoom: 0.0,
							},
						};
						$.ajax({
							type: "POST",
							url: "/zoom",
							data: JSON.stringify(zoomJson),
							success: function (data, status) {}.bind(this),
							error: function (XMLHttpRequest) {
								console.log(
									"showErrorInfo:" +
										JSON.stringify(JSON.parse(XMLHttpRequest.responseText))
								);
							}.bind(this),
							dataType: "json",
						});
					});

					$(".rangeinput_input").each(function () {
						var min = $(this).attr("min") * 1;
						var max = $(this).attr("max") * 1;
						var real_value = ((this.value - min) * 100) / (max - min);
						$(this).css("background-size", real_value + "% 100%");
						$(this)
							.parents(".rangeinput")
							.find(".rangeinput_value")
							.html(this.value);
					});
				} else {
					// if (
					// 	data.ipcIdentification.hardware == "DMS31V2" ||
					// 	data.ipcIdentification.hardware == "DMS885N"
					// ) {
					// 	if (data.mediaConfig.imageProcess.mirrorEnable) {
					// 		var tmp =
					// 			data.algConfig.dmsConf.dmsAdvancedSettings
					// 				.dmsDistractionAngleLeft;
					// 		data.algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleLeft =
					// 			data.algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleRight;
					// 		data.algConfig.dmsConf.dmsAdvancedSettings.dmsDistractionAngleRight =
					// 			tmp;
					// 	}
					// }
					this.model.setConfigJson(data);

					console.log("this.model.getConfigJson()", this.model.getConfigJson());
					this.view.refreshConfigs(this.model.getConfigJson()).hideLoading();
				}
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"get-config-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	getpdData: function (sender, args) {
		$.ajax({
			type: "GET",
			url: "/config",
			success: function (data, status) {
				if (
					data.hasOwnProperty("algConfig") &&
					data.algConfig.hasOwnProperty("pdsConf")
				) {
					this.view.pdRoiOutline =
						data.algConfig.pdsConf[this.view.opChn].pdRoiOutline;
					this.view.pdRoiStyle =
						data.algConfig.pdsConf[this.view.opChn].pdRoiStyle;
					this.view.pdMinorAxis =
						data.algConfig.pdsConf[this.view.opChn].pdRoiEllipseB;
					this.view.pdRoiBoard =
						data.algConfig.pdsConf[this.view.opChn].pdRoiBoard;
					console.log(this.view.pdRoiBoard);
				}
				this.view.changeCaliMode();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"get-config-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
			}.bind(this),
			dataType: "json",
		});
	},

	getAPCData: function (sender, args) {
		$.ajax({
			type: "GET",
			url: "/config",
			success: function (data, status) {
				if (
					data.hasOwnProperty("algConfig") &&
					data.algConfig.hasOwnProperty("apcConf")
				) {
					if (data.algConfig.apcConf.hasOwnProperty("apcDirection")) {
						this.view.apcDirection = data.algConfig.apcConf.apcDirection;
					}
					if (data.algConfig.apcConf.hasOwnProperty("apcDivider")) {
						this.view.apcDivider = data.algConfig.apcConf.apcDivider;
					}
					if (data.algConfig.apcConf.hasOwnProperty("astApcDetectionPoints")) {
						this.view.astApcDetectionPoints =
							data.algConfig.apcConf.astApcDetectionPoints;
					}
				}
				this.view.drawAPCDetection();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"get-config-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
			}.bind(this),
			dataType: "json",
		});
	},

	refreshSysInfo: function (sender, args) {
		console.log("refreshSysInfo");
		this.view.showLoading();
		$.ajax({
			type: "GET",
			url: "/config",
			success: function (data, status) {
				console.log(JSON.stringify(data));
				if (
					data.hasOwnProperty("ipcIdentification") &&
					data.ipcIdentification.hasOwnProperty("customer")
				) {
					this.view.customer = data.ipcIdentification.customer;
					if (this.view.customer == "200032") {
						this.model.lang = luis_lang;
					}
				}
				this.model.setConfigJson(data);
				this.view.refreshSysInfo(this.model.getConfigJson()).hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"get-config-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
			}.bind(this),
			dataType: "json",
		});
	},

	switchCam: function (sender, args) {
		console.log("switchCam:" + args.chnNum);
		if (1 == args.chnNum) {
			this.previewFps = 30;
		} else {
			this.previewFps = 12;
		}

		return this;
	},

	pingNetwork: function (sender, args) {
		console.log("pingNetwork:" + args.network);
		var json = {
			command: "ping",
			useNetcard: args.network == "cellular" ? "cell" : "wifi",
			destAddr: args.destAddr,
		};
		console.log(JSON.stringify(json));
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: JSON.stringify(json),
			success: function (data, status) {
				console.log(
					data.interface +
						" " +
						data.ipaddr +
						" " +
						Math.round(Number(data["speed(ms)"])) +
						"ms"
				);
				this.view.refreshPingResult(
					args.resultId,
					data.interface +
						" " +
						data.ipaddr +
						" " +
						Math.round(Number(data["speed(ms)"])) +
						"ms"
				);
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"ping-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	operateBlkDev: function (sender, args) {
		console.log(JSON.stringify(args));
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/formatSD",
			data: JSON.stringify(args),
			success: function (data, status) {
				this.view.showSuccessInfo("operate-requset-success");
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"ping-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	cvbsOutput: function (sender, args) {
		var chn = "";
		for (var i = 0; i < 4; i++) {
			args.dispChnEnum & (1 << i) && (chn += i + 1 + " ");
		}
		args.audioOutput && (chn += "a");
		chn = chn || "0";
		console.log(JSON.stringify({ command: "dispChn", chn: chn }));
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: JSON.stringify({ command: "dispChn", chn: chn }),
			success: function (data, status) {
				this.model.updateCvbsStatus(args);
				this.view.refreshCameraInfo(this.model.getCameraJson());
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"cvbs-output-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	changePassword: function (sender, args) {
		console.log("changePassword:", JSON.stringify(args));
		var jsonbody = {
			changeUser: args.changeUser || "",
			oldPassword: args.oldPassword,
			newPassword: args.newPassword,
		};
		console.log("Password change request:", JSON.stringify(jsonbody));
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/change_pwd",
			data: JSON.stringify(jsonbody),
			success: function () {
				this.view.showSuccessInfo("modify-success");
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"modify-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	changeTime: function (sender, args) {
		//以下定义的是本地时间与格林威治时间之间的时差
		var sysTimeZone = [
			{ name: "GMT0", value: 0 },
			{ name: "CET-1", value: 60 },
			{ name: "EET-2", value: 120 },
			{ name: "AST-3", value: 180 },
			{ name: "IRT-3:30", value: 210 },
			{ name: "GMT-4", value: 240 },
			{ name: "AFT-4:30", value: 270 },
			{ name: "PKT-5", value: 300 },
			{ name: "IST-5:30", value: 330 },
			{ name: "NPT-5:45", value: 345 },
			{ name: "BDT-6", value: 360 },
			{ name: "MMT-6:30", value: 390 },
			{ name: "ICT-7", value: 420 },
			{ name: "CST-8", value: 480 },
			{ name: "JST-9", value: 540 },
			{ name: "CST-9:30", value: 570 },
			{ name: "EST-10", value: 600 },
			{ name: "SBT-11", value: 660 },
			{ name: "NZST-12", value: 720 },
			{ name: "TOT-13", value: 780 },
			{ name: "CVT1", value: -60 },
			{ name: "GST2", value: -120 },
			{ name: "BRT3", value: -180 },
			{ name: "AST4", value: -240 },
			{ name: "VET4:30", value: -270 },
			{ name: "EST5", value: -300 },
			{ name: "CST6", value: -360 },
			{ name: "MST7", value: -420 },
			{ name: "PST8", value: -480 },
			{ name: "AKST9", value: -540 },
			{ name: "HAST10", value: -600 },
			{ name: "SST11", value: -660 },
		];
		var sysDate = new Date(); //获取系统当前时间
		var timeDelta = -1 * sysDate.getTimezoneOffset(); //该函数获取的是格林威治时间与本地时间之间的时差，需要取反
		var TimeZone = "";
		var tempVal = 0;
		var value = [];
		for (var i = 0; i < sysTimeZone.length; i++) {
			value[i] = Math.abs(timeDelta - sysTimeZone[i].value);
		}
		tempVal = Math.min.apply(null, value);
		for (var i = 0; i < value.length; i++) {
			if (value[i] == tempVal) {
				TimeZone = sysTimeZone[i].name;
				break;
			}
		}
		console.log("TimeZone:" + TimeZone);
		var jsonbody = {
			TimeZone: TimeZone,
			TimeValue: {
				Year: sysDate.getFullYear(),
				Month: sysDate.getMonth() + 1,
				Day: sysDate.getDate(),
				Hour: sysDate.getHours(),
				Minute: sysDate.getMinutes(),
				Second: sysDate.getSeconds(),
			},
		};
		console.log(JSON.stringify(jsonbody));
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/lockInTime",
			data: JSON.stringify(jsonbody),
			success: function () {
				this.view.showSuccessInfo("SetTime-success");
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"SetTime-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	formateSd: function (sender, args) {
		console.log("formate:");
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/format",
			data: JSON.stringify(args),
			success: function (data, status) {
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"format-request-failed",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},

	reboot: function (sender, args) {
		console.log("reboot");
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/reboot",
			data: JSON.stringify({}),
			success: function (data, status) {
				this.view.showRebooting();
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"reboot-request-failed",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	scanSsid: function () {
		console.log("scanSsid");
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: '{"command":"getWifiScan"}',
			success: function (data, status) {
				console.log(JSON.stringify(data));
				data.wifiNewScan.length &&
					data.wifiNewScan.sort(function (a, b) {
						return b["signal"] - a["signal"];
					});
				this.model.setScanSsidJson(data);
				this.view.refreshScanSsid(data);
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"scan-wifi-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	addScanSsid: function (sender, args) {
		console.log("addScanSsid:" + JSON.stringify(args));
		this.model.addScanSsid(args);
		this.view.refreshScanSsid(this.model.getScanSsidJson());
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/config",
			data: JSON.stringify(this.model.getScanSsidForConfig()),
			success: function (data) {
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"setting-failure",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	delScanSsid: function (sender, args) {
		console.log("delScanSsid:" + JSON.stringify(args));
		this.model.delScanSsid(args);
		this.view.refreshScanSsid(this.model.getScanSsidJson());
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/config",
			data: JSON.stringify(this.model.getScanSsidForConfig()),
			success: function (data) {
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"setting-failure",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	refreshDevInfo: function (sender, args) {
		console.log("refreshDevInfo:" + args.device);
		switch (args.device) {
			case "cellular":
				this.refreshCellularInfo();
				break;
			case "wifi":
				this.refreshWifiInfo();
				break;
			case "storage":
				this.refreshStorageInfo();
				break;
			case "camera":
				this.refreshCameraInfo();
				break;
			case "power":
				this.refreshPowerInfo();
				break;
			case "gpio":
				this.refreshGpioInfo();
				break;
			case "usbdev":
				this.refreshUsbInfo();
				break;
		}
		return this;
	},

	refreshAllDevInfo: function () {
		this.deviceInfoStat = {};
		this.refreshCellularInfo()
			.refreshWifiInfo()
			.refreshStorageInfo()
			.refreshCameraInfo()
			.refreshPowerInfo()
			.refreshGpioInfo()
			.refreshUsbInfo();
		return this;
	},

	isDevInfoComplete: function () {
		if (
			this.deviceInfoStat.hasOwnProperty("cellular") &&
			this.deviceInfoStat.hasOwnProperty("wifi") &&
			this.deviceInfoStat.hasOwnProperty("storage") &&
			this.deviceInfoStat.hasOwnProperty("camera") &&
			this.deviceInfoStat.hasOwnProperty("power") &&
			this.deviceInfoStat.hasOwnProperty("gpio") &&
			this.deviceInfoStat.hasOwnProperty("usb")
		) {
			return true;
		} else {
			return false;
		}
	},

	refreshCellularInfo: function () {
		this.view.showLoading();
		this.model.setCellularJson({});
		$.ajax({
			type: "GET",
			url: "/cellInfo",
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.model.appendCellularInfo(data);
				this.model.isCellularJsonComplete() &&
					this.view.refreshCellInfo(this.model.getCellularJson()) &&
					(this.deviceInfoStat.cellular = true);
				this.model.isCellularJsonComplete() &&
					this.isDevInfoComplete() &&
					this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.cellular = false;
				this.view.showErrorInfo(
					"get-cellInfo-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		$.ajax({
			type: "GET",
			url: "/config",
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.model.setConfigJson(data);
				this.model.appendCellularConf(data);
				this.model.isCellularJsonComplete() &&
					this.view.refreshCellInfo(this.model.getCellularJson()) &&
					(this.deviceInfoStat.cellular = true);
				this.model.isCellularJsonComplete() &&
					this.isDevInfoComplete() &&
					this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.cellular = false;
				this.view.showErrorInfo(
					"get-config-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		$.ajax({
			type: "POST",
			url: "/test",
			data: '{"command":"getVarInfoMessage","type":"network"}',
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.model.appendCellularStat(data);
				this.model.isCellularJsonComplete() &&
					this.view.refreshCellInfo(this.model.getCellularJson()) &&
					(this.deviceInfoStat.cellular = true);
				this.model.isCellularJsonComplete() &&
					this.isDevInfoComplete() &&
					this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.cellular = false;
				this.view.showErrorInfo(
					"get-network-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		$.ajax({
			type: "POST",
			url: "/test",
			data: '{"command":"getWifiInfo"}',
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.model.appendCellularOnline(data);
				this.model.isCellularJsonComplete() &&
					this.view.refreshCellInfo(this.model.getCellularJson()) &&
					(this.deviceInfoStat.cellular = true);
				this.model.isCellularJsonComplete() &&
					this.isDevInfoComplete() &&
					this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.cellular = false;
				this.view.showErrorInfo(
					"get-wifiinfo-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	refreshWifiInfo: function () {
		console.log("refreshWifiInfo");
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: '{"command":"getWifiInfo"}',
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.deviceInfoStat.wifi = true;
				this.view.refreshWifiInfo(data);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.wifi = false;
				this.view.showErrorInfo(
					"get-wifiinfo-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	refreshStorageInfo: function () {
		console.log("refreshStorageInfo");
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: '{"command":"getStorageInfo"}',
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.deviceInfoStat.storage = true;
				this.view.refreshStorageInfo(data);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.storage = false;
				this.view.showErrorInfo(
					"get-storage-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	refreshCameraInfo: function () {
		console.log("refreshCameraInfo");
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: '{"command":"getCameraInfo"}',
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.deviceInfoStat.camera = true;
				this.model.setCameraJson(data);
				this.view.refreshCameraInfo(data);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.camera = false;
				this.view.showErrorInfo(
					"get-camera-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	refreshPowerInfo: function () {
		console.log("refreshPowerInfo");
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: '{"command":"getPowerInfo"}',
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.deviceInfoStat.power = true;
				this.view.refreshPowerInfo(data);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.power = false;
				this.view.showErrorInfo(
					"get-power-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	refreshGpioInfo: function () {
		console.log("refreshGpioInfo");
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: '{"command":"getTriggerStatus"}',
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.deviceInfoStat.gpio = true;
				this.view.refreshGpioInfo(data);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.gpio = false;
				this.view.showErrorInfo(
					"get-gpio-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	refreshUsbInfo: function () {
		console.log("refreshUsbInfo");
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: '{"command":"lsusb"}',
			success: function (data, status) {
				console.log(JSON.stringify(data));
				this.deviceInfoStat.usb = true;
				this.view.refreshUsbInfo(data);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.deviceInfoStat.usb = false;
				this.view.showErrorInfo(
					"get-usb-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.isDevInfoComplete() && this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});

		return this;
	},

	query: function (sender, args) {
		console.log("query:" + JSON.stringify(args));
		if (this.querying) {
			return;
		}
		console.log(JSON.stringify(args));
		this.querying = true;
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/scan",
			data: JSON.stringify(args),
			success: function (data, status) {
				console.log(JSON.stringify(data));
				switch (args.scanType) {
					case 1:
						this.view.refreshRecordList(data);
						break;
					case 2:
						this.view.refreshEventList(data, "generate");
						break;
					case 3:
						this.view.refreshEventList(data, "clip");
						break;
				}
				this.view.hideLoading();
				this.querying = false;
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"query-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
				this.querying = false;
			}.bind(this),
			dataType: "json",
		});
	},

	queryLog: function (sender, args) {
		console.log("queryLog:" + JSON.stringify(args));
		if (this.querying) {
			return;
		}
		this.querying = true;
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/query_log",
			data: JSON.stringify(args),
			success: function (data, status) {
				this.view.refreshLogList(data);
				this.view.hideLoading();
				this.querying = false;
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"query-log-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
				this.querying = false;
			}.bind(this),
			dataType: "json",
		});
	},

	queryImg: function (sender, args) {
		console.log("queryImg:" + JSON.stringify(args));
		var maxPicNum = 100,
			minInterval = 10,
			interval =
				((args.endTime - args.startTime) * args.chn.length) /
				(maxPicNum - args.chn.length),
			time = args.startTime,
			json = {};
		interval < minInterval && (interval = minInterval);
		json.jpegGrepArrayList = [];
		while (time <= args.endTime) {
			for (var i = 0; i < args.chn.length; i++) {
				json.jpegGrepArrayList.push({
					time: Math.round(time),
					chn: args.chn[i],
				});
			}
			time += interval;
		}
		if (interval > minInterval) {
			for (i = 0; i < args.chn.length; i++) {
				json.jpegGrepArrayList.push({
					time: Math.round(args.endTime),
					chn: args.chn[i],
				});
			}
		}
		json.jpegGrepArrayList.sort(function (a, b) {
			if (a["chn"] < b["chn"]) {
				return -1;
			} else if (a["chn"] > b["chn"]) {
				return 1;
			} else if (a["time"] < b["time"]) {
				return -1;
			} else if (a["time"] > b["time"]) {
				return 1;
			} else {
				return 0;
			}
		});
		console.log("jsonbody:" + JSON.stringify(json));
		if (this.querying) {
			return;
		}
		this.querying = true;
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "jpegCollectionLocalGrep",
			data: JSON.stringify(json),
			success: function (data, status) {
				console.log(JSON.stringify(data));
				data.jpegGrepArrayList.sort(function (a, b) {
					if (a["time"] < b["time"]) {
						return -1;
					} else if (a["time"] > b["time"]) {
						return 1;
					} else if (a["chn"] < b["chn"]) {
						return -1;
					} else if (a["chn"] > b["chn"]) {
						return 1;
					} else {
						return 0;
					}
				});
				this.view
					.refreshGallery(data)
					.refreshSliderTime(args.startTime, args.endTime)
					.refreshSlider()
					.initPhotoSwipe();
				this.view.hideLoading();
				this.querying = false;
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"query-img-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
				this.querying = false;
			}.bind(this),
			dataType: "json",
		});
	},

	showStatPage: function (sender, args) {
		if (!this.WatchStatusLock) {
			//避免重复点击，导致多个定时循环请求
			console.log("showStatPage");
			this.startWatchStatus();
		}
		return this;
	},

	showLogPage: function (sender, args) {
		console.log("showLogPage");
		this.stopWatchStatus();
		return this;
	},

	showRecordPage: function (sender, args) {
		this.stopWatchStatus();
		this.view.showLoading();

		var json = {
			page: args.page,
			type: args.type,
			filetype: args.filetype,
			step: 20,
		};
		var json_str = JSON.stringify(json);
		console.log("showRecordPage:  " + json_str);
		$.ajax({
			type: "POST",
			url: "/query_record",
			data: json_str,
			dataType: "json",
			success: function (data, status) {
				this.view.refreshRecordList(data).hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showPlaybackError(JSON.parse(XMLHttpRequest.responseText));
			}.bind(this),
		});

		return this;
	},
	getRecordPages: function (sender, args) {
		this.stopWatchStatus();
		this.view.showLoading();

		var json = {
			page: args.page,
			type: args.type,
			filetype: args.filetype,
			step: 20,
		};
		var json_str = JSON.stringify(json);
		$.ajax({
			type: "POST",
			url: "/query_record",
			data: json_str,
			dataType: "json",
			success: function (data, status) {
				var args = {};
				if (
					data.hasOwnProperty("normal_video_count") &&
					data.hasOwnProperty("alarm_video_count") &&
					data.hasOwnProperty("alarm_pic_count")
				) {
					this.view.NormalVideoTotalPage = data.normal_video_count;
					this.view.AlarmVideoTotalPage = data.alarm_video_count;
					this.view.AlarmPicTotalPage = data.alarm_pic_count;

					args.page = this.view.AlarmVideoPage;
					args.type = this.view.recordType;
					args.filetype = this.view.fileType;
					this.view.showRecordPage.notify(args);
					this.view.showCurPage();
				}
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				his.view.hideLoading();
			}.bind(this),
		});
	},

	getAlarmPages: function (sender, args) {
		this.stopWatchStatus();
		this.view.showLoading();

		var json = { page: args.page, step: 100 };
		var json_str = JSON.stringify(json);
		$.ajax({
			type: "POST",
			url: "/alarm_log",
			data: json_str,
			dataType: "json",
			success: function (data, status) {
				var args = {};
				if (data.hasOwnProperty("totalPages")) {
					args.page = data.totalPages;
					this.view.alarmLogPage = data.totalPages;
					this.view.alarmLogTotal = data.totalPages;
					this.view.showAlarmListPage.notify(args);
					this.view.showCurPage();
				}
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.hideLoading();
			}.bind(this),
		});
	},

	showAlarmListPage: function (sender, args) {
		console.log("showAlarmLog");
		this.stopWatchStatus();
		this.view.showLoading();

		var json = { page: args.page, step: 100 };
		var json_str = JSON.stringify(json);
		$.ajax({
			type: "POST",
			url: "/alarm_log",
			data: json_str,
			dataType: "text",
			success: function (data, status) {
				this.view.refreshAlarmList(data).hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.hideLoading();
			}.bind(this),
		});
	},

	requestPlayback: function (sender, args) {
		console.log("deleteEvent:" + JSON.stringify(args));
		function S4() {
			return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
		}
		var json = $.extend(
			{
				captureEventUUID:
					S4() +
					S4() +
					"-" +
					S4() +
					"-" +
					S4() +
					"-" +
					S4() +
					"-" +
					S4() +
					S4() +
					S4(),
				playback: true,
			},
			args
		);
		if (this.querying) {
			return;
		}
		this.querying = true;
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/clip",
			data: JSON.stringify(json),
			success: function (data, status) {
				this.view.showPlaybackSuccess();
				this.view.hideLoading();
				this.querying = false;
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showPlaybackError(JSON.parse(XMLHttpRequest.responseText));
				this.view.hideLoading();
				this.querying = false;
			}.bind(this),
			dataType: "json",
		});
	},

	deleteEvent: function (sender, args) {
		console.log("deleteEvent:" + JSON.stringify(args));
		var json = {
			command: "deleteVideoFile",
			type: args.clipStat,
			startTime: args.startTime,
			us: args.us,
			pos: args.pos,
		};
		this.view.showLoading();
		$.ajax({
			type: "POST",
			url: "/test",
			data: JSON.stringify(json),
			success: function (data, status) {
				this.model.delEventFromList(args);
				this.view.refreshEventList(this.model.getEventListJson());
				this.view.hideLoading();
			}.bind(this),
			error: function (XMLHttpRequest) {
				this.view.showErrorInfo(
					"delete-event-fail",
					JSON.parse(XMLHttpRequest.responseText)
				);
				this.view.hideLoading();
			}.bind(this),
			dataType: "json",
		});
	},
};
