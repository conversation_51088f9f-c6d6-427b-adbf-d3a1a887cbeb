/**
 * 登录模块 - 覆盖login.html中的原始登录功能
 * 实现不同用户角色登录和权限控制
 */
(function() {
    // 定义用户配置
    const userConfig = {
        // 管理员 - 拥有全部权限
        admin: {
            permissions: "all",
            customerType: "all",
        },
        // 子管理员 - 有部分限制
        Intg: {
            permissions: "all",
            customerType: "all",
            restrictedElements: [
                // Audio相关设置 - 被隐藏
                "algConfig-pd-audioType", // 音频类型选择框
                ".alg-audio-conf", // 整个音频配置区域
                
                // PD模型相关设置 - 被隐藏
                "algConfig-pd-pdsModel", // 检测模型选择框
                "algConfig-pd-pdSensitivity", // PD Sensitivity设置
                
                // Alg灵敏度设置 - 被隐藏
                "[id$='-sensitivity']", // 所有以-sensitivity结尾的元素
                "[data-desc*='sensitivity']", // 所有data-desc属性包含sensitivity的元素
                
                // 其他可能需要隐藏的元素
                "mediaConfig-showGuiMask-0", // mainstream相关设置
                
                // Algorithm Advanced Settings中的Sensitivity相关设置 - 被隐藏
                "[data-desc='RedSensitivity']", // 红色灵敏度标签
                "[data-desc='pdYellowSensitivity']", // 黄色灵敏度标签
                "[data-desc='pdGreenSensitivity']", // 绿色灵敏度标签
                "algConfig-pd-pdRedSensitivity-0", // 红色灵敏度值1
                "algConfig-pd-pdRedSensitivity-1", // 红色灵敏度值2
                "algConfig-pd-pdRedSensitivity-2", // 红色灵敏度值3
                "algConfig-pd-pdYellowSensitivity-0", // 黄色灵敏度值1
                "algConfig-pd-pdYellowSensitivity-1", // 黄色灵敏度值2
                "algConfig-pd-pdYellowSensitivity-2", // 黄色灵敏度值3
                "algConfig-pd-pdGreenSensitivity-0", // 绿色灵敏度值1
                "algConfig-pd-pdGreenSensitivity-1", // 绿色灵敏度值2
                "algConfig-pd-pdGreenSensitivity-2", // 绿色灵敏度值3
                ".input-text-box:has([data-desc='RedSensitivity'])", // 包含红色灵敏度的整个输入框容器
                ".input-text-box:has([data-desc='pdYellowSensitivity'])", // 包含黄色灵敏度的整个输入框容器
                ".input-text-box:has([data-desc='pdGreenSensitivity'])", // 包含绿色灵敏度的整个输入框容器
                "div:has(.input-text-box:has([data-desc='RedSensitivity']))", // 包含所有灵敏度设置的外层容器
                
                // GUI绘制模式相关设置 - 被隐藏
                "algConfig-pd-detectPart", // GUI绘制模式选择框
                "[data-desc='pdDetectPart']", // GUI绘制模式标签
                ".custom-select-box:has(#algConfig-pd-detectPart)" // 包含GUI绘制模式的整个选择框容器
            ]
        },
        Install: {
            permissions: "all",
            customerType: "all",
            restrictedElements: [
                "#sel-ch3", // 隐藏图片流选项卡

                // Audio相关设置 - 被隐藏
                "algConfig-pd-audioType", // 音频类型选择框
                ".alg-audio-conf", // 整个音频配置区域
                
                // PD模型相关设置 - 被隐藏
                "algConfig-pd-pdsModel", // 检测模型选择框
                "algConfig-pd-pdSensitivity", // PD Sensitivity设置
                
                // Alg灵敏度设置 - 被隐藏
                "[id$='-sensitivity']", // 所有以-sensitivity结尾的元素
                "[data-desc*='sensitivity']", // 所有data-desc属性包含sensitivity的元素
                
                // MainStream相关设置 - 被隐藏
                "mediaConfig-showGuiMask-0", // mainstream相关设置
                "mediaConfig-mainBitrate", // 主码流比特率
                "[data-desc='main-bitrate']", // 主码流比特率标签
                "mediaConfig-mainRcMode", // 主码流码率控制模式
                "[data-desc='main-rc-mode']", // 主码流码率控制模式标签
                "mediaConfig-mainIFrameInterval", // 主码流I帧间隔
                "mediaConfig-mainIframeInt", // 主码流I帧间隔（正确的ID）
                "[data-desc='ifrm-interval']", // I帧间隔标签
                "[data-desc='main-iframe-interval']", // 主码流I帧间隔标签
                
                // SubStream相关设置 - 被隐藏
                "mediaConfig-subBitrate", // 子码流比特率
                "[data-desc='sub-bitrate']", // 子码流比特率标签
                "mediaConfig-subRcMode", // 子码流码率控制模式
                "[data-desc='sub-rc-mode']", // 子码流码率控制模式标签
                "mediaConfig-subIFrameInterval", // 子码流I帧间隔
                "mediaConfig-subIframeInt", // 子码流I帧间隔（正确的ID）
                "[data-desc='sub-iframe-interval']", // 子码流I帧间隔标签
                
                // Alg PD相关设置 - 被隐藏
                "algConfig-pd-pdTestMode", // PD测试模式
                "[data-desc='pd-test-mode']", // PD测试模式标签
                "algConfig-pd-pdRectPerson", // 人体矩形标记
                "[data-desc='display-pdRectPerson']", // 人体矩形标记标签
                ".input-switch-box:has(#algConfig-pd-pdTestMode)", // 包含PD测试模式的整个开关容器
                ".input-switch-box:has(#algConfig-pd-pdRectPerson)", // 包含人体矩形标记的整个开关容器
                ".input-switch-box p[data-desc='pd-test-mode']", // 更精确的测试模式标签选择器
                ".input-switch-box p[data-desc='display-pdRectPerson']", // 更精确的行人检测框标签选择器
                ".input-switch-box:has(p[data-desc='pd-test-mode'])", // 更精确的测试模式容器选择器
                ".input-switch-box:has(p[data-desc='display-pdRectPerson'])", // 更精确的行人检测框容器选择器
                
                // Alg Alarm相关设置 - 被隐藏
                "algConfig-pd-pdInterval-red", // 红色警报间隔
                "[data-desc*='red-alarm-interval']", // 红色警报间隔标签
                "algConfig-pd-pdInterval-yellow", // 黄色警报间隔
                "[data-desc*='yellow-alarm-interval']", // 黄色警报间隔标签
                "algConfig-pd-pdInterval-green", // 绿色警报间隔
                "[data-desc*='green-alarm-interval']", // 绿色警报间隔标签
                "algConfig-pd-pdAlarmOutInterval", // 警报输出持续时间
                "#pdalarm_interval_value", // 警报输出持续时间显示值
                "[data-desc*='alarm-out-duration']", // 警报输出持续时间标签
                ".rangeinput:has(#algConfig-pd-pdAlarmOutInterval)", // 包含警报输出持续时间的整个输入容器
                ".rangeinput_title:contains('pdAlarmOut-Interval')", // 警报输出间隔标题
                "pdalarmout-auto-enable", // 警报输出Auto开关
                ".config_checkbox2:has(#pdalarmout-auto-enable)", // 警报输出Auto开关容器
                ".config_checkbox2", // 所有AUTO开关容器
                "[data-desc='auto']", // AUTO标签
                "label[for='pdalarmout-auto-enable']", // AUTO开关标签
                
                // Algorithm Advanced Settings中的Sensitivity相关设置 - 被隐藏
                "[data-desc='RedSensitivity']", // 红色灵敏度标签
                "[data-desc='pdYellowSensitivity']", // 黄色灵敏度标签
                "[data-desc='pdGreenSensitivity']", // 绿色灵敏度标签
                "algConfig-pd-pdRedSensitivity-0", // 红色灵敏度值1
                "algConfig-pd-pdRedSensitivity-1", // 红色灵敏度值2
                "algConfig-pd-pdRedSensitivity-2", // 红色灵敏度值3
                "algConfig-pd-pdYellowSensitivity-0", // 黄色灵敏度值1
                "algConfig-pd-pdYellowSensitivity-1", // 黄色灵敏度值2
                "algConfig-pd-pdYellowSensitivity-2", // 黄色灵敏度值3
                "algConfig-pd-pdGreenSensitivity-0", // 绿色灵敏度值1
                "algConfig-pd-pdGreenSensitivity-1", // 绿色灵敏度值2
                "algConfig-pd-pdGreenSensitivity-2", // 绿色灵敏度值3
                ".input-text-box:has([data-desc='RedSensitivity'])", // 包含红色灵敏度的整个输入框容器
                ".input-text-box:has([data-desc='pdYellowSensitivity'])", // 包含黄色灵敏度的整个输入框容器
                ".input-text-box:has([data-desc='pdGreenSensitivity'])", // 包含绿色灵敏度的整个输入框容器
                "div:has(.input-text-box:has([data-desc='RedSensitivity']))", // 包含所有灵敏度设置的外层容器
                
                // GUI绘制模式相关设置 - 被隐藏
                "algConfig-pd-detectPart", // GUI绘制模式选择框
                "[data-desc='pdDetectPart']", // GUI绘制模式标签
                ".custom-select-box:has(#algConfig-pd-detectPart)", // 包含GUI绘制模式的整个选择框容器
                
                // Network相关设置 - 被隐藏
                "#networkConfig", // 网络配置选项卡
                "[data-desc='network-conf']", // 网络配置标签
                ".network-button", // 网络按钮
                "[href='#networkConfig']", // 网络配置标签页链接
                ".configmenu:has(h1:contains('ethernet'))", // 包含ethernet标题的配置菜单
                ".configmenu:has(p:contains('DHCP'))", // 包含DHCP文本的配置菜单
                "networkConfig-ethernet-enableDHCP", // DHCP开关
                "networkConfig-ethernet-dhcpTimeout", // DHCP超时
                "networkConfig-ethernet-ipAddress", // IP地址
                "networkConfig-ethernet-subnetMask", // 子网掩码
                "networkConfig-ethernet-gateway", // 网关
                "[data-desc='dhcp']", // DHCP标签
                "[data-desc='dhcp-timeout']", // DHCP超时标签
                "[data-desc='ipaddr']", // IP地址标签
                "[data-desc='subnet-mask']", // 子网掩码标签
                "[data-desc='gateway']", // 网关标签
                ".input-text-box:has(#networkConfig-ethernet-ipAddress)", // 包含IP地址的输入框容器
                ".input-text-box:has(#networkConfig-ethernet-subnetMask)", // 包含子网掩码的输入框容器
                ".input-text-box:has(#networkConfig-ethernet-gateway)", // 包含网关的输入框容器
                ".input-text-box:has(#networkConfig-ethernet-dhcpTimeout)", // 包含DHCP超时的输入框容器
                ".config_checkbox:has(#networkConfig-ethernet-enableDHCP)", // 包含DHCP开关的复选框容器
            ]
        },
        // 客户类型1用户
        customer1: {
            password: "customer1",
            permissions: "all",
            customerType: "202018", // 特定客户类型
            restrictedElements: [
                // 限制部分功能
                "FCT-shelterEnable" // 遮挡报警开关
            ]
        },
        // 客户类型2用户
        customer2: { 
            password: "customer2",
            permissions: ["view", "query"],
            customerType: "201623" // 特定客户类型
        }
    };

    // 确保scustomer有值
    var scustomer = window.localStorage.getItem(window.location.host + "-scustomer");
    if(scustomer === null || scustomer === undefined || scustomer === "") {
        // 如果scustomer为空或null，通过AJAX请求获取
        $.ajax({
            type: "GET",
            url: "/config",
            async: false, // 同步请求确保在继续执行前获取到值
            success: function(data) {
                if(data.hasOwnProperty("ipcIdentification") && 
                   data.ipcIdentification.hasOwnProperty("scustomer")) {
                    scustomer = data.ipcIdentification.scustomer;
                    // 保存到localStorage
                    window.localStorage.setItem(window.location.host + "-scustomer", scustomer);
                    console.log("成功获取客户类型:", scustomer);
                } else {
                    // 设置默认值
                    scustomer = "default";
                    window.localStorage.setItem(window.location.host + "-scustomer", scustomer);
                }
            },
            error: function() {
                // 请求失败时使用默认值
                scustomer = "default";
                window.localStorage.setItem(window.location.host + "-scustomer", scustomer);
            }
        });
    }
    console.log("当前客户类型:", scustomer);

    if(scustomer == "200001") {
    // 等待DOM加载完成
    $(document).ready(function() {
        // 清除保存的密码
        var itemPassword = window.location.host + "-pwd";
        var itemRemember = window.location.host + "-rem";
        
        // 每次页面加载时清除密码和用户名，除非用户明确选择了"记住我"
        var itemUsername = window.location.host + "-usr";
        if (localStorage.getItem(itemRemember) !== "true") {
            localStorage.removeItem(itemPassword);
            localStorage.removeItem(itemUsername);
        } else {
            // 如果用户选择了"记住我"，则恢复用户名
            // 使用延时确保DOM元素已经创建
            setTimeout(function() {
                var usernameElement = document.getElementById("username");
                if (usernameElement && !usernameElement.readOnly) {
                    // 只有在元素存在且不是只读的情况下才恢复用户名
                    var savedUsername = localStorage.getItem(itemUsername);
                    if (savedUsername) {
                        usernameElement.value = savedUsername;
                    }
                }
            }, 100);
        }
        
        // 覆盖原始登录函数
        if (typeof window.myLogin !== 'undefined') {
            // 重写登录函数
            window.myLogin.OnSubmit = function() {
                const $username = $("#username");
                const $password = $("#password");
                const $showError = $("#show-error");

                // 验证输入是否为空
                if (!$username.val() || !$password.val()) {
                    $showError
                        .text("用户名和密码不能为空")
                        .shake(2, 10, 400);
                    return false;
                }
                
                // 准备发送到后端验证的数据
                var json = { usr: $username.val(), pwd: $password.val() };
                var json_str = JSON.stringify(json);
                
                // 显示加载动画
                $.mobile.loading("show");
                
                // 发送到后端验证
                $.ajax({
                    type: "POST",
                    url: "login",
                    data: json_str + "\r\n" + "pwd=" + $password.val(),
                    success: function (data, status) {
                        $.mobile.loading("hide");
                        
                        // 密码验证成功后，根据本地配置应用权限控制
                        // 注意：这里只是用来控制界面元素的显示/隐藏，不再用于密码验证
                        
                        // 首先清除之前的权限状态
                        if (typeof clearAllRestrictedStates === 'function') {
                            clearAllRestrictedStates();
                        }
                        
                        if (userConfig.hasOwnProperty($username.val())) {
                            const user = userConfig[$username.val()];
                            
                            // 保存用户信息到本地存储，用于权限控制
                            const userInfo = {
                                username: $username.val(),
                                permissions: user.permissions,
                                customerType: user.customerType,
                                restrictedElements: user.restrictedElements || []
                            };
                            localStorage.setItem('currentUser', JSON.stringify(userInfo));
                            
                            // 保存客户类型
                            if (user.customerType && user.customerType !== "all") {
                                localStorage.setItem(window.location.host + "-customer", user.customerType);
                            } else {
                                localStorage.removeItem(window.location.host + "-customer");
                            }
                            
                            // 提取要隐藏的元素ID，保存到 localStorage
                            if (user.restrictedElements && user.restrictedElements.length > 0) {
                                // 提取所有ID选择器（以#开头的选择器）并去除#号
                                const elementIds = user.restrictedElements
                                    .filter(selector => selector.startsWith('#'))
                                    .map(selector => selector.substring(1)); // 移除#前缀
                                
                                if (elementIds.length > 0) {
                                    localStorage.setItem('hiddenElements', JSON.stringify(elementIds));
                                    console.log("已保存要隐藏的元素:", elementIds);
                                }
                            }
                        } else {
                            // 用户存在于后端但不在前端配置中，使用默认权限
                            const userInfo = {
                                username: $username.val(),
                                permissions: "all",
                                customerType: "all",
                                restrictedElements: []
                            };
                            localStorage.setItem('currentUser', JSON.stringify(userInfo));
                            localStorage.removeItem(window.location.host + "-customer");
                        }
                        
                        // 处理"记住我"选项
                        var itemPassword = window.location.host + "-pwd";
                        var itemRemember = window.location.host + "-rem";
                        var itemUsername = window.location.host + "-usr";
                        
                        // 无论如何，先清除之前可能存在的密码和用户名
                        localStorage.removeItem(itemPassword);
                        localStorage.removeItem(itemUsername);
                        
                        if ($("#remember").is(":checked")) {
                            // 只有在明确勾选"记住我"时才保存密码和用户名
                            localStorage.setItem(itemPassword, $password.val());
                            // 只有在用户名不是只读的情况下才保存用户名
                            var usernameElement = document.getElementById("username");
                            if (usernameElement && !usernameElement.readOnly) {
                                localStorage.setItem(itemUsername, $username.val());
                            }
                            localStorage.setItem(itemRemember, true);
                        } else {
                            // 如果没有勾选"记住我"，确保清除所有相关信息
                            localStorage.removeItem(itemRemember);
                            localStorage.setItem(itemRemember, false);
                        }
                        
                        // 登录成功后跳转
                        window.location.href = "index.html";
                    },
                    error: function (XMLHttpRequest) {
                        // 登录失败
                        $.mobile.loading("hide");
                        $showError
                            .text("用户名或密码错误")
                            .shake(2, 10, 400);
                    },
                    dataType: "json",
                });
                
                return true;
            };
        }

    
        // 添加用于隐藏元素的函数 - 无论哪种登录方式都需要
        window.hideRestrictedElements = function() {
            // 获取当前登录用户
            try {
                const userInfoStr = localStorage.getItem('currentUser');
                if (!userInfoStr) {
                    console.log("未找到用户登录信息");
                    return;
                }
                
                const userInfo = JSON.parse(userInfoStr);
                console.log("应用权限控制，用户:", userInfo.username);
                
                // 首先恢复所有可能被隐藏的元素 - 确保状态清除
                const allPossibleElements = [
                    "#sel-ch3", "#networkConfig", "#mediaConfig-voFormat",
                    ".custom-select-box:has(#mediaConfig-voFormat)"
                ];
                
                allPossibleElements.forEach(function(selector) {
                    try {
                        $(selector).show();
                    } catch (e) {
                        // 忽略选择器错误
                    }
                });
                
                // 根据客户类型控制AHD Stream的Video Format显示
                const scustomer = window.localStorage.getItem(window.location.host + "-scustomer");
                if (scustomer !== "200001") {
                    // 非200001客户隐藏AHD Stream的Video Format
                    $("#mediaConfig-voFormat").hide();
                    $(".custom-select-box:has(#mediaConfig-voFormat)").hide();
                } else {
                    // 200001客户显示AHD Stream的Video Format
                    $("#mediaConfig-voFormat").show();
                    $(".custom-select-box:has(#mediaConfig-voFormat)").show();
                }
                
                // 应用用户特定的元素限制
                if (userInfo.restrictedElements && userInfo.restrictedElements.length > 0) {
                    userInfo.restrictedElements.forEach(function(selector) {
                        try {
                            // 处理CSS选择器
                            if (selector.startsWith('.') || 
                                selector.startsWith('#') || 
                                selector.startsWith('[')) {
                                // 直接使用选择器
                                $(selector).hide();
                                console.log("隐藏元素:", selector);
                            } else {
                                // 假定为ID
                                const $element = $('#' + selector);
                                if ($element.length) {
                                    // 查找父元素，提高隐藏效果
                                    const $container = $element.closest('.custom-select-box');
                                    if ($container.length) {
                                        $container.hide();
                                        console.log("隐藏元素容器:", selector);
                                    } else {
                                        $element.hide();
                                        console.log("隐藏元素:", selector);
                                    }
                                }
                            }
                        } catch (selectorError) {
                            console.error("处理选择器时出错:", selector, selectorError);
                        }
                    });
                    
                    // 为Install用户添加网络配置点击事件
                    if (userInfo.username === 'Install' && 
                        userInfo.restrictedElements.includes("#networkConfig")) {
                        // 绑定点击事件，允许点击但立即隐藏内容
                        $("[data-goto-id='networkConfig'], #network").off('click.hideNetwork').on('click.hideNetwork', function(e) {
                            // 让原始点击事件执行
                            setTimeout(function() {
                                // 然后立即隐藏网络配置面板
                                $("#networkConfig").hide();
                            }, 0);
                        });
                    }
                }
            } catch (error) {
                console.error("隐藏元素时出错:", error);
            }
        };
        
        // 添加清除所有权限状态的函数
        window.clearAllRestrictedStates = function() {
            console.log("清除所有权限状态");
            
            // 显示所有可能被隐藏的元素（除了需要根据客户类型控制的元素）
            const allElements = [
                "#sel-ch3", "#networkConfig"
            ];
            
            allElements.forEach(function(selector) {
                try {
                    $(selector).show();
                } catch (e) {
                    // 忽略选择器错误
                }
            });
            
            // 根据客户类型控制AHD Stream Video Format显示
            const scustomer = window.localStorage.getItem(window.location.host + "-scustomer");
            if (scustomer === "200001") {
                $("#mediaConfig-voFormat").show();
                $(".custom-select-box:has(#mediaConfig-voFormat)").show();
            } else {
                $("#mediaConfig-voFormat").hide();
                $(".custom-select-box:has(#mediaConfig-voFormat)").hide();
            }
            
            // 移除Install用户的特殊事件监听
            $("[data-goto-id='networkConfig'], #network").off('click.hideNetwork');
            
            // 清除localStorage中的隐藏元素记录
            localStorage.removeItem('hiddenElements');
        };
        
        // 在配置页面加载完成后执行权限控制
        if (window.location.pathname.includes('config.html')) {
            $(document).on('pagecreate', function() {
                setTimeout(function() {
                    // 先清除状态，再应用权限控制
                    clearAllRestrictedStates();
                    setTimeout(hideRestrictedElements, 100);
                }, 500);
            });
        }
    });
    
    // 监听页面加载完成事件
    $(window).on('load', function() {
        // 适用于index.html等页面
        if (typeof hideRestrictedElements === 'function') {
            setTimeout(hideRestrictedElements, 1000);
            
            // 在权限控制执行后，显示tab-chn-3元素
            setTimeout(function() {
                var tabChn3 = document.getElementById('tab-chn-3');
                if (tabChn3) {
                    tabChn3.style.display = 'block';
                }
            }, 500);
        }
    });
    }
    else {
        // 当scustomer不等于"200001"时的处理，只隐藏Video Format，不隐藏整个tab-chn-3
        document.write('<style>.custom-select-box:has(#mediaConfig-voFormat){display:none !important;}</style>');
    }
})();
