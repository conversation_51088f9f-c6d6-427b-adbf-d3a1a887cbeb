/**
 * Created by <PERSON> on 2017/9/26.
 */
/**用于对不同界面的发送json消息的区分*/
var toggle=1;
var WIFIstate;
/*解决一开始就是wifi的情况下sta不显示的问题*/



function sleep(ms) {
    var now = new Date();
    var exitTime = now.getTime() + ms;
    while (true) {
        now = new Date();
        if (now.getTime() > exitTime)
        return;
        }
    }
function handleImportConfig(evt) 
{
    var files = evt.target.files;
    for (var i = 0, f; f = files[i]; i++) 
    {
        var name = f.name;//读取选中文件的文件名
        var size = f.size;//读取选中文件的大小
        if(name == "config.tar.gz")
        {
        console.log("文件名:"+name+"大小:"+size);		
        var reader = new FileReader();
        reader.onload = (function (theFile) {
            return function (e) {
                var bindata = e.target.result;
                var bytedata = new Uint8Array(bindata);
                var head = "\r\n------WebKitFormBoundaryXrjvwAVbcGXiqJOi\r\nContent-Disposition: form-data; name=\"file\"; filename=\""+name+"\"\r\nContent-Type: application/octet-stream\r\n\r\n";
            
                var tail = "\r\n------WebKitFormBoundaryXrjvwAVbcGXiqJOi--\r\n";
                var len = head.length + size + tail.length;
                var byteArr = new Uint8Array(len);
                for(var i=0;i<head.length;i++)
                {
                    byteArr[i] = head[i].charCodeAt(0);
                }
                for(var i=0;i<size;i++)
                {
                    byteArr[head.length+i] = bytedata[i];
                }
                for(var i=0;i<tail.length;i++)
                {
                    byteArr[head.length+size+i] = tail[i].charCodeAt(0);
                }
                    
                var xhr = new XMLHttpRequest();
                var protocol = window.location.protocol;
                var host = window.location.host;
                var url = protocol + "//" + host + "/import_config";
　　				    xhr.open("POST", url);
                xhr.setRequestHeader("Content-Type","multipart/form-data; boundary=----WebKitFormBoundaryXrjvwAVbcGXiqJOi"); 	
                xhr.send(byteArr);
                this.showLoading();
                xhr.onload = function () {
                    //如果请求成功
                    if((xhr.status >= 200 && xhr.status < 300) || xhr.status == 304){
                        this.hideLoading();
                        console.log("Import Config Success.");
                        var lang;
                        var itemLang = window.location.host+"-lang";
                        window.localStorage && (lang = window.localStorage.getItem(itemLang));
                        $("#popup-show-info").find("h1").text(this.model.getKeyLang("success",lang));
                        $("#popup-show-info").find("h3").text(this.model.getKeyLang("ImportSuccess",lang));
                        $("#popup-show-info").find("p").text("");
                        $("#show-info").trigger("click");
                    }
                  }.bind(this)                
            }.bind(this);
        }.bind(this))(f);
        reader.readAsArrayBuffer(f);
        }
        else
        {
            var lang;
            var itemLang = window.location.host+"-lang";
            window.localStorage && (lang = window.localStorage.getItem(itemLang));
            $("#popup-show-info").find("h1").text(this.model.getKeyLang("failure",lang));
            $("#popup-show-info").find("h3").text(this.model.getKeyLang("ConfigFile_Invalid",lang));
            $("#popup-show-info").find("p").text("");
            $("#show-info").trigger("click");
        }
    }
}
    
function handleImportFaceId(evt){
    var files = evt.target.files;
    for (var i = 0, f; f = files[i]; i++) 
    {
        var name = f.name;//读取选中文件的文件名
        var size = f.size;//读取选中文件的大小
        if(name == "FaceId.tar.gz")
        {
        console.log("文件名:"+name+"大小:"+size);		
        var reader = new FileReader();
        reader.onload = (function (theFile) {
            return function (e) {
                var bindata = e.target.result;
                var bytedata = new Uint8Array(bindata);
                var head = "\r\n------WebKitFormBoundaryXrjvwAVbcGXiqJOi\r\nContent-Disposition: form-data; name=\"file\"; filename=\""+name+"\"\r\nContent-Type: application/octet-stream\r\n\r\n";
                var tail = "\r\n------WebKitFormBoundaryXrjvwAVbcGXiqJOi--\r\n";
                var len = head.length + size + tail.length;
                var byteArr = new Uint8Array(len);
                for(var i=0;i<head.length;i++)
                {
                    byteArr[i] = head[i].charCodeAt(0);
                }
                
                for(var i=0;i<size;i++)
                {
                    byteArr[head.length+i] = bytedata[i];
                }
                for(var i=0;i<tail.length;i++)
                {
                    byteArr[head.length+size+i] = tail[i].charCodeAt(0);
                }
                    
                var xhr = new XMLHttpRequest();
                var protocol = window.location.protocol;
                var host = window.location.host;
                var url = protocol + "//" + host + "/import_FaceID";
　　				    xhr.open("POST", url);
                console.log(url);
                xhr.setRequestHeader("Content-Type","multipart/form-data; boundary=----WebKitFormBoundaryXrjvwAVbcGXiqJOi"); 	
                xhr.send(byteArr);
                this.showLoading();
                xhr.onreadystatechange = function(){
                    //若响应完成且请求成功
                    if(xhr.readyState === 4 && xhr.status === 200){
                        this.hideLoading();                     
                        var lang;
                        var itemLang = window.location.host+"-lang";                       
                        window.localStorage && (lang = window.localStorage.getItem(itemLang));    
                        console.log("Import FaceID Success."+lang);                    
                        $("#popup-show-info").find("h1").text(this.model.getKeyLang("success",lang));
                        $("#popup-show-info").find("h3").text(this.model.getKeyLang("ImportSuccess",lang));
                        $("#popup-show-info").find("p").text("");
                        $("#show-info").trigger("click");         
                        this.registerEvent.notify({"command":"getUsers"});
                    }
                }.bind(this)          
            }.bind(this);
        }.bind(this))(f);
        reader.readAsArrayBuffer(f);
        }
        else
        {
            var lang;
            var itemLang = window.location.host+"-lang";
            window.localStorage && (lang = window.localStorage.getItem(itemLang));
            $("#popup-show-info").find("h1").text(this.model.getKeyLang("failure",lang));
            $("#popup-show-info").find("h3").text(this.model.getKeyLang("UserFile_Invalid",lang));
            $("#popup-show-info").find("p").text("");
            $("#show-info").trigger("click");
        }
    }
}

	function findStrTailPos(byteArray,strIndex)
	{
		var findpos = 0;
		for (var i = 0; i < byteArray.byteLength; i++) 
		{
			var find = 1;		
			for(var j=0;j<strIndex.length;j++)
			{
				if(strIndex[j].charCodeAt(0) != byteArray[i+j])
				{
					find = 0;
					break;
				}
			}
			if(find)
			{	
				findpos = i+strIndex.length;
				break;
			}
		}
		console.log(findpos);
		return findpos;
	}
	
	function findStrHeaderPos(byteArray,strIndex)
	{
		var findpos = 0;
		for (var i = 0; i < byteArray.byteLength; i++) 
		{
			var find = 1;
			for(var j=0;j<strIndex.length;j++)
			{
				if(strIndex[j].charCodeAt(0) != byteArray[i+j])
				{
					find = 0;
					break;
				}
			}
			if(find)
			{
				findpos = i;
				break;
			}
		}
		console.log(findpos);
		return findpos;
	}
	
	
	function handlereadystatechange(xhr,filename)
	{
		if (xhr.readyState==4)
		{
			if (xhr.status==200)
			{
				var index1 = "Content-Type: application/octet-stream\r\n\r\n";
				var index2 = "\r\n------ZnGpDtePMx0KrHh_G0X99Yef9r8JZsRJSXC";
				var pos1 = 0;
				var pos2 = 0;
				var arrayBuffer = xhr.response;
				if (arrayBuffer) 
				{
					var byteArray = new Uint8Array(arrayBuffer);
					pos1 = findStrTailPos(byteArray,index1);
					pos2 = findStrHeaderPos(byteArray,index2);
					var len = pos2-pos1;
					console.log(len);
					if(len > 0)
					{
						var byteArr = new Uint8Array(len);
						for (var i = 0; i < byteArr.byteLength; i++) {
							byteArr[i] = byteArray[pos1+i];
						}
						var blob = new Blob([byteArr], {type: "text/plain;"});
						saveAs(blob, filename);
						if(filename == "config.tar.gz")
						{
							console.log("Export Config Success.");
						}else if(filename == "FaceId.tar.gz")
						{
							console.log("Export FaceId Success.");
						}
						else if(filename == "log.tar.gz")
						{
							console.log("Export Log File Success.");
						}
					}
				}
			}
		}
	}

	function handleXHR(url,filename)
	{
		var xhr = new XMLHttpRequest();
		xhr.onreadystatechange=function(){
			handlereadystatechange(xhr,filename);
		};
		xhr.open("POST", url);
		xhr.responseType = "arraybuffer";
		xhr.setRequestHeader("Content-Type","multipart/form-data"); 
		xhr.send("");	 
	}
	
	function handleExportConfig(evt) 
	{
        //var Ipaddr = $("networkConfig-ethernet-ipAddress");
        //var Ipaddr = document.getElementById("networkConfig-ethernet-ipAddress");
        //var $Ipaddr = $("#networkConfig-ethernet-ipAddress");
        //console.log("Ipaddr:"+$Ipaddr);
        var protocol = window.location.protocol;
        var host = window.location.host;
        //alert(host);
        var url = protocol + "//" + host + "/export_config";
        console.log("url:"+url);
        //var url = "http://**************:8080/export_config";
		var filename = "config.tar.gz";
		handleXHR(url,filename);
    }
    
    function handleExportFaceId(evt){
        var protocol = window.location.protocol;
        var host = window.location.host;
        var url = protocol + "//" + host + "/export_FaceID";
        console.log("url:"+url);
		var filename = "FaceId.tar.gz";
		handleXHR(url,filename);
    }
	function handleExportLogFile(evt) 
	{
        var protocol = window.location.protocol;
        var host = window.location.host;
        var url = protocol + "//" + host + "/export_logfile";
        console.log(url);
		var filename = "log.tar.gz";
		handleXHR(url,filename);
    }
//下拉刷新
var DownScroll = function (view, scrollId, callback) {
    this.view = view;
    this.$scrollDom = $("#"+scrollId);
    this.callback = callback;
    this.isDownScrolling = false;
    this.init();
};

DownScroll.prototype = {
    init: function () {
        var isIOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
        this.startTop = null;
        this.offset = 60;
        this.outOffsetRate = 0.2;
        this.minAngle = 45;
        this.mustToTop = !isIOS;
        this.hardwareClass = "scroll-hardware";
        this.warpClass = "scroll-downwarp";
        this.resetClass = "scroll-downwarp-reset";
        this.htmlContent = '<div class="scroll-downwarp"><div class="downwarp-content"><p class="downwarp-progress"></p><p class="downwarp-tip">'+this.view.model.getKeyLang("down-scroll-refresh", this.view.lang)+'</p></div></div>';
        $("#paeg-header").after(this.htmlContent);
        this.$downwarp = $(".scroll-downwarp");
        this.$downProgressDom = $(".downwarp-progress");
        this.$downTipDom = $(".downwarp-tip");
        this.enable();

        return this;
    },
    enable: function () {
        this.$scrollDom.mousedown(this.touchStartEvent.bind(this));
        this.$scrollDom.mousemove(this.touchMoveEvent.bind(this));
        this.$scrollDom.mouseup(this.touchEndEvent.bind(this));
        this.$scrollDom.mouseleave(this.touchEndEvent.bind(this));
        this.$scrollDom.on("touchstart", this.touchStartEvent.bind(this));
        this.$scrollDom.on("touchmove", this.touchMoveEvent.bind(this));
        this.$scrollDom.on("touchend", this.touchEndEvent.bind(this));
        return this;
    },
    setCallback: function (callback) {
        this.callback = callback;
    },
    touchStartEvent: function (e) {
        if (this.isDownScrolling) return;
        this.startTop = $(document).scrollTop();
        if (this.mustToTop) {
            this.startY = e.touches ? e.touches[0].pageY : e.originalEvent.targetTouches ? e.originalEvent.targetTouches[0].pageY : e.clientY;
        }
    },
    touchMoveEvent: function (e) {
        var curX = e.touches ? e.touches[0].pageX : e.originalEvent.targetTouches ? e.originalEvent.targetTouches[0].pageX : e.clientX;
        var curY = e.touches ? e.touches[0].pageY : e.originalEvent.targetTouches ? e.originalEvent.targetTouches[0].pageY : e.clientY;
        if (this.isDownScrolling || this.startTop == null || (this.mustToTop && this.startTop > 0)) return;
        !this.preX && (this.preX = curX);
        !this.preY && (this.preY = curY);
        var dx = Math.abs(this.preX - curX);
        var dy = Math.abs(this.preY - curY);
        var dz = Math.sqrt(dx*dx + dy*dy);
        if (dz != 0) {
            var angle = Math.asin(dy / dz) / Math.PI*180;
            if (angle < this.minAngle) return;
        }
        var diff = curY - this.preY;
        this.preX = curX;
        this.preY = curY;
        (!this.startY && !this.mustToTop) && (this.startY = curY);
        var moveY = curY - this.startY;
        if(moveY > 0) {
            e.preventDefault();
            !this.downHight && (this.downHight = 0);
            if (this.downHight < this.offset) {
                this.downHight += diff;
                this.$downTipDom.text(this.view.model.getKeyLang("down-scroll-refresh", this.view.lang));
                this.$downwarp.removeClass(this.resetClass);
                this.$scrollDom.addClass(this.hardwareClass);
                this.$scrollDom.css("webkitOverflowScrolling", "auto");
            } else {
                this.isMoveDown = true;
                if (diff > 0) {
                    this.downHight += diff * this.outOffsetRate;
                } else {
                    this.downHight += diff;
                }
                this.$downTipDom.text(this.view.model.getKeyLang("release-update", this.view.lang));
                this.$downwarp.removeClass(this.resetClass);
                this.$scrollDom.addClass(this.hardwareClass);
                this.$scrollDom.css("webkitOverflowScrolling", "auto");
            }
            this.$downwarp.css("height", this.downHight+"px");
            var rate = this.downHight / this.offset;
            var progress = 360 * rate;
            this.$downProgressDom.css("webkitTransform", "rotate(" + progress + "deg)");
            this.$downProgressDom.css("transform", "rotate(" + progress + "deg)");
        }
    },
    touchEndEvent: function (e) {
        if (this.isDownScrolling) return;
        if (this.downHight > this.offset) {
            this.downHight = 50;
            this.$downwarp.addClass(this.resetClass);
            this.$downwarp.css("height", this.downHight+"px");
            this.$downTipDom.text(this.view.model.getKeyLang("loading", this.view.lang));
            this.triggerDownScroll();
        } else {
            this.downHight = 0;
            this.$downwarp.addClass(this.resetClass);
            this.$downwarp.css("height", this.downHight+"px");
            this.$scrollDom.removeClass(this.hardwareClass);
        }
        this.startY = 0;
        this.preX = 0;
        this.preY = 0;
        this.startTop = null;
    },
    triggerDownScroll: function () {
        this.isDownScrolling = true;
        this.callback && this.callback(this);
    },
    endDownScroll: function () {
        if (this.isDownScrolling) {
            this.isDownScrolling = false;
            this.downHight = 0;
            this.$downwarp.addClass(this.resetClass);
            this.$downwarp.css("height", this.downHight+"px");
            this.$scrollDom.removeClass(this.hardwareClass);
        }
    }
}; 

var WebappView = function (model, page) {
    this.model = model;
    this.page = page;
    this.widthpading = 0;
    this.switchCamEvent = new Event(this);
    this.refreshDevInfoEvent = new Event(this);
    this.scanSsidEvent = new Event(this);
    this.addScanSsidEvent = new Event(this);
    this.delScanSsidEvent = new Event(this);
    this.pingNetworkEvent = new Event(this);
    this.operateBlkDevEvent = new Event(this);
    this.cvbsOutputEvent = new Event(this);
    this.rebootEvent = new Event(this);
    this.showStatPage = new Event(this);
    this.showLogPage = new Event(this);
    this.showRecordPage = new Event(this);
    this.showAlarmListPage = new Event(this);
    this.getAlarmPages = new Event(this);
    this.getRecordPages = new Event(this);
    this.queryEvent = new Event(this);
    this.btnGetDevTimeEvent = new Event(this);
    this.queryLogEvent = new Event(this);
    this.queryImgEvent = new Event(this);
    this.requestPlaybackEvent = new Event(this);
    this.deleteEvent = new Event(this);
    this.submitConfigsEvent = new Event(this);
    this.submitSliAlgParamEvent = new Event(this);
    this.submitSensitivityEvent = new Event(this);
    this.submitShelterEvent = new Event(this);
    this.changeStreamTypeEvent = new Event(this);
    this.submitImageProcessEvent = new Event(this);
    this.cancelConfigsEvent = new Event(this);
    this.refreshConfigsEvent = new Event(this);
    this.getpdDataEvent = new Event(this);
    this.getAPCDataEvent = new Event(this);
    this.guiMask = new Event(this);
    this.refreshRecordEvent = new Event(this);
    this.restoreFactoryEvent = new Event(this);
    this.changePasswordEvent = new Event(this);
    this.changeTimeEvent = new Event(this);
    this.formatSdEvent = new Event(this);
    this.saveConfigsEvent = new Event(this);
    this.logoutEvent = new Event(this);
    this.CancelCalibration = new Event(this);
    this.getImageParamEvent = new Event(this);
    this.setImageParamEvent = new Event(this);
    this.calibrationEvent = new Event(this);
    this.apcSetConfigEvent = new Event(this);
    this.changeCaliModeEvent = new Event(this);
    this.selLangEvent = new Event(this);
    this.submitCalibrationBoardEvnet = new Event(this);
    this.setIconPosEvent = new Event(this);
    this.registerEvent = new Event(this);
    this.ptzEvent = new Event(this);
    this.afCalEvent = new Event(this);
    this.calibrationPreTemplate = null;
    this.indexMenuTemplate = null;
    this.selLangTemplate = null;
    this.ssidListTemplate = null;
    this.configsTemplate = null;
    this.sysInfoTemplate = null;
    this.wifiJson = null;
    this.collectJson = {};
    this.scanSsidJson = null;
    this.CalibrationJson = null;
    this.recordListJson = null;
    this.eventListJson = null;
    this.theme = null;
    this.lang = null;
    this.hardware = null;
    this.board = null;
    this.voSplit = null;
    this.pdRoiOutline = null;
    this.lastPdRoiOutline = null;
    this.pdRoiStyle = null;
    this.pdMinorAxis = null;
    this.pdRoiBoard = null;
    this.apcDirection = null;
    this.apcDivider = null;
    this.astApcDetectionPoints = null;
    this.lastRoiStyle = null;
    this.sversion = null;
    this.customer = null;
    this.calibration_screen = null;
    this.calibration_drawboard = null;
    this.apcScreen = null;
    this.calibration_board = null;
    this.calibration_div = null;
    this.calimode = null;
    this.lastCaliMode = null;
    this.chnNum = 4;
    this.opChn = 0;
    this.NormalVideoPage = 0;
    this.AlarmVideoPage = 0;
    this.AlarmPicPage = 0;
    this.NormalVideoTotalPage = 0;
    this.AlarmVideoTotalPage = 0;
    this.AlarmPicTotalPage = 0;
    this.recordType="alarm";
    this.fileType="video";
    this.IsRecLastPage = false;
    this.IsAlarmLastPage = false;
    this.alarmLogPage = 0;
    this.alarmLogTotal = 0;
    this.pdHollow = {};
    this.freemode_onOff= false;
    this.option_roistyle1;
    this.option_roistyle2;
    this.option_roistyle3;
    this.option_roistyle4;
    this.option_roistyle5;
    this.isClickCali = true;
    this.streamType = 0;//0:img  1:webrtc stream
    this.init(page);
};

var pageIsSelectmenuDialog = function( page ) {
    var isDialog = false,
        id = page && page.attr( "id" );

    $( ".filterable-select" ).each( function() {
        if ( $( this ).attr( "id" ) + "-dialog" === id ) {
            isDialog = true;
            return false;
        }
    });

    return isDialog;
};

WebappView.prototype = {
    init: function (page) {
        window.localStorage = window.localStorage || window.sessionStorage;
        var itemLang = window.location.host+"-lang";
        var itemTheme = window.location.host+"-theme";
        var itemCaliMode = window.location.host+"-calimode";
        window.localStorage && (this.lang = window.localStorage.getItem(itemLang));
        window.localStorage && (this.theme = window.localStorage.getItem(itemTheme));
        //window.localStorage && (this.calimode = window.localStorage.getItem(itemCaliMode));
        this.lang = this.lang || "EN";
        this.theme = this.theme || "b";
        //this.calimode = this.calimode || "normal";
        Handlebars.registerHelper("addOne", function(index){
            return index+1;
        });
        Handlebars.registerHelper("equal", function(v1, v2, options){
            if (v1 == v2) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("unequal", function(v1, v2, options){
            if (v1 != v2) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("arrSizeEqual", function(a, len, options){
            if (a.length == len) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isArray", function(v, options){
            if (Array.isArray(v)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotArray", function(v, options){
            if (!Array.isArray(v)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("mask", function(v1, v2, options){
            if ((v1 & (1<<v2)) > 0) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportIrcut", function(v1, options){
            if (v1 != null && (v1.indexOf("DMS885N") != -1 || v1.indexOf("WFT") != -1 || v1.indexOf("DMS") != -1 || v1.indexOf("ADA32V2") != -1 || v1.indexOf("ADA32V3") != -1 || v1.indexOf("ADA900") != -1 || v1.indexOf("RCT16947") != -1 || v1.indexOf("ADA42") != -1 || v1.indexOf("IPTR20S1") != -1 || v1.indexOf("ADA32C4") != -1)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportSubStream", function(v1, options){
            if (v1 != null && (v1.indexOf("ADA42") != -1 || v1.indexOf("IPTR20S1") != -1)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isWFTdev", function(v1, options){
            if (v1 != null && v1.indexOf("WFT") != -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotWFT", function(v1, options){
            if (v1 != null && v1.indexOf("WFT") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isWFdev", function(v1, options){
            if (v1 != null && v1.indexOf("WF") != -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotWFdev", function(v1, options){
            if (v1 != null && v1.indexOf("WF") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("hideNotSupportRotate", function(options){
            if (this != null && this.model != null){
                var config = this.model.getConfigJson();
                if (config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")){
                    if ("IPCR20S4".indexOf(config.ipcIdentification.hardware) != -1)
                    {
                        //if ("201894"== config.ipcIdentification.customer)
                            return "";
                    }
                    else if ("ADA32V2".indexOf(config.ipcIdentification.hardware) != -1 || "ADA32V3".indexOf(config.ipcIdentification.hardware) != -1 || "ADA32C4".indexOf(config.ipcIdentification.hardware) != -1)
                    {
                        return "";
                    }
                }
            }
            return "style=\"display:none;\"";
        }.bind(this));
        Handlebars.registerHelper("isLOSsys", function(v1, options){
            if (v1 != null && v1.indexOf("LOS") != -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isIPCdev", function(v1, options){
            if (v1 != null && v1.indexOf("IPC") != -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isA32OW", function(options){
            if (this.model != null && this.model.getSVersion() != null && (this.model.getSVersion().indexOf("H-1.45") != -1 || this.model.getSVersion().indexOf("H-1.99") != -1)){
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        }.bind(this));
        Handlebars.registerHelper("isNotA32OW", function(options){
            if (this.model != null && this.model.getSVersion() != null && (this.model.getSVersion().indexOf("H-1.45") == -1 && this.model.getSVersion().indexOf("H-1.99") == -1)){
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        }.bind(this));
        Handlebars.registerHelper("isA37", function(options){
            if (this.model != null && this.model.getSVersion() != null && (this.model.getSVersion().indexOf("H-1.45") != -1 || this.model.getSVersion().indexOf("H-1.99") != -1)){
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        }.bind(this));
        Handlebars.registerHelper("isA32", function(options){
            if (this.model != null && this.model.getMachineType() != null && (this.model.getMachineType().indexOf("ADA32V2") != -1 || this.model.getMachineType().indexOf("ADA32IR") != -1 || this.model.getMachineType().indexOf("ADA32N1") != -1 || this.model.getMachineType().indexOf("ADA32V3") != -1 || this.model.getMachineType().indexOf("RCT16947V2") != -1 || this.model.getMachineType().indexOf("ADA32C4") != -1
                || this.model.getMachineType().indexOf("ADA32E1") != -1 )) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        }.bind(this));
        Handlebars.registerHelper("isNotA32", function(options){
            if (this.model != null && this.model.getMachineType() != null && (this.model.getMachineType().indexOf("ADA32V2") != -1 || this.model.getMachineType().indexOf("ADA32IR") != -1 || this.model.getMachineType().indexOf("ADA32N1") != -1 || this.model.getMachineType().indexOf("ADA32V3") != -1 || this.model.getMachineType().indexOf("RCT16947V2") != -1 || this.model.getMachineType().indexOf("ADA32C4") != -1
                || this.model.getMachineType().indexOf("ADA32E1") != -1 )) {
                return options.inverse(this);
            } else {
                return options.fn(this);
            }
        }.bind(this));
        Handlebars.registerHelper("isA32V3", function(options){
            if (this.model != null && this.model.getMachineType() != null && (this.model.getMachineType().indexOf("ADA32V3") != -1 || this.model.getMachineType().indexOf("RCT16947V2") != -1)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        }.bind(this));
        Handlebars.registerHelper("isNotA32V3", function(options){
            if (this.model != null && this.model.getMachineType() != null && (this.model.getMachineType().indexOf("ADA32V3") != -1 || this.model.getMachineType().indexOf("RCT16947V2") != -1)) {
                return options.inverse(this);
            } else {
                return options.fn(this);
            }
        }.bind(this));
        Handlebars.registerHelper("isSupportFHD", function(v1, options){
            if (v1 != null && v1.indexOf("20S") != -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportFHD", function(v1, options){
            if (v1 != null && v1.indexOf("20S") == -1 && v1.indexOf("DMS") == -1 && v1.indexOf("ADA") == -1 && v1.indexOf("RCT16947") == -1){
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupport32K", function(v1, options){
            if (v1 != null && (v1.indexOf("IPCR20S3") != -1 || v1.indexOf("IPCR20S4") != -1 || v1.indexOf("IPCR20S5") != -1 || v1.indexOf("WF") != -1 || v1.indexOf("IPT") != -1 || v1.indexOf("ADA32N1") != -1 || v1.indexOf("ADA32E1") != -1 || v1.indexOf("ADA32C4") != -1)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
	    Handlebars.registerHelper("isNotSupportRotate", function(v1, options){
            if (v1 != null && v1.indexOf("IPCR20S2") == -1 && v1.indexOf("IPCR20S3") == -1 && v1.indexOf("IPCR20S4") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
		Handlebars.registerHelper("isNotSupportMJpeg", function(v1, options){
            if (v1 != null && v1.indexOf("IPCR20S2") == -1 && v1.indexOf("IPCR20S4") == -1 && v1.indexOf("ADA32V2") == -1 && v1.indexOf("ADA900V1") == -1 && v1.indexOf("IPTR20S1") == -1 && v1.indexOf("WFCR20S2") == -1 && v1.indexOf("ADA32E1") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
		Handlebars.registerHelper("isIPC", function(v1, options){
            if (v1 != null && v1.indexOf("IPCR20S2") != -1 || v1.indexOf("IPCR20S3") != -1 || v1.indexOf("IPCR20S4") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("showCalibrationBtn", function(v1, v2, options){
            if (v1 != null && v1.indexOf("DMS31V2") != -1) {
                return "style=\"display:inline;\"";
            } 
            else if(v2 != null && v2.indexOf("201623") != -1){
                return "style=\"display:inline;\"";
            }
            else {
                return "style=\"display:none;\"";
            }
        });

	    Handlebars.registerHelper("isNotIPC", function(v1, options){
            if (v1 != null && v1.indexOf("IPCR20S2") == -1 && v1.indexOf("IPCR20S3") == -1 && v1.indexOf("IPCR20S4") == -1 && v1.indexOf("IPCR20S5") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isSupportVO", function(v1, options){
            if (v1 != null && (v1.indexOf("ADA42V1") != -1 || v1.indexOf("ADA42PTZV1") != -1 || v1.indexOf("DMS885N") != -1 || v1.indexOf("DMS31V2") != -1 || v1.indexOf("ADA32V2") != -1 || v1.indexOf("ADA32IR") != -1 || v1.indexOf("ADA32V3") != -1 || v1.indexOf("RCT16947V2") != -1 || v1.indexOf("ADA32C4") != -1)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportVO", function(v1, options){
            if (v1 != null && v1.indexOf("ADA42V1") == -1 && v1.indexOf("ADA42PTZV1") == -1 && v1.indexOf("DMS885N") == -1 && v1.indexOf("DMS31V2") == -1 && v1.indexOf("ADA32V2") == -1 && v1.indexOf("ADA32IR") == -1  && v1.indexOf("ADA32V3") == -1 && v1.indexOf("RCT16947V2") == -1 && v1.indexOf("ADA32C4") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportViFps", function(v1, options){
            if (v1 != null && v1.indexOf("DMS31V2") == -1 && v1.indexOf("DMS885N") == -1 && v1.indexOf("ADA32V2") == -1 && v1.indexOf("ADA32IR") == -1 && v1.indexOf("ADA32N1") == -1 && v1.indexOf("ADA32V3") == -1 && v1.indexOf("RCT16947V2") == -1 && v1.indexOf("ADA32C4") == -1 && v1.indexOf("ADA32E1") == -1 ) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isSupportAlg", function(v1, options){
            if (v1 != null && (v1.indexOf("ADA42V1") != -1 || v1.indexOf("ADA42PTZV1") != -1 ||  v1.indexOf("DMS885N") != -1 ||  v1.indexOf("DMS31V2") != -1 || v1.indexOf("ADA32V2") != -1 || v1.indexOf("ADA32IR") != -1 || v1.indexOf("ADA32N1") != -1 || v1.indexOf("ADA32V3") != -1 || v1.indexOf("RCT16947V2") != -1 || v1.indexOf("ADA47V1") != -1 || v1.indexOf("ADA900V1") != -1 || v1.indexOf("HDW845V1") != -1 || v1.indexOf("ADA32C4") != -1
                || v1.indexOf("ADA32E1") != -1 )) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("showWIFISTA",function(hardware,serEnable,serNet){
            if(hardware!=null && hardware.indexOf("WF")!=-1)
            {
                return "style=\"display:block;\"";
            }
            else
            {
                if(hardware != null && (hardware.indexOf("DMS31V2") != -1 || hardware.indexOf("DMS885N") != -1 || hardware.indexOf("ADA32C4") != -1 ))
                {
                    if(serEnable == true && serNet == 1)                       
                        return "style=\"display:block;\"";
                    else
                        return "style=\"display:none;\"";
                }
                else
                {
                    return "style=\"display:none;\"";
                }
            }
        });

        
        Handlebars.registerHelper("NotAudioSetting", function(v1, options){
            if (v1 != null && (v1.indexOf("ADA42V1") != -1 || v1.indexOf("ADA42PTZV1") != -1 ||  v1.indexOf("DMS885N") != -1 ||  v1.indexOf("DMS31V2") != -1 || v1.indexOf("ADA32V2") != -1 || v1.indexOf("ADA32IR") != -1 || v1.indexOf("ADA32V3") != -1 || v1.indexOf("RCT16947V2") != -1 || v1.indexOf("ADA47V1") != -1 || v1.indexOf("HDW845V1") != -1)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportAlg", function(v1, options){
            if (v1 != null && v1.indexOf("ADA42V1") == -1 && v1.indexOf("ADA42PTZV1") == -1 && v1.indexOf("DMS885N") == -1 && v1.indexOf("DMS31V2") == -1 && v1.indexOf("ADA32V2") == -1 && v1.indexOf("ADA32IR") == -1 && v1.indexOf("ADA32N1") == -1 && v1.indexOf("ADA32V3") == -1 && v1.indexOf("RCT16947V2") == -1 && v1.indexOf("ADA900V1") == -1 && v1.indexOf("ADA47V1") == -1 && v1.indexOf("HDW845V1") == -1 && v1.indexOf("ADA32C4") == -1 && v1.indexOf("ADA32E1") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });

        Handlebars.registerHelper("isNotSupportADAS", function(v1, options){
            if (v1 != null && v1.indexOf("ADA42V1") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportDMS", function(v1, options){
            if (v1 != null && v1.indexOf("ADA42V1") == -1 && v1.indexOf("DMS31V2") == -1 && v1.indexOf("DMS885N") == -1 && v1.indexOf("ADA47V1") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportPD", function(v1, options){
            if (v1 != null && v1.indexOf("ADA42V1") == -1 && v1.indexOf("ADA42PTZV1") == -1 && v1.indexOf("ADA32V2") == -1 && v1.indexOf("ADA32IR") == -1 && v1.indexOf("ADA32N1") == -1 && v1.indexOf("ADA32V3") == -1 && v1.indexOf("RCT16947V2") == -1 && v1.indexOf("ADA32C4") == -1 && v1.indexOf("HDW845V1") == -1 && v1.indexOf("ADA32E1") == -1 ) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportAPC", function(v1, options){
            if (v1 != null && v1.indexOf("ADA900V1") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isNotSupportZoom", function(v1, options){
            if (v1 != null && v1.indexOf("HDW845V1") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });        
        Handlebars.registerHelper("isCustomer", function(v1, options){
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("customer")) {
                    if(config.ipcIdentification.customer != "" && v1.indexOf(config.ipcIdentification.customer) != -1)
                    {
                        return options.fn(this);
                    }
                }
            }
            return options.inverse(this);
        }.bind(this));
        Handlebars.registerHelper("isNotCustomer", function(v1, options){
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("customer")) {
                    if(config.ipcIdentification.customer == "" || v1.indexOf(config.ipcIdentification.customer) == -1)
                    {
                        return options.fn(this);
                    }
                }
            }
            return options.inverse(this);
        }.bind(this));
        Handlebars.registerHelper("isHardware", function(v1, options){
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")) {
                    if(config.ipcIdentification.hardware != "" && v1.indexOf(config.ipcIdentification.hardware) != -1)
                    {
                        return options.fn(this);
                    }
                }
            }
            return options.inverse(this);
        }.bind(this));
        Handlebars.registerHelper("isNotHardware", function(v1, options){
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")) {
                    if(config.ipcIdentification.hardware == "" || v1.indexOf(config.ipcIdentification.hardware) == -1)
                    {
                        return options.fn(this);
                    }
                }
            }
            return options.inverse(this);
        }.bind(this));
        Handlebars.registerHelper("hideDoubleAudioSourceDevice", function(options){
            hardware = "ADA900V1";
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")) {
                    if(config.ipcIdentification.hardware != "" && hardware.indexOf(config.ipcIdentification.hardware) != -1)
                    {
                        return "style=\"display:none;\"";
                    }
                }
            }
            return "";
        }.bind(this));
        Handlebars.registerHelper("hideNotDoubleAudioSourceDevice", function(options){
            hardware = "ADA900V1";
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")) {
                    if(config.ipcIdentification.hardware == "" || hardware.indexOf(config.ipcIdentification.hardware) == -1)
                    {
                        return "style=\"display:none;\"";
                    }
                }
            }
            return "";
        }.bind(this));
        Handlebars.registerHelper("displayCustomer", function(customer, options){
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("customer")) {
                    if(config.ipcIdentification.customer != "" && customer == config.ipcIdentification.customer)
                    {
                        if(config.systemConfig != null)
                        {
                            return "style=\"display:inline;\"";
                        }
                    }
                }
            }
            /* 适配index版本 */
            if(this != null && this.customer != null)
            {
                if(customer == this.customer)
                {
                    return "style=\"display:inline;\"";
                }
            }
            return "";
        }.bind(this));
        Handlebars.registerHelper("hideCustomer", function(customer, options){
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("customer")) {
                    if(config.ipcIdentification.customer != "" && customer.indexOf(config.ipcIdentification.customer) != -1)
                    {
                        if(config.systemConfig != null && config.systemConfig.webuiFull == false)
                        {
                            return "style=\"display:none;\"";
                        }
                    }
                }
            }
            /* 适配index版本 */
            if(this != null && this.customer != null)
            {
                if(this.customer != "" && customer.indexOf(this.customer) != -1)
                {
                    return "style=\"display:none;\"";
                }
            }
            return "";
        }.bind(this));
        Handlebars.registerHelper("hideNotCustomer", function(customer, options){
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("customer")) {
                    if(config.ipcIdentification.customer == "" || customer.indexOf(config.ipcIdentification.customer) == -1)
                    {
                        //if(config.systemConfig != null && ((config.systemConfig.webuiFull == false) || (config.ipcIdentification.hardware != "DMS31V2")))
                        if(config.systemConfig != null)
                        {
                            return "style=\"display:none;\"";
                        }
                    }
                }
            }

            /* 适配index版本 */
            if(this != null && this.customer != null)
            {
                if(this.customer == "" || customer.indexOf(this.customer) == -1)
                {
                    return "style=\"display:none;\"";
                }
            }

            return "";
        }.bind(this));
        Handlebars.registerHelper("hideHardware", function(hardware, options){
            if(this != null && this.model != null){
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")){
                    if(config.ipcIdentification.hardware != "" && hardware.indexOf(config.ipcIdentification.hardware) != -1)
                    {
                        return "style=\"display:none;\"";
                    }
                }
            }
            /* 适配index版本 */
            if(this != null && this.hardware != null)
            {
                if(this.hardware != "" && hardware.indexOf(this.hardware) != -1)
                {
                    return "style=\"display:none;\"";
                }
            }

            /* 适配query版本 */
            if(this.hasOwnProperty("statusJson") && this.statusJson.hasOwnProperty("devInfo") && this.statusJson.devInfo.hasOwnProperty(hardware))
            {
                if(this.statusJson.devInfo.hardware != "" && hardware.indexOf(this.statusJson.devInfo.hardware) != -1)
                {
                    return "style=\"display:none;\"";
                }
            }

            return "";
        }.bind(this));
        Handlebars.registerHelper("getSpecialHardware", function(options){
			var hardware = "";
			var customer = "";
            if(this != null && this.model != null){
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")){
                    if(config.ipcIdentification.hardware != "")
                    {
                        hardware = config.ipcIdentification.hardware;
                    }
                }
				if(config.ipcIdentification.customer != "")
				{
					customer = config.ipcIdentification.customer;
				}
            }
            /* 适配index版本 */
            if(this != null && this.hardware != null)
            {
                if(this.hardware != "")
                {
                    hardware =  this.hardware;
                }	
				if(this.customer != "")
				{
					customer = this.hardware;
				}
				
            }
			if(hardware == "ADA32V2" || hardware == "ADA32V3" || hardware == "ADA32C4")
			{
				if(customer == "200026")
				{
					return "DBC-AI20";
				}
                else if(customer == "201819")
                {
                    return "RCT16947V2";
                }
                else if(customer == "200789")
                {
                    return "C-R-AI-AHD";
                }
			}
            return hardware;
        }.bind(this));

        Handlebars.registerHelper("showHardware", function(hardware, options){
            if(this != null && this.model != null){
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")){
                    if(config.ipcIdentification.hardware != "" && hardware.indexOf(config.ipcIdentification.hardware) != -1)
                    {
                        return "";
                    }
                }
            }
            /* 适配index版本 */
            if(this != null && this.hardware != null)
            {
                if(this.hardware != "" && hardware.indexOf(this.hardware) != -1)
                {
                    return "";
                }
            }

            /* 适配query版本 */
            if(this.hasOwnProperty("statusJson") && this.statusJson.hasOwnProperty("devInfo") && this.statusJson.devInfo.hasOwnProperty(hardware))
            {
                if(this.statusJson.devInfo.hardware != "" && hardware.indexOf(this.statusJson.devInfo.hardware) != -1)
                {
                   return "";
                }
            }

            return "style=\"display:none;\"";
        }.bind(this));
        Handlebars.registerHelper("showHWandCustomer", function(hardware,customer){
            if(this != null && this.model != null){
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware") && config.ipcIdentification.hasOwnProperty("customer")){
                    if(config.ipcIdentification.hardware != "" && config.ipcIdentification.customer != ""  && hardware.indexOf(config.ipcIdentification.hardware) != -1 && customer.indexOf(config.ipcIdentification.customer) != -1)
                    {
                        return "";
                    }
                }
            }
            /* 适配index版本 */
            if(this != null && this.hardware != null && this.customer != null)
            {
                if(this.hardware != "" && hardware.indexOf(this.hardware) != -1 && this.customer != "" && customer.indexOf(this.customer) != -1)
                {
                    return "";
                }
            }

            return "style=\"display:none;\"";
        }.bind(this));
        Handlebars.registerHelper("hideHWandCustomer", function(hardware,customer){
            if(this != null && this.model != null){
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware") && config.ipcIdentification.hasOwnProperty("customer")){
                    if(config.ipcIdentification.hardware != "" && config.ipcIdentification.customer != ""  && hardware.indexOf(config.ipcIdentification.hardware) != -1 && customer.indexOf(config.ipcIdentification.customer) != -1)
                    {
                        return "style=\"display:none;\"";
                    }
                }
            }
            /* 适配index版本 */
            if(this != null && this.hardware != null && this.customer != null)
            {
                if(this.hardware != "" && hardware.indexOf(this.hardware) != -1 && this.customer != "" && customer.indexOf(this.customer) != -1)
                {
                    return "style=\"display:none;\"";
                }
            }

            return "";
        }.bind(this));
        Handlebars.registerHelper("hideCellular", function(options){
            if(this != null && this.model != null){
                var config = this.model.getCellularJson();
                if(config.hasOwnProperty("bCellExist") ){
                    if(!config.bCellExist)
                    {
                        return "style=\"display:none;\"";
                    }
                }
            }
            return "";
        }.bind(this));
        Handlebars.registerHelper("hideUSBCam", function(option){
            var usbCamHost = window.location.host.indexOf("192.168.222");
            //console.log( window.location.host);
            if(usbCamHost!=-1)
            {
                return "style=\"display:none;\"";
            }
            return "";
        }.bind(this));
        Handlebars.registerHelper("hideNotHardware", function(hardware, options){
            if(this != null && this.model != null){
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")){
                    if(config.ipcIdentification.hardware == "" || hardware.indexOf(config.ipcIdentification.hardware) == -1)
                    {

                        return "style=\"display:none;\"";
                    }
                }
            }
            /* 适配query版本 */
            if(this.hasOwnProperty("statusJson") && this.statusJson.hasOwnProperty("devInfo") && this.statusJson.devInfo.hasOwnProperty(hardware))
            {
                if(this.statusJson.devInfo.hardware == "" || hardware.indexOf(this.statusJson.devInfo.hardware) == -1)
                {
                    return "style=\"display:none;\"";
                }
            }

            /* 适配index版本 */
            if(this != null && this.hardware != null)
            {
                if(this.hardware == "" || hardware.indexOf(this.hardware) == -1)
                {
                    return "style=\"display:none;\"";
                }
            }
            return "";
        }.bind(this));

        Handlebars.registerHelper("hideNotWebuiFull", function(hardware, option){
            var config = this.model.getConfigJson();
            if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")){
                if(config.ipcIdentification.hardware != "" && hardware.indexOf(config.ipcIdentification.hardware) != -1)
                {
                    if(config.systemConfig != null && config.systemConfig.webuiFull == false)
                    {
                        return "style=\"display:none;\"";
                    }
                }
            }
            
            /* 适配index版本 */
            if(this != null && this.hardware != null)
            {
                if(this.hardware != "" && hardware.indexOf(this.hardware) != -1)
                {
                    if(this.webuiFull != null && this.webuiFull == false)
                    {
                        return "style=\"display:none;\"";
                    }
                }
            }
        }.bind(this));

        Handlebars.registerHelper("isCheckAlgEnable", function(){
            var config = this.model.getConfigJson();
            if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")){
                if(config.algConfig != "" && config.algConfig.algEnable == true)
                {
                        return "checked=\"checked\"";
                }
            }
            return "";
        }.bind(this));

        Handlebars.registerHelper("display720PScale", function(options){
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
				var v1 = "ADA32V2 ADA32V3 RCT16947V2 DMS31V2 DMS885N ADA32IR ADA42 WFCR20S2 WFCR20S1LOS ADA32C4";
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("hardware")) {
                    if(config.ipcIdentification.hardware != "" && v1.indexOf(config.ipcIdentification.hardware) != -1)
                    {
                        return "";
                    }
                }
            }
            return "(16:9)";
        }.bind(this));	

        Handlebars.registerHelper("isIRCam", function(v1, options){
            if (v1 != null && (v1 == 25)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });

        Handlebars.registerHelper("isIRCamButNotExhibition", function(v1, v2, v3, options){
            if ((v1 != null && (v1 == 25)) && (v2 != v3)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });

        Handlebars.registerHelper("isPTZ", function(hardware, options){
            if (hardware != null && (hardware.indexOf("ADA42PTZV1") == -1)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("hideCalibration", function(hardware, options){
            if (hardware != null && hardware.indexOf("ADA42V1") == -1 && hardware.indexOf("ADA42PTZV1") == -1 && hardware.indexOf("DMS885N") == -1 && hardware.indexOf("DMS31V2") == -1 && hardware.indexOf("ADA32V2") == -1  && hardware.indexOf("ADA32IR") == -1  && hardware.indexOf("ADA32N1") == -1 && hardware.indexOf("ADA32V3") == -1 && hardware.indexOf("RCT16947V2") == -1 && hardware.indexOf("ADA47V1") == -1 && hardware.indexOf("ADA900V1") == -1 && hardware.indexOf("ADA32C4") == -1  && hardware.indexOf("ADA32E1") == -1) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("hideNotBoard", function(board, options){
            if(this != null && this.model != null){
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("board")){
                    if(config.ipcIdentification.board == "" || board.indexOf(config.ipcIdentification.board) == -1)
                    {
                        return "style=\"display:none;\"";
                    }
                }
            }
            return "";
        }.bind(this));

        Handlebars.registerHelper("hideBoard", function(board, options){
            if(this != null && this.model != null){
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("board")){
                    if(config.ipcIdentification.board == "" || board.indexOf(config.ipcIdentification.board) != -1)
                    {
                        return "style=\"display:none;\"";
                    }
                }
            }
            return "";
        }.bind(this));

        Handlebars.registerHelper("isNotSupportRecord", function(v1, options){
            console.log(v1);
            if (v1 != 0 && (v1 == 20 || v1 == 22 || v1 == 23 ||  v1==24 ||  v1==25 || v1==26 || v1==27 || v1==28 || v1==29 || v1==30 || v1==31|| v1==34 || v1==36 || v1==37)) {
                return options.inverse(this);
            } else {
                return options.fn(this);
            }
        });
        Handlebars.registerHelper("isNotSupportServer", function(v1, options){
            if (v1 != null && v1.indexOf("DMS31V2") == -1  && v1.indexOf("WFCR20S2") == -1 && v1.indexOf("ADA32C4") == -1 ){
                return options.fn(this);
            } else {
                // if(v1.indexOf("WFCR20S2")!=-1)
                // {
                //     var config = this.model.getConfigJson();
                //     if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("customer")) {
                //         if("201795" != config.ipcIdentification.customer)
                //         {
                //             return options.fn(this);
                //         }
                //     }
                // }
                return options.inverse(this);
            }
        }.bind(this));
        Handlebars.registerHelper("isSupportServer", function(v1, options){
            if (v1 != null && (v1.indexOf("DMS885N") != -1 || v1.indexOf("DMS31V2") != -1 || v1.indexOf("ADA32C4") != -1 )/*&& v1.indexOf("WFCR20S2") == -1 */){
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("lessThan", function(v1, v2, options){
            if (v1 < v2) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isBitSet", function(v, b, options){
            if (v & (1<<b)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("hasOwnProperty", function(obj, key, options){
            if (obj.hasOwnProperty(key)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("notOwnProperty", function(obj, key, options){
            if (!obj.hasOwnProperty(key)) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isActivate", function(options){
            if(this != null && this.model != null) {
                var config = this.model.getConfigJson();
                if(config.ipcIdentification && config.ipcIdentification.hasOwnProperty("keyAuth")) {
                    if(config.ipcIdentification.keyAuth == false)
                    {
                        return options.inverse(this);
                    }
                }
            }
            return options.fn(this);
        }.bind(this));
        Handlebars.registerHelper("isTrueVal", function(obj, key, options){
            if (obj.hasOwnProperty(key) && obj[key]) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("isFalseVal", function(obj, key, options){
            if (obj.hasOwnProperty(key) && !obj[key]) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("getObjVal", function(obj, key){
            return obj[key];
        });
        Handlebars.registerHelper("getKeyLang", function(key){
            return this.model.getKeyLang(key, this.lang);
        }.bind(this));
        Handlebars.registerHelper("getArrayVal", function(obj, key, index){
            return obj[key][index];
        });
        Handlebars.registerHelper("getThumbPath", function(obj){
            if (!obj.hasOwnProperty("picPath")){
                return "./css/images/register.png";
            } else {
                var value = obj["picPath"];
                value = value.replace(".jpg", "_thumb.jpg");
                return value;
            }
        }.bind(this));
        Handlebars.registerHelper("getResPath", function(obj){
            if (obj.hasOwnProperty("filePath")) {
                return obj["filePath"];
            } else if (obj.hasOwnProperty("picPath")){
                return obj["picPath"];
            } else {
                return null;
            }
        }.bind(this));
        Handlebars.registerHelper("getResClass", function(obj){
            if (obj.hasOwnProperty("filePath") && obj.filePath.indexOf("jpg")!=-1) {
                return "btn-display-picture";
            } else {
                return "btn-play-video";
            }
        }.bind(this));
        Handlebars.registerHelper("getResIcon", function(obj){
            /*if (obj.hasOwnProperty("filePath")) {
                return "./css/images/rec-video.png"
            } else {
                return "./css/images/rec-image.png";
            }*/
            if(obj.hasOwnProperty("filePath") && obj.filePath.indexOf(".jpg")!=-1){
                return obj.filePath;
            }else{
                return "./css/images/rec-video.png";
            }
        }.bind(this));
        Handlebars.registerHelper("transYesNo", function(value){
            if (value) {
                return this.model.getKeyLang("yes", this.lang);
            } else {
                return this.model.getKeyLang("no", this.lang);
            }
        }.bind(this));
        Handlebars.registerHelper("transCloseOpen", function(value){
            if (value) {
                return this.model.getKeyLang("close", this.lang);
            } else {
                return this.model.getKeyLang("open", this.lang);
            }
        }.bind(this));
        Handlebars.registerHelper("transMachine", function(value){
            switch (value) {
                case 0: return "DV376";
                case 1: return "DV423";
                default : return "unknown"
            }
        });
        Handlebars.registerHelper("transCellHwStat", function(value){
            switch (value){
                case 0:
                    return this.model.getKeyLang("running", this.lang);
                case 1:
                    return this.model.getKeyLang("initing", this.lang);
                case 2:
                    return this.model.getKeyLang("no-sim-card", this.lang);
                case 3:
                    return this.model.getKeyLang("at-cmd-err", this.lang);
                case 4:
                    return this.model.getKeyLang("at-cmd-timeout", this.lang);
                case 5:
                    return this.model.getKeyLang("connect-fail", this.lang);
                case 6:
                    return this.model.getKeyLang("lost-device", this.lang);
                default :
                    return this.model.getKeyLang("unknown-error", this.lang);
            }
        }.bind(this));
        Handlebars.registerHelper("transCellRegStat", function(value){
            switch (value){
                case 0:
                    return this.model.getKeyLang("noworking", this.lang);
                case 1:
                    return this.model.getKeyLang("registered", this.lang);
                case 2:
                    return this.model.getKeyLang("registing", this.lang);
                case 3:
                    return this.model.getKeyLang("denied", this.lang);
                case 4:
                    return this.model.getKeyLang("unknown", this.lang);
                case 5:
                    return this.model.getKeyLang("roaming", this.lang);
                default :
                    return this.model.getKeyLang("unknown-error", this.lang);
            }
        }.bind(this));
        Handlebars.registerHelper("transConnectStat", function(value){
            switch (value){
                case 0:
                    return this.model.getKeyLang("disconnected", this.lang);
                case 1:
                    return this.model.getKeyLang("connecting", this.lang);
                case 2:
                    return this.model.getKeyLang("connected", this.lang);
                default :
                    return this.model.getKeyLang("unknown-error", this.lang);
            }
        }.bind(this));
        Handlebars.registerHelper("transInitStat", function(value){
            switch (value){
                case 0:
                    return this.model.getKeyLang("not-init", this.lang);
                case 1:
                    return this.model.getKeyLang("initing", this.lang);
                case 2:
                    return this.model.getKeyLang("inited", this.lang);
                default :
                    return this.model.getKeyLang("init-fail", this.lang);
            }
        }.bind(this));
        Handlebars.registerHelper("transFsType", function(value){
            switch (value){
                case 1:
                    return "fat32";
                case 2:
                    return "ext3";
                case 3:
                    return "ext4";
                default :
                    return "-";
            }
        }.bind(this));
        Handlebars.registerHelper("transMB2GB", function(obj, key){
            return Math.round(obj[key]/1024);
        });
        Handlebars.registerHelper("transByte2MB", function(value){
            if(value/1024 < 1024)
                return (value/1024).toFixed(2)+"KB";
            else 
                return (value/1024/1024).toFixed(2)+"MB";
        });
        Handlebars.registerHelper("transPts2Date", function(value){         
            if(this != null && this.model != null)
            {               
                this.nowTime = new Date();
                var devUTChour = this.model.getConfigJson().systemConfig.time.UTChour;
                var devUTCmin = this.model.getConfigJson().systemConfig.time.UTCminute;
                var utcSec = this.nowTime.getTimezoneOffset()*60;
                var time = new Date((value + utcSec + devUTChour*3600 + devUTCmin*60 )*1000);
                var y = time.getFullYear();
                var m = time.getMonth()+1;
                (m<10)&&(m='0'+m);
                var d = time.getDate();
                (d<10)&&(d='0'+d);
                var h = time.getHours();
                (h<10)&&(h='0'+h);
                var mm = time.getMinutes();
                (mm<10)&&(mm='0'+mm);
                var s = time.getSeconds();
                (s<10)&&(s='0'+s);
                return y+'-'+m+'-'+d+' '+h+':'+mm+':'+s;
            }
        }.bind(this));
        Handlebars.registerHelper("transDuration2Time", function(value){
            var h = Math.floor(value/1000/3600);
            (h<10)&&(h='0'+h);
            var mm = Math.round((Math.floor(value/60000))%60);
            (mm<10)&&(mm='0'+mm);
            var s = Math.round(value/1000%60);
            (s<10)&&(s='0'+s);
            return h+':'+mm+':'+s;
        });
        Handlebars.registerHelper("transRecType", function(value){
            return this.model.getKeyLang(value, this.lang);
        }.bind(this));
        Handlebars.registerHelper("isFileWriting", function(file, options){
            console.log(file);
            if (file.hasOwnProperty("duration") && file["duration"] == 0
                && file.hasOwnProperty("fileSize") && file["fileSize"] == 0) {
                return options.fn(this);
            } else {
                return options.inverse(this);
            }
        });
        Handlebars.registerHelper("transSdName", function(value){
            switch (value){
                case 0:
                    return "SD1";
                case 1:
                    return "SD2";
                case 2:
                    return "SD3";
                case 3:
                    return "EMMC";
                case 4:
                    return "Udisk";
                default :
                    return "unknown";
            }
        }.bind(this));
        Handlebars.registerHelper("transSdStat", function(value){
            switch (value){
                case 0:
                    return this.model.getKeyLang("no-sdcard", this.lang);
                case 1:
                    return this.model.getKeyLang("enable", this.lang);
                case 2:
                    return this.model.getKeyLang("mount", this.lang);
                case 3:
                    return this.model.getKeyLang("disable", this.lang);
                case 4:
                    return this.model.getKeyLang("exception", this.lang);
                case 5:
                    return this.model.getKeyLang("repairing-fs", this.lang);
                case 6:
                    return this.model.getKeyLang("repairing-par", this.lang);
                case 7:
                    return this.model.getKeyLang("formating", this.lang);
                case 8:
                    return this.model.getKeyLang("cleaning", this.lang);
                default :
                    return this.model.getKeyLang("unknown", this.lang);
            }
        }.bind(this));
        Handlebars.registerHelper("transRes", function(value){
            switch (value){
                case 1:
                    return "D1";
                case 2:
                    return "720P";
                case 3:
                    return "1080P";
                default :
                    return "-";
            }
        }.bind(this));
        Handlebars.registerHelper("transCvbsStat", function(vout, aout){
            var stat = "";
            for (var i=0; i<4; i++) {
                if (vout & (1<<i)){
                    stat += "1 "
                }else{
                    stat += "0 "
                }
            }
            if (aout){
                stat += "Audio"
            }else {
                stat += "Mute"
            }
            return stat;
        }.bind(this));
        Handlebars.registerHelper("transVoltage", function(value){
            return value/100;
        });
        Handlebars.registerHelper("transDatetime", function(value){
            var date = new Date(value*1000);
            return date.Format("yyyy-MM-dd hh:mm:ss");
        });
        Handlebars.registerHelper("transReason", function(value){
            var reason = Number(String(value).substr(-1,1));
            switch (reason) {
                case 0:
                    return "default";
                case 1:
                    return "12v";
                case 2:
                    return "url";
                case 3:
                    return "manual";
                case 4:
                    return "gsensor";
                case 5:
                    return "unsafe power off";
                case 6:
                    return "serial";
                case 7:
                    return "playback";
                default :
                    return "unknown";
            }
        });
        Handlebars.registerHelper("transImgUrl", function(value){
            return "./img/"+value;
        });
        Handlebars.registerHelper("transImgChn", function(value){
            return "CH"+(value+1);
        });
        Handlebars.registerHelper("transPercent", function(v, p){
            console.log("v:"+v+", p:"+p);
            return parseInt(v*100/p);
        });

        Date.prototype.Format = function (fmt) {
            var o = {
                "M+": this.getMonth() + 1,
                "d+": this.getDate(),
                "h+": this.getHours(),
                "m+": this.getMinutes(),
                "s+": this.getSeconds(),
                "q+": Math.floor((this.getMonth() + 3) / 3)
            };
            if (/(y+)/.test(fmt))
                fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
            for (var k in o){
                if (new RegExp("(" + k + ")").test(fmt)) {
                    fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
                }
            }
            return fmt;
        };
        switch (page) {
            case "preview":
                this.isFullscreen = false;
                this.imgQuality = 3;
                this.imgWidth = 1920;
                this.imgHeight = 1080;
                this.imgUrls = [];
                var host;
                if(window.location.host.indexOf("8443")!=-1)
                {
                    host = window.location.host.slice(0, window.location.host.lastIndexOf(":8443"));
                    for (var i=0; i<4; i++) {
                        this.imgUrls.push("https://"+host+":8445/snap"+i+".jpeg?quality="+this.imgQuality);
                    }
                    this.imgUrls.push("https://"+host+":8445/snap"+0+".jpeg?quality="+this.imgQuality);
                    this.imgwsUrl = "wss://"+host+":8448/ws";
                }
                else if(window.location.host.indexOf("8080")!=-1 || window.location.host.indexOf("8081")!=-1)
                {
                    host = window.location.host.slice(0, window.location.host.lastIndexOf(":8080"));
                    for (var i=0; i<4; i++) {
                        this.imgUrls.push("http://"+host+":8081/snap"+i+".jpeg?quality="+this.imgQuality);
                    }
                    this.imgUrls.push("http://"+host+":8081/snap"+0+".jpeg?quality="+this.imgQuality);
                    this.imgwsUrl = "ws://"+host+":8083/ws";
                }
                this.selLangTemplate = Handlebars.compile($("#sel-lang-template").html());
                this.indexMenuTemplate = Handlebars.compile($("#index-menu-template").html());
                this.calibrationPreTemplate = Handlebars.compile($("#calibration-info-template").html());
                break;

            case "device":
                //this.downScroll = new DownScroll(this, "page-device", null);
                //this.cellInfoTemplate = Handlebars.compile($("#cell-info-template").html());
                //this.wifiInfoTemplate = Handlebars.compile($("#wifi-info-template").html());
                this.scanSsidTemplate = Handlebars.compile($("#scan-ssid-template").html());
                this.storageInfoTemplate = Handlebars.compile($("#storage-info-template").html());
                this.cameraInfoTemplate = Handlebars.compile($("#camera-info-template").html());
                this.powerInfoTemplate = Handlebars.compile($("#power-info-template").html());
                this.gpioInfoTemplate = Handlebars.compile($("#gpio-info-template").html());
                this.usbInfoTemplate = Handlebars.compile($("#usb-info-template").html());
                break;

            case "query":
                this.deviceTime = new Date();
                this.logTypeList = [];
                this.allLogChecked = false;
                this.curTabQuery = "tab-status";
                this.mediaPlayer = new MediaElementPlayer(document.getElementById("mediaplayer"), {
                    stretching: 'auto',
                    pluginPath: './js/',
                    success: function (media) {
                        console.log(media.id);
                        media.addEventListener('loadedmetadata', function () {
                            console.log("loadedmetadata");
                        });
                        media.addEventListener('error', function (e) {
                            console.log("media error!");
                        });
                    }
                });
                this.statusListTemplate = Handlebars.compile($("#status-list-template").html());
                this.formatTemplate = Handlebars.compile($("#format-sdcard-template").html());
                this.logListTemplate = Handlebars.compile($("#log-list-template").html());
                //this.cellInfoTemplate = Handlebars.compile($("#cell-info-template").html());
                //this.wifiInfoTemplate = Handlebars.compile($("#wifi-info-template").html());
                //this.cmsServerTemplate = Handlebars.compile($("#cmsServer-status-template").html());
                this.recordListTemplate = Handlebars.compile($("#record-list-template").html());
                break;

            case "config":
                //this.downScroll = new DownScroll(this, "page-config", null);
                this.ssidListTemplate = Handlebars.compile($("#ssid-list-template").html());
                this.configsTemplate = Handlebars.compile($("#configs-template").html());
                break;
            case "system":
                this.deviceTime = new Date();
                this.sysInfoTemplate = Handlebars.compile($("#sys-info-template").html());
                break;
        }
        this.createChildren(page)
            .setupHandlers(page)
            .enable(page)
            .changeLang(page)
            .changeTheme(page);
    },

    createChildren: function (page) {
        page = page || this.page;
        var itemLang = window.location.host+"-lang";
        this.$btnRefresh = $(".btn-refresh");
        this.$btnNav = $(".xbtn-nav");/*.btn-nav 改成了.xbtn-nav*/
        this.$popupShowInfo = $("#popup-show-info");
        this.$popupAskForSure = $("#popup-ask-for-sure");
        this.$popupFormInput2 = $("#popup-form-input2");
        this.$popupFormInput3 = $("#popup-form-input3");
	    this.$popupFormInput4 = $("#popup-form-input4");
        this.$btnAskForSure = $("#btn-ask-for-sure");
        this.$btnAskForCancel = $("#btn-ask-for-cancel");
        this.$btnFormInput2 = $("#btn-form-input2");
        this.$btnFormInput3 = $("#btn-form-input3");
	    this.$btnFormInput4 = $("#btn-form-input4");
        this.$btnPopup = $(".btn-popup");
        switch (page) {
            case "preview":
                this.$btnLogout = $("#btn-logout");
                this.$btnCancelCalibration = $("#btn-cancelCalibration");
                this.$btnTheme = $("#btn-theme");
                this.$selLang = $("#sel-lang");
                this.$selCaliMode = $("#algConfig-pd-pdWorkMode");
                this.$selAPCDirection = $("#select-apc-direction");
                this.$selLang.val(this.lang);//.selectmenu("refresh");
                //this.$selCaliMode.val(this.calimode);
                this.$selRoiStyle = $("#algConfig-pd-pdRoiStyle");
                this.$roiBoardColorSelect = $("#roi-board-color-select");
                this.$roiBoardColorScale = $("#roi-board-color-scale");
                this.$roiBoardHiderOther = $("#roi-board-hide-other");
                this.$selHollow = $("#select-hollow");
                this.$selIconPos = $("#select-iconLocation");
                this.$selLang.trigger("change");
                this.$btnDrawRoi = $("#roi_button");
                this.$btnDrawHollow = $("#hollow_button");
                this.$camWin4x = $("#cam-win-4x");
                this.$camWin2x = $("#cam-win-2x");
                this.$camWin1x = $("#cam-win-1x");
                this.$camImg = $(".cam-img");//$("img");
                this.$imgSolo = $("#img-solo");
                this.$btnSwitchStream = $(".btn-switch-stream");
                this.$btnCalibration = $(".btn-calibration");
                this.$btnSubmitCalibrationBoard = $("#btn_calibrationboard_submit");
                this.$btnCalibrationStartConfirm = $("#btn_calibration_start_confirm");
                this.$btnCalibrationStartCancel = $("#btn_calibration_start_cancel");
                this.$btnRegister = $(".btn-register");
                this.$btnFrsDivBack = $("#frs_div_back");
                this.$btnFrstestLogin = $("#testLoginBtn");
                this.$btnFrsnewUser = $("#newUserBtn");
                this.$btnDeleteUser = $("#deleteUserBtn");
                this.$inputDeleteAll = $("#check-all");
                this.$btnImportFaceId = $("#import_user");
                this.$btnExportFaceId = $("#export_user");
                this.$btnFullscreen = $(".btn-fullscreen");
                this.$btnGetTrackPos = $(".btn-trackpos");
                this.$btnSetZoom = $(".btn-zoom");
                this.$btnStreamType = $("#streamType");
                this.$selSensitivity = $("#FCT-Set-pdSensitivity");
                this.$btnshelterEnable = $("#FCT-shelterEnable");
                this.$btnZoomAry = $("#btn-zoom-array");
                this.$btnImageParam = $(".btn-imageparam");
                this.$sliImageParam = $(".ui-slider-handle");
                this.$imageParamChange = $(".slider-image-param");
                this.$algParamChange = $(".slider-alg-param");
                this.$imageProcessMirror = $("#imageProcess-mirrorEnable");
                this.$imageProcessFilp = $("#imageProcess-flipEnable");
                this.$uiHeader = $(".ui-header");
                this.$uiNavbar = $(".ui-navbar");
                this.$uiFooter = $(".ui-footer");
                this.$txtWarn = $("#txt_warn");
                this.$divWarn = $(".div_warn");
                this.$ptzBtn = $(".ptz-btn");
                this.$freemodeEnable = $("#roi-freemode-enable");
                this.$btnTestPreview = $("#TestPreview");
                /*this.$camImg.each(function(i, d){
                    console.log(i+":"+d);
                    d.src = this.imgUrls[i];
                }.bind(this));*/
                break;

            case "device":
                this.$tabDevice = $(".tab-device");
                this.$btnPing = $(".btn-ping");
                this.$btnCvbs = $(".btn-cvbs");
                this.$btnOuput = $("#btn-ouput");
                this.$btnCurTab = $("#btn-cellular");
                break;

            case "query":
                this.$popupShowInfo2 = $("#popup-show-info2");
                this.$pageChange = $(".btn-page");
                this.$tabQuery = $(".tab-query");
                this.$recordFilter = $(".btn-filter");
                this.$selTimeType = $("#sel-time-type");
                this.$btnQuery = $("#btn-query");
                this.$btnDevTime = $("#btn-dev-time");
                this.$selRelativeTime = $("#sel-relative-time");
                this.$inputBeginTime = $("#input-begin-time");
                this.$inputEndTime = $("#input-end-time");
                this.$selClipStat = $("#sel-clip-stat");
                this.$btnPlayVideo = $(".btn-play-video");
                this.$btnDisplayPicture = $(".btn-display-picture");
                this.$btnRefreshPicture = $("#btn-refresh-picture");
                this.$btnRequestPlayback = $("#btn-request-playback");
                this.$popupShowRequestPlayback = $("#popup-request-playback");
                this.$btnSurePlayback = $("#btn-sure-playback");
                this.$btnSwitchVideoChn = $(".btn-switch-video-chn");
                this.$btnPopupType = $("#btn-popup-type");
                this.$btnSelectAll = $("#btn-select-all");
                this.$checkLogType = $(".check-log-type");
                this.$btnSearchLog = $("#btn-search-log");
                this.$btnDownload = $(".btn-download");
                this.$jumpPage = $("#jump-page-button");
                break;

            case "config":
                this.$tabConfigs = $(".tab-configs");
                this.$btnSelChn = $(".xsel-chn");
                this.$mediaChnCfg = $(".media-chn-conf");
                this.$selChnAlg = $(".sel-chn-alg");
                this.$tabAlgConf = $(".alg-conf");
                this.$tabChnConf = $(".tab-chn-conf");
                this.$tabAlgWire = $(".tab-alg-wire");
                this.$checkDHCP = $("#networkConfig-ethernet-enableDHCP");
                this.$checkNTP = $("#systemConfig-time-ntpEnable");
                this.$sendmediaJson = $("#media");
                this.$sendalgJson = $("#alg");
                this.$sendnetworkJson = $("#network");
                this.$sendsysJson = $("#sys");
                this.$btnSubmit = $("#btn-submit");
                this.$btnCancel = $("#btn-cancel");
                this.$selectDMSsensitivity = $('#algConfig-dmsSensitivity');
                this.$selectRecordMode = $('#systemConfig-recMode');
                this.$inputCharNormal = $(".char-normal");
                this.$selApFrequency = $("#networkConfig-wifi-apMode-frequency");
                this.$selSerNetType = $("#serverConfig-netType");
                this.$btnServerEnable=$("#serverConfig-enable");
                this.$tabSet2gChannel = $("#set-2G-channel");
                this.$tabSet5gChannel = $("#set-5G-channel");
                this.$selSet2gChannel = $("#networkConfig-wifi-apMode-set2GChannel");
                this.$selSet5gChannel = $("#networkConfig-wifi-apMode-set5GChannel");
                this.$selCountryCode = $("#networkConfig-wifi-apMode-countryCode");
                this.$selMainStreamFramerate = $("#mediaConfig-mainFramerate");
                this.$selSubStreamFramerate = $("#mediaConfig-subFramerate");
                this.$selMainStreamEncode = $("#mediaConfig-mainStream-enEncode");
                this.$selMainStreamRcMode = $("#mediaConfig-mainStream-rcMode");
                this.$selMainStreamIfrmInterval = $("#mediaConfig-mainStream-ifrmInterval");
                this.$selSubStreamIfrmInterval = $("#mediaConfig-subStream-ifrmInterval");
                this.$selVideoMode = $("#mediaConfig-imageProcess-videoMode");
                this.$checkPdAlarmAuto = $("#pdalarmout-auto-enable");
                this.$checkIrPdAlarmAuto = $("#ir-pdalarmout-auto-enable");
                this.$checkZoomAlarmAuto = $("#zoomalarmout-auto-enable");
                this.$algAudioConf = $(".alg-audio-conf");
                this.$pdsDetInterval = $(".pds-det-interval");
                this.$voStreamFormat = $("#mediaConfig-voStreamFormat");
                break;

            case "system":
                this.$btnDevTime = $("#systemConfig-changetime");
                this.$inputArticleNum = $("#ipcIdentification-articleNum");
                this.$inputspSerialNum = $("#ipcIdentification-spSerialNum");
                this.$showError = $("#show-error");
				this.$btnImportConfig = $("#import_config");
				this.$btnExportConfig = $("#export_config");
                this.$btnExportLogFile = $("#export_logfile");
				break;
        }

        return this;
    },

    setupHandlers: function (page) {
        page = page || this.page;
        this.btnAskForSureHandler = this.btnAskForSure.bind(this);
        this.$btnAskForCancelHandler = this.btnAskForCancel.bind(this);
        this.btnFormInput2Handler = this.btnFormInput2.bind(this);
        this.btnFormInput3Handler = this.btnFormInput3.bind(this);
	    this.btnFormInput4Handler = this.btnFormInput4.bind(this);
        this.popupTriggerHandler = this.popupTrigger.bind(this);
        switch (page) {
            case "preview":
                this.btnLogoutHandler = this.btnLogout.bind(this);
                this.btnCancelCalibrationHandler = this.btnCancelCalibration.bind(this);
                this.btnThemeHandler = this.btnTheme.bind(this);
                this.selRoiStyleHandler = this.selRoiStyle.bind(this);
                this.roiBoardChangeHandler = this.roiBoardChange.bind(this);
                this.selLangHandler = this.selLang.bind(this);
                this.selCaliModeHandler = this.selCaliMode.bind(this);
                this.$selAPCDirectionHandler = this.selAPCDirection.bind(this);
                this.btnDrawRoiHandler = this.drawRoi.bind(this);
                this.btnDrawHollowHandler = this.drawHollow.bind(this);
                this.selHollowHandler = this.selHollow.bind(this);
                this.selIconPosHandler = this.selIconPos.bind(this);
                this.imgClickHandler = this.imgClick.bind(this);
                this.btnCalibrationHandler = this.btnCalibration.bind(this);
                this.btnSubmitCalibrationBoardHandler = this.btnSubmitCalibrationBoard.bind(this);
                this.btnCalibrationStartConfirmHandler = this.btnCalibrationStartConfirm.bind(this);
                this.btnCalibrationStartCancelHandler = this.btnCalibrationStartCancel.bind(this);
                this.btnRegisterHandler = this.btnRegister.bind(this);
				this.btnFrsDivBackHandler = this.btnFrsDivBack.bind(this);
				this.btnFrstestLoginHandler = this.btnFrstestLogin.bind(this);
                this.btnFrsnewUserHandler = this.btnFrsnewUser.bind(this);
                this.btnDeleteUserHandler = this.btnDeleteUser.bind(this);
                this.inputDeleteAllHandler = this.checkDeleteAll.bind(this);
                this.btnFullscreenHandler = this.btnFullscreen.bind(this);
                this.btnGetTrackPosHandler = this.btnGetTrackPos.bind(this);
                this.btnSetZoomHandler = this.btnSetZoom.bind(this);
                this.selSetSensitivityHandler = this.selSetSensitivity.bind(this);
                this.changeStreamTypeHandler = this.changeStreamType.bind(this);
                this.btnshelterEnableHandler = this.btnshelterEnable.bind(this);
                this.btnZoomAryHandler = this.btnZoomAryClick.bind(this);
                this.btnZoomAryStopHandler = this.btnZoomAryRelease.bind(this);
                this.windowResizeHandler = this.adaptScreen.bind(this);
                this.btnSwitchStreamHandler = this.btnSwitchStream.bind(this);
                this.btnImageParamHandler = this.btnImageParam.bind(this);
                this.sliImageParamHandler = this.sliImageParam.bind(this);
                this.sliAlgParamHandler = this.sliAlgParam.bind(this);
                this.MirrorEnableHandler = this.imageMirrorEnable.bind(this);
                this.FilpEnableHandler = this.imageFilpEnable.bind(this);
                this.fullscreenChangeHandler = this.fullscreenChange.bind(this);
                this.btnImportFaceIdHandler = handleImportFaceId.bind(this);
                this.btnExportFaceIdHandler = handleExportFaceId;
                this.ptzStartHandler = this.ptzTopStart.bind(this);
                this.ptzStopHandler = this.ptzTopStop.bind(this);
                this.$freemodeEnableHandler = this.freemodeEnable.bind(this);
                this.btnTestPreviewHandler = this.TestPreview.bind(this);//FCT 客户定制
                break;

            case "device":
                this.switchDeviceHandler = this.switchDevice.bind(this);
                this.btnRefreshHandler = this.btnRefresh.bind(this);
                this.btnPingHandler = this.btnPing.bind(this);
                this.btnRefreshSsidHandler = this.btnRefreshSsid.bind(this);
                this.btnOuputHandler = this.btnOuput.bind(this);
                this.swipeLeftHandler = this.swipeLeft.bind(this);
                this.swipeRightHandler = this.swipeRight.bind(this);
                break;

            case "query":
                this.switchQueryHandler = this.switchQuery.bind(this);
                this.pageChangeHandler = this.pageChange.bind(this);
                this.recordFilterHandler = this.recordFilter.bind(this);
                this.switchTimeTypeHandler = this.switchTimeType.bind(this);
                this.queryHandler = this.query.bind(this);
                this.btnDisplayPictureHandler = this.btnDisplayPicture.bind(this);
                this.btnPlayVideoHandler = this.btnPlayVideo.bind(this);
                this.btnRefreshPictureHandler = this.btnRefreshPicture.bind(this);
                this.btnRequestPlaybackHandler = this.btnRequestPlayback.bind(this);
                this.btnSurePlaybackHandler = this.btnSurePlayback.bind(this);
                this.sliderChangeHandler = this.sliderChange.bind(this);
                this.beforeshowQueryHandler = this.beforeshowQuery.bind(this);
                this.beforeshowDisplayPictureHandler = this.beforshowDisplayPicture.bind(this);
                this.beforshowPlayVideoHandler = this.beforshowPlayVideo.bind(this);
                this.beforshowRecordCalendarHandler = this.beforshowRecordCalendar.bind(this);
                this.pagecreateRecordCalendarHandler = this.pagecreateRecordCalendar.bind(this);
                this.btnSwitchVideoChnHandler = this.btnSwitchVideoChn.bind(this);
                this.btnSelectAllHandler = this.btnSelectAll.bind(this);
                this.checkLogTypeHandler = this.checkLogType.bind(this);
                this.btnSearchLogHandler = this.btnSearchLog.bind(this);
                this.jumpPageHandler = this.jumpPage.bind(this);
                this.btnDownloadHandler = this.btnDownload.bind(this);
                this.btnRefreshHandler = this.btnRefresh.bind(this);
                $(document).on("pagebeforeshow", "#page-play-video", this.beforshowPlayVideoHandler);
                $(document).on("pagebeforehide", "#page-play-video", function(){this.mediaPlayer.pause();}.bind(this));
                break;

            case "config":
                this.switchConfigsHandler = this.switchConfigs.bind(this);
                this.switchChannelConfigHandler = this.switchChannelConfig.bind(this);
                this.switchChnAlgTypeHandler = this.switchChnAlgType.bind(this);
                this.checkDHCPHandler = this.checkDHCP.bind(this);
                this.checkNTPHandler = this.checkNTP.bind(this);
                this.sendJsonHandler = this.sendmediaJson.bind(this);
                this.sendJsonHandler1 = this.sendalgJson.bind(this);
                this.sendJsonHandler2 = this.sendnetworkJson.bind(this);
                this.sendJsonHandler3 = this.sendsysJson.bind(this);
                this.checkPdAlarmAutoHandler = this.enablePdAlarmOutAuto.bind(this);
                this.checkIrPdAlarmAutoHandler = this.enableIrPdAlarmOutAuto.bind(this);
                this.checkZoomAlarmAutoHandler = this.enableZoomAlarmOutAuto.bind(this);
                this.btnSubmitHandler = this.btnSubmit.bind(this);
                this.selectDMSsensitivityHandler = this.selectDMSsensitivity.bind(this);
                this.selMainStreamEncodeHandler = this.selMainStreamEncode.bind(this);
                this.selMainStremRcModeHandler = this.selMainStremRcMode.bind(this);
                this.selectRecordModeHandler = this.selectRecordMode.bind(this);
                this.switchSerNetTypeHandler = this.switchSerNetType.bind(this);
                this.enableServerHandler = this.enableServer.bind(this);
                this.btnCancelHandler = this.btnCancel.bind(this);
                this.btnRefreshHandler = this.btnRefresh.bind(this);
                this.inputCharNormalHandler = this.inputCharNormal.bind(this);
                this.switchApFrequencyHandler = this.switchApFrequency.bind(this);
                this.switchMainFramerateHandler = this.switchMainFramerate.bind(this);
                this.switchSubFramerateHandler = this.switchSubFramerate.bind(this);
                break;
			case "system":
                this.btnImportConfigHandler = handleImportConfig.bind(this);
                this.inputArticleNumHandler = this.inputArticleNum.bind(this);
                this.inputspSerialNumHandler = this.inputspSerialNum.bind(this);
				this.btnExportConfigHandler = handleExportConfig;
                this.btnExportLogFileHandler = handleExportLogFile;
				break;
        }

        return this;
    },

    enable: function (page) {
        page = page || this.page;
        this.$btnAskForSure.unbind().click(this.btnAskForSureHandler);
        this.$btnAskForCancel.unbind().click(this.$btnAskForCancelHandler);
        this.$btnFormInput2.unbind().click(this.btnFormInput2Handler);
        this.$btnFormInput3.unbind().click(this.btnFormInput3Handler);
	    this.$btnFormInput4.unbind().click(this.btnFormInput4Handler);
        this.$btnPopup.unbind().click(this.popupTriggerHandler);
        switch (page) {
            case "preview":
                this.$btnLogout.unbind().click(this.btnLogoutHandler);
                this.$btnCancelCalibration.unbind().click(this.btnCancelCalibrationHandler);
                this.$btnTheme.unbind().click(this.btnThemeHandler);
                this.$selRoiStyle.change(this.selRoiStyleHandler);
                this.$roiBoardColorSelect.change(this.roiBoardChangeHandler);
                this.$roiBoardColorScale.change(this.roiBoardChangeHandler);
                this.$roiBoardHiderOther.change(this.roiBoardChangeHandler);
                this.$selLang.unbind().change(this.selLangHandler);
                this.$selCaliMode.change(this.selCaliModeHandler);
                this.$selAPCDirection.change(this.$selAPCDirectionHandler);
                this.$btnDrawRoi.unbind().click(this.btnDrawRoiHandler);
                this.$btnDrawHollow.unbind().click(this.btnDrawHollowHandler);
                this.$selHollow.change(this.selHollowHandler);
                this.$selIconPos.unbind().change(this.selIconPosHandler);
                this.$camImg.unbind().click(this.imgClickHandler);
                this.$btnCalibration.unbind().click(this.btnCalibrationHandler);
                this.$btnSubmitCalibrationBoard.unbind().click(this.btnSubmitCalibrationBoardHandler);
                this.$btnCalibrationStartConfirm.unbind().click(this.btnCalibrationStartConfirmHandler);
                this.$btnCalibrationStartCancel.unbind().click(this.btnCalibrationStartCancelHandler);
                this.$btnRegister.unbind().click(this.btnRegisterHandler);
				this.$btnFrsDivBack.unbind().click(this.btnFrsDivBackHandler);
				this.$btnFrstestLogin.unbind().click(this.btnFrstestLoginHandler);
                this.$btnFrsnewUser.unbind().click(this.btnFrsnewUserHandler);
                this.$btnDeleteUser.unbind().click(this.btnDeleteUserHandler);
                this.$inputDeleteAll.unbind().change(this.inputDeleteAllHandler);
                this.$btnFullscreen.unbind().click(this.btnFullscreenHandler);
                this.$btnGetTrackPos.unbind().click(this.btnGetTrackPosHandler);
                this.$btnSetZoom.unbind().click(this.btnSetZoomHandler);
                this.$selSensitivity.unbind().change(this.selSetSensitivityHandler);
                this.$btnStreamType.unbind().click(this.changeStreamTypeHandler);
                this.$btnshelterEnable.unbind().change(this.btnshelterEnableHandler);
                this.$btnZoomAry.unbind().mousedown(this.btnZoomAryHandler);				
                this.$btnZoomAry.unbind("mouseup", this.btnZoomAryStopHandler).mouseup(this.btnZoomAryStopHandler);
                this.$btnZoomAry.unbind("touchend", this.btnZoomAryStopHandler).touchend(this.btnZoomAryStopHandler);				
                this.$btnSwitchStream.unbind().click(this.btnSwitchStreamHandler);
                this.$btnImageParam.unbind().click(this.btnImageParamHandler);
                //this.$sliImageParam.unbind("mouseup", this.sliImageParamHandler).mouseup(this.sliImageParamHandler);
                this.$imageParamChange.unbind("change",this.sliImageParamHandler).change(this.sliImageParamHandler);
                this.$algParamChange.unbind("change",this.sliAlgParamHandler).change(this.sliAlgParamHandler);
                this.$imageProcessMirror.unbind("click",this.MirrorEnableHandler).click(this.MirrorEnableHandler);
                this.$imageProcessFilp.unbind("change",this.FilpEnableHandler).change(this.FilpEnableHandler);
                this.$btnImportFaceId.unbind().change(this.btnImportFaceIdHandler);
                this.$btnExportFaceId.unbind().click(this.btnExportFaceIdHandler);
                this.$ptzBtn.unbind().mousedown(this.ptzStartHandler);
                this.$ptzBtn.unbind("mouseup", this.ptzStopHandler).mouseup(this.ptzStopHandler);
                this.$ptzBtn.unbind("touchstart",this.ptzStartHandler).touchstart(this.ptzStartHandler);
                this.$ptzBtn.unbind("touchend", this.ptzStopHandler).touchend(this.ptzStopHandler);
                this.$freemodeEnable.unbind().change(this.$freemodeEnableHandler);
                this.$btnTestPreview.unbind().click(this.btnTestPreviewHandler);
                document.addEventListener("fullscreenchange", this.fullscreenChangeHandler);
                document.addEventListener("mozfullscreenchange", this.fullscreenChangeHandler);
                document.addEventListener("webkitfullscreenchange", this.fullscreenChangeHandler);
                document.addEventListener("msfullscreenchange", this.fullscreenChangeHandler);
                $(window).resize(this.windowResizeHandler);
                this.adaptScreen();
                break;

            case "device":
                this.$btnNav.unbind().click(this.switchDeviceHandler);
                this.$btnRefresh.unbind().click(this.btnRefreshHandler);
                this.$btnPing.unbind().click(this.btnPingHandler);
                this.$btnOuput.unbind().click(this.btnOuputHandler);
                $(document).on("swipeleft", ".ui-page", this.swipeLeftHandler);
                $(document).on("swiperight", ".ui-page", this.swipeRightHandler);
                //this.downScroll.setCallback(this.btnRefreshHandler);
                break;

            case "query":
                this.$btnNav.unbind().click(this.switchQueryHandler);/*这里的unbind().我去掉了，不然会去除自定义控件的click事件*/
                this.$pageChange.unbind().click(this.pageChangeHandler);
                this.$recordFilter.unbind().click(this.recordFilterHandler);
                this.$selTimeType.change(this.switchTimeTypeHandler);
                this.$btnQuery.unbind().click(this.queryHandler);
                this.$btnDisplayPicture.unbind().click(this.btnDisplayPictureHandler);
                this.$btnPlayVideo.unbind().click(this.btnPlayVideoHandler);
                this.$btnRefreshPicture.unbind().click(this.btnRefreshPictureHandler);
                this.$btnRequestPlayback.unbind().click(this.btnRequestPlaybackHandler);
                this.$btnSurePlayback.unbind().click(this.btnSurePlaybackHandler);
                this.$btnSwitchVideoChn.unbind().click(this.btnSwitchVideoChnHandler);
                this.$btnSelectAll.unbind().click(this.btnSelectAllHandler);
                this.$checkLogType.unbind().click(this.checkLogTypeHandler);
                this.$btnSearchLog.unbind().click(this.btnSearchLogHandler);
                this.$jumpPage.unbind().click(this.jumpPageHandler);
                this.$btnDownload.unbind().click(this.btnDownloadHandler);
                this.$btnRefresh.unbind().click(this.btnRefreshHandler);
                $(document).on("pagebeforeshow", "#page-query", this.beforeshowQueryHandler);
                $(document).on("pagebeforeshow", "#page-display-picture", this.beforeshowDisplayPictureHandler);
                $(document).on("pagebeforeshow", "#page-play-video", this.beforshowPlayVideoHandler);
                $(document).on("pagebeforehide", "#page-play-video", function(){this.mediaPlayer.pause();}.bind(this));
                $(document).on("pagebeforeshow", "#page-record-calendar", this.beforshowRecordCalendarHandler);
                $(document).on("pagecreate", "#page-record-calendar", this.pagecreateRecordCalendarHandler);
                break;

            case "config":
                this.$btnNav.click(this.switchConfigsHandler);/*这里的unbind().我去掉了，不然会去除自定义控件的click事件*/
                this.$btnSelChn.click(this.switchChannelConfigHandler);/*这里的unbind().我去掉了，不然会去除自定义控件的click事件*/
                this.$selChnAlg.change(this.switchChnAlgTypeHandler);
                this.$checkDHCP.unbind().click(this.checkDHCPHandler);
                this.$checkNTP.unbind().click(this.checkNTPHandler);
                this.$sendmediaJson.click(this.sendJsonHandler);
                this.$sendalgJson.click(this.sendJsonHandler1);
                this.$sendnetworkJson.click(this.sendJsonHandler2);
                this.$sendsysJson.click(this.sendJsonHandler3);
                this.$checkPdAlarmAuto.unbind().click(this.checkPdAlarmAutoHandler);
                this.$checkIrPdAlarmAuto.unbind().click(this.checkIrPdAlarmAutoHandler);
                this.$checkZoomAlarmAuto.unbind().click(this.checkZoomAlarmAutoHandler);
                this.$btnSubmit.unbind().click(this.btnSubmitHandler);
                this.$btnCancel.unbind().click(this.btnCancelHandler);
                this.$selectDMSsensitivity.change(this.selectDMSsensitivityHandler);
                this.$selectRecordMode.change(this.selectRecordModeHandler);
                this.$selMainStreamEncode.change(this.selMainStreamEncodeHandler);
                this.$selMainStreamRcMode.change(this.selMainStremRcModeHandler);
                this.$btnRefresh.unbind().click(this.btnRefreshHandler);
                this.$inputCharNormal.unbind().keyup(this.inputCharNormalHandler);
                this.$selSerNetType.unbind().change(this.switchSerNetTypeHandler);
                this.$btnServerEnable.unbind().click(this.enableServerHandler);
                //this.downScroll.setCallback(this.btnRefreshHandler);
                this.$selApFrequency.change(this.switchApFrequencyHandler);
                this.$selMainStreamFramerate.change(this.switchMainFramerateHandler);
                this.$selSubStreamFramerate.change(this.switchSubFramerateHandler);
                $(document).on("selectmenucreate", ".filterable-select", this.onSelectmenucreate);
                $(document).on("pagecontainerbeforeshow", this.onPagecontainerbeforeshow);
                $(document).on("pagecontainerhide", this.onPagecontainerhide);
                break;
			case "system":
                this.$btnImportConfig.unbind().change(this.btnImportConfigHandler);
                this.$inputArticleNum.unbind().keydown(this.inputArticleNumHandler);
                this.$inputspSerialNum.unbind().keydown(this.inputspSerialNumHandler);
				this.$btnExportConfig.unbind().click(this.btnExportConfigHandler);
                this.$btnExportLogFile.unbind().click(this.btnExportLogFileHandler);
                
				break;
        }

        return this;
    },

    changeTheme: function (page) {
        page = page || this.page;
        switch (page) {
            case "preview":
                $("#page-home").removeClass("ui-page-theme-a ui-page-theme-b").addClass("ui-page-theme-"+this.theme);
                $(".ui-collapsible-content").removeClass("ui-body-a ui-body-b").addClass("ui-body-"+this.theme);
                break;
            case "device":
                $("#page-device").removeClass("ui-page-theme-a ui-page-theme-b").addClass("ui-page-theme-"+this.theme);
                break;
            case "query":
                $("#page-query").removeClass("ui-page-theme-a ui-page-theme-b").addClass("ui-page-theme-"+this.theme);
                $("#page-play-video").removeClass("ui-page-theme-a ui-page-theme-b").addClass("ui-page-theme-"+this.theme);
                $("#page-display-picture").removeClass("ui-page-theme-a ui-page-theme-b").addClass("ui-page-theme-"+this.theme);
                $("#page-record-calendar").removeClass("ui-page-theme-a ui-page-theme-b").addClass("ui-page-theme-"+this.theme);
                break;
            case "config":
                $("#page-config").removeClass("ui-page-theme-a ui-page-theme-b").addClass("ui-page-theme-"+this.theme);
                break;
            case "system":
                $("#page-system").removeClass("ui-page-theme-a ui-page-theme-b").addClass("ui-page-theme-"+this.theme);
                break;
        }
        if (this.model.getMachineType() == "DMS885N" || this.model.getMachineType() == "DMS31V2" || this.model.getMachineType() == "ADA47V1") {  //加载dms31需要的对话框样式
           $("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/dialog.css"}).appendTo("head");
        }
        
        /*if(this.customer=="200032"){ // luis定制版
            if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                $("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-mobilecommon.css"}).appendTo("head");
            }else {
                $("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/luis-common.css"}).appendTo("head");
            }
        }else{  //通用版
            if ((navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i))) {
                $("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/mobilecommon.css"}).appendTo("head");
            }else {
                $("<link>").attr({ rel: "stylesheet",type: "text/css",href: "./css/common.css"}).appendTo("head");
            }
        }*/
        return this;
    },

    changeLang: function (page) {
        page = page || this.page;
        var itemLang = window.location.host+"-lang";
       
        var lang = this.lang || window.localStorage.getItem(itemLang) || 'EN';
        if(!lang){
            return this;
        }         
        if(lang=="INC" || lang=="TEL") lang="EN";
        $("*").each(function(index, dom){
            var key = $(dom).jqmData("desc");
            if(key){
                $(dom).html(this.model.getKeyLang(key, lang));
            }
        }.bind(this));

        $("a").each(function(index, dom){
            var key = $(dom).jqmData("title");
            if(undefined != key){
                //console.log(this.model.getKeyLang(key, lang));
                $(dom).attr("title",this.model.getKeyLang(key, lang));
            }
        }.bind(this));

        switch (page) {
            case "preview":
                break;
            case "system":
                //$("#restore-type").selectmenu("refresh");
                break;
            case "query":
                /*$("#sel-time-type").selectmenu("refresh");
                $("#sel-relative-time").selectmenu("refresh");
                $("#sel-clip-stat").selectmenu("refresh");*/
                break;
            case "config":
                break;
        }
        return this;
    },

    changeChildrenLang: function (id) {
        if(this.customer == "200032")
        {
            this.model.lang = luis_lang;
        }
        $(id).find("*").each(function(index, dom){
            var key = $(dom).jqmData("desc");
            if(key){
                $(dom).html(this.model.getKeyLang(key, this.lang));
            }
        }.bind(this));
        return this;
    },
    imgClick: function (event) {
        var clk = $(event.target).jqmData("click");
        console.log("imgClick:"+clk);
        if ((this.hardware != "ADA42V1") && (this.hardware != "ADA42PTZV1") && (this.board != 25)) {
            return this;
        }
        var win_id;
        var idx = 0;
        if (clk != "btn-all") {
            win_id = "#cam-win-1x";
            $("#mod-measure").show();
            switch (clk) {
                case "btn-ch1":
                {
                    idx = 0;
                    break;
                }
                case "btn-ch2":
                {
                    idx = 1;
                    $("#mod-measure").hide();
                    this.$selCaliMode.val("normal");
                    break;
                }
                case "btn-ch3":
                {    
                    idx = 2;
                    break;
                }
                case "btn-ch4":
                {
                    idx = 3;
                    break;
                }
            }
        } else if(this.hardware == "ADA42V1") {
            win_id = "#cam-win-4x";    
        } else {
            win_id = "#cam-win-2x";
        }
        this.$camWin4x.hide();
        this.$camWin2x.hide();
        this.$camWin1x.hide();
        if (win_id == "#cam-win-1x") {
            $("#td-btn-calibration").show();
            this.opChn = idx;
            this.imgUrls[4] = this.imgUrls[idx];
            this.$imgSolo[0].src = this.imgUrls[4];
            this.switchCamEvent.notify({"chnNum": 1});
            if (this.hardware == "ADA42PTZV1"){
                if (1 == this.opChn)
                {
                    $("#td-btn-calibration").hide();
                    $(".btn-trackpos").show();
                }
                $(".btn-fullscreen").show();
            }
        } else if(win_id == "#cam-win-2x") {
            this.opChn = 0;
            $("#td-btn-calibration").hide();
            this.switchCamEvent.notify({"chnNum": 2});
            if (this.hardware == "ADA42PTZV1"){
                $(".btn-fullscreen").hide();
                $("#td-btn-calibration").hide();
                $(".btn-trackpos").hide();
            }
        } else {
            this.opChn = 0;
            $("#td-btn-calibration").hide();
            this.switchCamEvent.notify({"chnNum": 4});
        }
        $(win_id).show();
        return this;
    },
    btnLogout: function () {
        console.log("btnLogout");
        if(this.lastCaliMode=="normal" && (this.hardware=="ADA32V2" || this.hardware=="ADA32V3" || this.hardware=="ADA32IR" || this.hardware=="ADA32N1" || this.hardware=="ADA32C4"  || this.hardware=="ADA32E1"))
        {
            var args= this.model.getConfigJson().mediaConfig[window.ch === 'ch0'? 'chn0':'chn1'].showGuiMask | 4 ;  //打开picture strem
            // this.guiMask.notify(args);
            this.$freemodeEnable.prop("checked", false);
	    this.freemode_onOff = false;
            this.changeCaliModeEvent.notify(this.lastCaliMode);
        }      
        this.logoutEvent.notify();
    },

    btnCancelCalibration:function()
    {
        this.CancelCalibration.notify();
    },

    btnTheme: function () {
        var itemTheme = window.location.host+"-theme";
        var oldTheme = this.theme, newTheme;
        if (oldTheme == "a") {
            newTheme = "b";
        } else {
            newTheme = "a";
        }
        console.log("btnTheme:"+oldTheme+"->"+newTheme);
        this.theme = newTheme;
        this.changeTheme();
        window.localStorage && (window.localStorage.setItem(itemTheme, this.theme));
    },

    jumpToLoginPage: function () {
        window.location.href = "./login.html";
    },

    selLang: function () {
        var itemLang = window.location.host+"-lang";
        this.lang = this.$selLang.val();
        console.log("lang:"+this.lang);
        this.selLangEvent.notify(this.lang);
        this.model.setDevLang(this.lang);
        this.changeLang();
        window.localStorage && (this.lang = window.localStorage.setItem(itemLang, this.lang));
    },

     btnFullscreen: function () {
        console.log("btnFullscreen");
        if (!this.isFullscreen) {
            this.enterFullscreen();
        } else {
            this.exitFullscreen();
        }

        return this;
    },

    btnGetTrackPos: function() {
        this.calimode = "getTrackPos";
        console.log("btnGetTrackPos");
        this.$camImg.unbind();
        $("#draw_canvas").show();
        $("#calibration_button").show();
        $(".control-wrapper").show();
        this.$btnSubmitCalibrationBoard.show();
        if(this.calibration_board) this.calibration_board.quit();
        if(this.calibration_screen)this.calibration_screen.quit();
        if(this.calibration_div) this.calibration_div.displayMeasureItems("none");
        this.calibration_board = new CalibrationBoard(document.getElementById("draw_canvas"),1);
        this.calibration_board.reviseMouseX(this.widthpading);
    },

    btnSetZoom: function() {
        $("#btn-zoom-array").show();
    },

    selSetSensitivity:function(){
        console.log("selSensitivity");
        var json = {           
            "algConfig":{
                "pdsConf": [{"pdSensitivity":Number($("#FCT-Set-pdSensitivity").val())}]
            }
        };
        this.algTime && window.clearTimeout(this.algTime);
        this.algTime = window.setTimeout(function(){
            this.submitSensitivityEvent.notify(json);
        }.bind(this), 200);
        return this;
    },

    changeStreamType:function(){
        var itemStream = window.location.host+"-streamType";
        window.localStorage && (this.streamType = window.localStorage.getItem(itemStream));
        this.streamType = this.streamType || 0;
        if(this.streamType == 0)
        {          
            this.streamType = 1;
            console.log(this.streamType);
            $("#rtc_media_player").show();
            $("#img-solo").hide();          
            this.$btnStreamType.text(this.model.getKeyLang("main-stream", this.lang));
        }
        else
        {
            this.streamType = 0;
            $("#rtc_media_player").hide();
            $("#img-solo").show();          
            console.log(this.streamType);
            this.$btnStreamType.text(this.model.getKeyLang("pic-stream", this.lang));
        }
        this.changeStreamTypeEvent.notify(this.streamType);
        window.localStorage && (window.localStorage.setItem(itemStream, this.streamType));
    },

    showStreamType:function(type)
    {
        window.onload = function() {
            console.log(this.$btnStreamType);
            if(type == 0)
            {
                $("#img-solo").show();
                $("#rtc_media_player").hide();
                this.$btnStreamType.text(this.model.getKeyLang("pic-stream", this.lang));
            }
            else
            {
                $("#rtc_media_player").show();
                $("#img-solo").hide();          
                this.$btnStreamType.text(this.model.getKeyLang("main-stream", this.lang));
            }
        }.bind(this);
    },

    btnshelterEnable:function()
    {
        console.log("enableShelter");
        var checkValue = false;
        if(this.$btnshelterEnable.is(":checked"))checkValue = true;
        var json = {           
            "algConfig":{
                "pdsConf": [{"shelterEnable":checkValue}]
            }
        };
        this.algTime && window.clearTimeout(this.algTime);
        this.algTime = window.setTimeout(function(){
            this.submitShelterEvent.notify(json);
        }.bind(this), 200);
        return this;

    },

    btnZoomAryRelease: function(evnet) {
        let btnIdx = $(event.target).jqmData("btn-idx");
        let zoomJson;
        switch (btnIdx){
            case 1:
            case 2:
                zoomJson = {
                    "ptz": {
                        op : 0,
                        "step": {
                            "pStep": 0.0,
                            "tStep": 0.0,
                            "zStep": 0.0
                        },
                        "speed":{
                            "pSpeed": 0.0,
                            "tSpeed": 0.0,
                            "zSpeed": 0.0
                        }
                    }
                };			
                this.ptzJson = zoomJson;      
                this.ptzNotify();       
                break;
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
                break;
        }        
        return this;
    },
    btnZoomAryClick: function(evnet) {
        let btnIdx = $(event.target).jqmData("btn-idx");
        let zoomJson;
        switch (btnIdx){
            case 1:
                zoomJson = {                    
                    "ptz": {
                        op : 5,
                        "step": {
                            "pStep": 0.0,
                            "tStep": 0.0,
                            "zStep": 1.0
                        },
                        "speed":{
                            "pSpeed": 0.0,
                            "tSpeed": 0.0,
                            "zSpeed": 0.0
                        }
                    }
                };   				
                this.ptzJson = zoomJson;
                this.ptzNotify();
                break;
            case 2:
                zoomJson = {
                    "ptz": {
                        op : 6,
                        "step": {
                            "pStep": 0.0,
                            "tStep": 0.0,
                            "zStep": 1.0
                        },
                        "speed":{
                            "pSpeed": 0.0,
                            "tSpeed": 0.0,
                            "zSpeed": 0.0
                        }
                    }
                };
                this.ptzJson = zoomJson;      
                this.ptzNotify();       
                break;
            case 3:
                zoomJson = {                    
                };   
                this.ptzJson = zoomJson; 
                break;
            case 4:
                zoomJson = {
                    "ptz": {
                        op : 14,
                        "step": {
                            "pStep": 0.0,
                            "tStep": 0.0,
                            "zStep": 0.0
                        },
                        "speed":{
                            "pSpeed": 0.0,
                            "tSpeed": 0.0,
                            "zSpeed": 0.0
                        }
                    }
                };
                this.ptzJson = zoomJson;      
                this.ptzNotify();       
                break;
            case 5:
                zoomJson = {
                    "ptz": {
                        op : 15,
                        "step": {
                            "pStep": 0.0,
                            "tStep": 0.0,
                            "zStep": 0.0
                        },
                        "speed":{
                            "pSpeed": 0.0,
                            "tSpeed": 0.0,
                            "zSpeed": 0.0
                        }
                    }
                };
                this.ptzJson = zoomJson;      
                this.ptzNotify();       
                break;
           case 6:
                zoomJson = {
                    "ptz": {
                        op : 16,
                        "step": {
                            "pStep": 0.0,
                            "tStep": 0.0,
                            "zStep": 0.0
                        },
                        "speed":{
                            "pSpeed": 0.0,
                            "tSpeed": 0.0,
                            "zSpeed": 0.0
                        }
                    }
                };
                this.ptzJson = zoomJson;      
                this.ptzNotify();       
                break;
            case 7:
                $("#btn-zoom-array").hide();
                break;
        }        
        return this;
    },
    afCalNotify: function() {
        this.afCalEvent.notify(this.ptzJson);
    },
    selAPCDirection:function(){
        this.apcDirection = this.$selAPCDirection.val();
        if(this.apcScreen != null)
        {
            if(this.apcDirection == 0 || this.apcDirection == 1)   //切换方向，将分割线放到中间，避免上次的位置和已有的矩形位置冲突
            {
                this.apcDivider[1] = (this.astApcDetectionPoints[1] + this.astApcDetectionPoints[3])/2;               
            }
            else
            {
                this.apcDivider[0] = (this.astApcDetectionPoints[0] + this.astApcDetectionPoints[2])/2;
            }
            this.apcScreen.setPoints(this.astApcDetectionPoints,this.apcDivider);
            this.apcScreen.setDirection(this.apcDirection);
            this.apcScreen.show();
        }
    },
    selCaliMode: function () {
        console.log('selCaliMode');
        var itemCaliMode = window.location.host + "-calimode";
        if( this.$selCaliMode.val()=="measure" && this.$selRoiStyle.val()!=0){ //测距模式下，roistyle必须为水平模式
            this.lang = this.model.getDevLang();
            this.$popupShowInfo.find("h1").text(this.model.getKeyLang("RoiStyle Not Support", this.lang));
            this.$popupShowInfo.find("h3").text(this.model.getKeyLang("select Horizontal trapezoid",this.lang));
            this.$popupShowInfo.find("p").text("");
            $("#show-info").trigger("click");
            this.$selCaliMode.val("normal");
        }
        else{
            this.calimode = this.$selCaliMode.val();
            this.changeCaliMode();
        }
        
    },
    changeCaliMode:function(){
        if(this.calimode=="measure"){
            this.$btnSubmitCalibrationBoard.show();
            if(this.calibration_screen) {
                this.calibration_screen.quit();         
                this.calibration_screen = null;
            }
            if(!this.calibration_div) this.calibration_div = new CalibrationDiv(this.pdRoiStyle);
            this.calibration_board = new CalibrationBoard(document.getElementById("draw_canvas"),2);
            this.calibration_div.displayMeasureItems("block"); 
            this.calibration_board.reviseMouseX(this.widthpading);

            //隐藏自由拖拽功能
            $("#switch-freemode-box").hide();
            $(".roi_board_class").hide();

            //隐藏其他roi类型
            $("#vertical_left").remove();
            $("#vertical_right").remove();
            $("#semicircle").remove();
            $("#ellipse").remove();
            $("#draw_board").remove();
            //进入标定界面之后，如果选择test模式，先判断之前是否标定成功，成功则直接填充实际距离并且不弹出提醒框；否则需要发送切换为test模式，并弹出提醒框
           //console.log( this.CalibrationJson.pdWorkMode+" "+ this.CalibrationJson.bCalibrated+" "+ this.CalibrationJson.pdDetectWBorder);
            if(this.CalibrationJson.bCalibrated){
                var points=new Array(); //默认距离
                points.push(this.CalibrationJson.pdDetectWBorder);
                points.push(this.CalibrationJson.pdDetectGreenFBorder);
                points.push(this.CalibrationJson.pdDetectYellowFBorder);
                points.push(this.CalibrationJson.pdDetectRedFBorder);
                this.setDistance(points);
            }
            /*else{
                this.showCaliWarn();          
            }*/
        }else if(this.calimode == "developer"){
            this.$selRoiStyle.append(this.option_roistyle1);
            this.$selRoiStyle.append(this.option_roistyle2);
            if (this.board != 25) this.$selRoiStyleappend(this.option_roistyle3);
            this.$selRoiStyle.append(this.option_roistyle4);
            this.$btnSubmitCalibrationBoard.show();
            if(this.calibration_board) this.calibration_board.quit();
            if(this.calibration_screen)this.calibration_screen.quit();
            if(this.calibration_div) this.calibration_div.displayMeasureItems("none");

            this.calibration_board = new CalibrationBoard(document.getElementById("draw_canvas"),1);
            this.calibration_board.reviseMouseX(this.widthpading);
        }
        else{
            //显示自由拖拽功能
            $("#switch-freemode-box").show();

            this.$selRoiStyle.append(this.option_roistyle1);
            this.$selRoiStyle.append(this.option_roistyle2);
            if (this.board != 25) this.$selRoiStyle.append(this.option_roistyle3);
            if (this.board != 30) this.$selRoiStyle.append(this.option_roistyle4);
            //只在ADA32设备开放画板ROI属性
 this.$selRoiStyle.append(this.option_roistyle5);
            // this.pdHollow.enable = this.$selHollow.val();
            this.pdHollow.enable = 0;
            this.$btnSubmitCalibrationBoard.hide();

            if(this.calibration_screen)this.calibration_screen.quit();
            if(this.calibration_div) this.calibration_div.displayMeasureItems("none");
            if(this.calibration_board) this.calibration_board.quit();
            if(this.calibration_drawboard) this.calibration_drawboard.quit();


            this.calibration_screen = new CalibrationScreen(this.sversion,this.customer,document.getElementById("draw_canvas"));

            this.calibration_screen.setState(this.pdRoiStyle);
            this.calibration_screen.setPoints(this.pdRoiOutline);
            this.calibration_screen.setlang(this.model.getDevLang());
            if(this.pdRoiStyle == 4) this.calibration_screen.setMinorAxis(this.pdMinorAxis); 
            else this.calibration_screen.setFreeMode(false);



           
            var pdHollow = {};
            pdHollow.Scale = 0.0;//默认 0
            pdHollow.style = 1;//默认为任意图形
            pdHollow.enable = this.pdHollow.enable;
            pdHollow.points = this.pdHollow.points; //[0.2,0.4,0.4,0.4,0.6,0.4,0.8,0.4,0.8,0.5,0.8,0.6,0.6,0.6,0.4,0.6,0.2,0.6,0.2,0.5];
            this.calibration_screen.setHollow(pdHollow);

            if(this.pdRoiStyle == 5) $("#switch-freemode-box").hide();
            else $("#switch-freemode-box").show();

            if(this.pdRoiStyle == 5) $(".roi_board_class").show();
            else $(".roi_board_class").hide();

            if(this.customer == "200032" || this.customer == "200889")
            {
                if(this.pdRoiStyle == 5) $("#hollow_div").hide();
                else $("#hollow_div").show();
            }
            
            //if(this.customer=="200598") this.calibration_screen.setFreeMode(true);
            this.calibration_screen.reviseMouseX(this.widthpading);//修正鼠标x坐标
            this.calibration_drawboard = new CalibrationDrawBoard(document.getElementById("draw_canvas"));
            console.log(this.pdRoiBoard);
            this.calibration_drawboard.setPdRoiBoard(this.pdRoiBoard);
            
            console.log(`this.pdRoiBoard:${this.pdRoiBoard.pdRedPoint[0]} `);

            //console.log(this.pdRoiStyle);
            if (this.pdRoiStyle <= 4)
            {
                this.calibration_screen.reset();
                this.calibration_screen.show();
            }
            else
            {
                this.roiBoardReset();
                this.calibration_drawboard.reset();
                this.calibration_drawboard.show();
            }
        }
        // this.changeCaliModeEvent.notify(this.calimode);
    },

    btnCalibration: function () {
        this.option_roistyle1 = $("#vertical_left");
        this.option_roistyle2 = $("#vertical_right");
        this.option_roistyle3 = $("#semicircle");
        this.option_roistyle4 = $("#ellipse");
        this.option_roistyle5 = $("#draw_board");

        $('#change_ch_select').prop('disabled', true);

        console.log('btnCalibration',this.hardware);
        // 移除 camImg 对象的事件处理
        this.$camImg.unbind();
        $("#draw_canvas").show();
        var config = this.model.getConfigJson();
        if (!this.isClickCali) {
            return;
        }
        this.isClickCali = false;
        setTimeout(function () {    
            this.isClickCali = true;
        }.bind(this), 5000);  // 防止 guimask 提交频繁导致多次媒体重构失败
        
        /* 标定模式 */
        $("#calibration_start_div").show();
        /* 自由拖拽框 */
        $("#switch-freemode-box").show();
        /* 镂空ROI区域 */
        if(this.customer == "200032" || this.customer == "200889")
        {
            $("#hollow_div").show();
        }
        if(this.customer == "201244")
        {
            $("#alarmIcon").show();
        }
        /* 显示ROI类型 */
        $("#select-roistyle").show();
        /* 标定确认和取消 */
        $("#calibration_button").show();
        $(".control-wrapper").hide();
        $("#calibration_pre_div").hide();
        this.$freemodeEnable.prop("checked", false);
        this.freemode_onOff = false;
        if (this.hardware == "ADA32V3" || this.hardware == "ADA32IR")
            var args= this.model.getConfigJson().mediaConfig.showGuiMask & 14;  // 关闭 picture stream，ADA32V3的图片流用的是主码流的通道
        else
            
            console.log('this.model.getConfigJson()',this.model.getConfigJson());
            var args= this.model.getConfigJson().mediaConfig.showGuiMask & 11;  // 关闭 picstream
            
        this.lastRoiStyle = this.pdRoiStyle;                                                           
        this.lastCaliMode = this.calimode;
        this.lastPdRoiOutline = this.pdRoiOutline; 
        this.$selRoiStyle.val(this.pdRoiStyle);
        this.$selCaliMode.val(this.calimode);
        // this.guiMask.notify(args);
        this.$selCaliMode.trigger("change");
        this.$btnDrawRoi.trigger("click");   
        return this;
    },

    roiBoardReset:function(){
        if (this.calibration_drawboard == null)
            return ;
        
        var $select = $('#roi-board-color-select');
        var $range = $('#roi-board-color-scale');
        var $checked = $('#roi-board-hide-other');

        var isHideOther =$checked.prop('checked');  // 隐藏状态保留
        var selectedColor = $select.val();          // 保留上一个的选择颜色
        var prePdRoiBoard = this.calibration_drawboard.getPdRoiBoard(); // 修复圆滑系数


        if (selectedColor == 0)
            changeScale = prePdRoiBoard.pdGreenScale;
        else if (selectedColor == 1)
            changeScale = prePdRoiBoard.pdYellowScale;
        else if (selectedColor == 2)
            changeScale = prePdRoiBoard.pdRedScale;

        

        $range.val(changeScale);    // 恢复圆滑系数
        
        this.calibration_drawboard.setRoiChoice(selectedColor); // 恢复上一个状态
        this.calibration_drawboard.setIsHideOther(isHideOther); // 恢复上一个状态

        $range.trigger('input');
        $range.trigger('propertychange');
    },
    roiBoardChange:function(){
        var changeScale;
        var $select = $('#roi-board-color-select');
        var $range = $('#roi-board-color-scale');
        var $checked = $('#roi-board-hide-other');

        var selectedColor = $select.val();
        var rangeValue = parseFloat($range.val());
        var isHideOther =$checked.prop('checked');

        // console.log(`selectedColor${selectedColor}`);
        // console.log(`rangeValue${rangeValue}`);
        // console.log(`isHideOther${isHideOther}`);

        if (this.calibration_drawboard != null)
        {
            var preRoiChoice = this.calibration_drawboard.getRoiChoice();
            var prePdRoiBoard = this.calibration_drawboard.getPdRoiBoard();
            var preIsHideOther = this.calibration_drawboard.getIsHideOther();
            
            if (selectedColor == 0)
                changeScale = prePdRoiBoard.pdGreenScale;
            else if (selectedColor == 1)
                changeScale = prePdRoiBoard.pdYellowScale;
            else if (selectedColor == 2)
                changeScale = prePdRoiBoard.pdRedScale;
            
            if (preRoiChoice != selectedColor)
            {
                this.calibration_drawboard.setRoiChoice(selectedColor);

                $range.val(changeScale);
                $range.trigger('input');
                $range.trigger('propertychange');
            }
            else if (rangeValue != changeScale)
            {
                if (preRoiChoice == 0)
                    prePdRoiBoard.pdGreenScale = rangeValue;
                else if (preRoiChoice == 1)
                    prePdRoiBoard.pdYellowScale = rangeValue;
                else if (preRoiChoice == 2)
                    prePdRoiBoard.pdRedScale = rangeValue;
                this.calibration_drawboard.setPdRoiBoard(prePdRoiBoard);
            }
            
            if (preIsHideOther != isHideOther)
            {
                this.calibration_drawboard.setIsHideOther(isHideOther);
            }
            this.calibration_drawboard.show();
        }

    },


    selRoiStyle:function(){
        this.pdRoiStyle = this.$selRoiStyle.val();
        if(this.pdRoiStyle == 0){
            this.pdRoiOutline = [0.4,0.4,0.3,0.6,0.2,0.8,0.1,1.0,0.6,0.4,0.7,0.6,0.8,0.8,0.9,1.0,0.35,0.7,0.43,0.7,0.35,0.96,0.43,0.96]; 
        }else if(this.pdRoiStyle == 1 || this.pdRoiStyle == 2){
            this.pdRoiOutline = [0.2,0.3,0.4,0.3,0.6,0.3,0.8,0.3,0.2,0.8,0.4,0.8,0.6,0.8,0.8,0.8,0.35,0.7,0.43,0.7,0.35,0.96,0.43,0.96]; 
        }
        else if(this.pdRoiStyle == 3){
            this.pdRoiOutline = [0.5,0.99,0.077058,0.99,0.245304,0.99,0.355801,0.99,0.758955,0.522238,0.763951,0.609408,0.780605,0.9,0.785763,0.99,0.4,0.5,0.55,0.5,0.4,0.85,0.55,0.85]; 
        }
        else if(this.pdRoiStyle == 4)
        {
            this.pdRoiOutline = [0.4,0.4,0.3,0.6,0.2,0.8,0.1,1.0,0.6,0.4,0.7,0.6,0.8,0.8,0.9,1.0,0.35,0.7,0.43,0.7,0.35,0.96,0.43,0.96]
            this.pdMinorAxis =  [0.1,0.1,0.1];  // 抛物线 1/2 短轴的长度
        }

        if(this.pdRoiStyle == 5) $("#switch-freemode-box").hide();
        else $("#switch-freemode-box").show();

        if(this.pdRoiStyle == 5) $(".roi_board_class").show();
        else $(".roi_board_class").hide();
        
        if(this.customer == "200032" || this.customer == "200889")
        {
            if(this.pdRoiStyle == 5) $("#hollow_div").hide();
            else $("#hollow_div").show();
        }

        if (this.pdRoiStyle <= 4)
        {
            if(this.calibration_screen){
                this.calibration_screen.reset();
                this.calibration_screen.setState(this.pdRoiStyle);
                this.calibration_screen.setPoints(this.pdRoiOutline);
                this.calibration_screen.setlang(this.model.getDevLang());
                if(this.pdRoiStyle == 4 ) this.calibration_screen.setMinorAxis(this.pdMinorAxis);
                else this.calibration_screen.setFreeMode(false);
                //if(this.customer=="200598") this.calibration_screen.setFreeMode(true);
                this.calibration_screen.reviseMouseX(this.widthpading);//修正鼠标x坐标
                this.calibration_screen.show();
            }
        }
        else
        {
            if(this.calibration_drawboard)
            {
                $(".hollow_div").hide();
                this.roiBoardReset();
                this.calibration_drawboard.reset();
                this.calibration_drawboard.show();
            }
        }

        if(this.calibration_div){
            this.calibration_div.displayMeasureItems("none");
        }
        if(this.calimode=="developer"){
            if(this.calibration_screen)this.calibration_screen.quit();
            if(this.calibration_div) this.calibration_div.displayMeasureItems("none");
        }
        //$('.btn-calibration').click();
        
    },
    drawRoi:function()  //用于关闭镂空roi绘制使能
    {
        if(this.customer=="200032")
        {
            this.$btnDrawRoi.css("background-color","#ff7500");
            this.$btnDrawHollow.css("background-color","#1f2a44");
            $(".select_button_text").css("color","#ffffff");
            $(".select_button_text").css("border-color","#ff7500");
        }
        else if(this.customer=="201623")
        {
            this.$btnDrawRoi.css("background-color","#ff963b");
            $(".select_button_text").css("color","black");
        }
        else
        {
            this.$btnDrawRoi.css("background-color","var(--theme_color)");
            this.$btnDrawRoi.css("box-shadow","var(--theme_color)");
            this.$btnDrawHollow.css("background-color","white");
            this.$btnDrawHollow.css("box-shadow","var(--theme_color)");
            $(".select_button_text").css("color","black");
            $(".select_button_text").css("border-color","theme_light_color");
        }
        if(this.calibration_screen!=null)
        {
            this.calibration_screen.setDrawEnable(0);
        }
    },
    drawHollow:function()//用于打开镂空roi绘制使能
    {
        if(this.customer=="200032")
        {
            this.$btnDrawHollow.css("background-color","#ff7500");
            this.$btnDrawRoi.css("background-color","#1f2a44");
            $(".select_button_text").css("color","#ffffff");
            $(".select_button_text").css("border-color","#ff7500");
        }
	else
        {
            this.$btnDrawRoi.css("background-color","white");
            this.$btnDrawRoi.css("box-shadow","var(--theme_color)");
            this.$btnDrawHollow.css("background-color","var(--theme_color)");
            this.$btnDrawHollow.css("box-shadow","var(--theme_color)");      
            $(".select_button_text").css("color","black");
            $(".select_button_text").css("border-color","var(--theme_light_color)");
        }
        if(this.calibration_screen!=null)
        {
            this.calibration_screen.setDrawEnable(1);
        }
    },

    selHollow:function()
    {
        this.pdHollow.enable = this.$selHollow.val();
        if(this.calibration_screen!=null)
        {
            var pdHollow = {};
            pdHollow.Scale = 0.0;
            pdHollow.enable = this.pdHollow.enable;
            pdHollow.style = 1;
            pdHollow.points = this.pdHollow.points;//[0.2,0.4,0.4,0.4,0.6,0.4,0.8,0.4,0.8,0.5,0.8,0.6,0.6,0.6,0.4,0.6,0.2,0.6,0.2,0.5];
            console.log(pdHollow);
            this.calibration_screen.setHollow(pdHollow);
        }
        else{
            if(this.calibration_screen)this.calibration_screen.quit();

            this.calibration_screen = new CalibrationScreen(this.sversion,this.customer,document.getElementById("draw_canvas"));
            this.calibration_screen.setState(this.pdRoiStyle);
            this.calibration_screen.setPoints(this.pdRoiOutline);
            this.calibration_screen.setlang(this.model.getDevLang());
            this.calibration_screen.reviseMouseX(this.widthpading);//修正鼠标x坐标

            var pdHollow = {};
            pdHollow.Scale = 0.0;
            pdHollow.style = 1;//this.pdHollowStyle;
            // pdHollow.enable = this.pdHollow.enable;
            pdHollow.enable  = 0
            pdHollow.points = this.pdHollow.points;//[0.2,0.4,0.4,0.4,0.6,0.4,0.8,0.4,0.8,0.5,0.8,0.6,0.6,0.6,0.4,0.6,0.2,0.6,0.2,0.5];
            this.calibration_screen.setHollow(pdHollow);

            this.calibration_screen.show();
        }
    },

    selIconPos:function()
    {
        var args = parseInt(this.$selIconPos.val(),10);
        this.setIconPosEvent.notify(args);
    },

    showCaliWarn: function(){
        this.lang = this.model.getDevLang();  //不知此处为何 this.lang 为空，只能重新获取；
        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("draw-calibration-head", this.lang));
        this.$popupShowInfo.find("h3").text(this.model.getKeyLang("draw-calibration-text",this.lang));
        this.$popupShowInfo.find("p").text("");
        $("#show-info").trigger("click");
    },

    btnSubmitCalibrationBoard:function(){
        var args={};
        /*var p = this.calibration_board.getCalibrationBoardPonits();        
        for(var i=0;i<p.length;i++){
            if(isNaN(p[i])){
                this.showCaliWarn();
                return this;
            }
        }*/
        args.params = [0.2,0.28,0.3,0.46,0.5,0.4,0.7,0.7];
        if(this.calimode == "measure") args.mode = "measure";
        else if(this.calimode == "developer") args.mode = "developer";
        else if(this.calimode == "getTrackPos") args.mode = "getTrackPos";
        this.submitCalibrationBoardEvnet.notify(args);
    },

    setDistance:function(data){
        console.log(data);
        this.calibration_board.clearColor();
        this.calibration_div.setMeasureItemsValue(data);//填充标定实际距离         
     /*   this.calibration_screen = new CalibrationScreen("calibration",this.sversion,this.customer,document.getElementById("draw_canvas"));//measure模式，webui暂时不用绘制roi
        this.calibration_screen.setState(this.pdRoiStyle);
        this.calibration_screen.setPoints(this.pdRoiOutline);
        this.calibration_screen.reviseMouseX(this.widthpading);//修正鼠标x坐标     
        this.calibration_screen.show();*/
        this.calibration_div.setInputChange(this.calibration_screen);
    },

    EnterEvent:function()
    {
        var pdDetectWBorder = parseInt($("#24-input").val(),10);
        var pdDetectRedFBorde = parseInt($("#3-y-input").val(),10);
        var pdDetectYellowFBorder = parseInt($("#2-y-input").val(),10);
        var pdDetectGreenFBorder = parseInt($("#1-y-input").val(),10);
        var json_str;
        var json={"accessApi":{"password":""}, "algConfig":{"pdsConf":[{"pdWorkMode":1,"pdDetectWBorder":pdDetectWBorder,"pdDetectRedFBorder":pdDetectRedFBorde,"pdDetectYellowFBorder":pdDetectYellowFBorder,"pdDetectGreenFBorder":pdDetectGreenFBorder}]}};
        json_str = JSON.stringify(json);
        $.ajax({
            type:"POST",
            url:"/config",
            data:json_str,
            success:function(data){
            }.bind(this),
            error:function(XMLHttpRequest){	
            }.bind(this),
            dataType:"json"
        });
    },

    btnCalibrationStartConfirm: function () {
    	if (this.hardware == "DMS31V2" || this.hardware == "DMS885N" || this.hardware == "ADA47V1"){
			var args={};
			args.mode=2;
            this.calibrationEvent.notify(args);
            //$("#calibration_pre_div").show();
            //$("#calibration_button").hide();
        }
        else if(this.hardware == "ADA900V1")
        {
            if(this.apcScreen){
                var framePoints = this.apcScreen.getFramePoints();
                var dividerPoints = this.apcScreen.getDividerPoint();
                var direciton = this.apcDirection;
                var args={};
                args.direciton=parseInt(direciton,10);
                args.framePoints = framePoints;
                args.diviPoints = dividerPoints;
                this.apcScreen.quit();
                this.apcSetConfigEvent.notify(args);
            }
            $("#calibration_pre_div").show();
            $("#calibration_button").hide();
            $("#apc_direction_div").hide();
            var args= this.model.getConfigJson().mediaConfig.showGuiMask | 4 ; // 打开picture stream
            // this.guiMask.notify(args);
        }
        else
        {    
            if(this.calimode=="measure")
            {
                if(this.CalibrationJson.bCalibrated==true)  //如果是measure模式，必须描绘标定成功才能确定保存
                {
                    if(this.calibration_screen)
                    {
                        var p = this.calibration_screen.getPoints();
                        var s = this.calibration_screen.getStyle();       
                        var axis = this.calibration_screen.getMinorAxis(); 
                        var pdHollow = this.calibration_screen.getHollow();

                        this.pdRoiOutline = p;//更新this.pdRoiOutline，否则还是标定之前的坐标，尽管已经设置到设备端
                        this.pdRoiStyle = s;
                        this.pdMinorAxis = axis;
                        this.pdHollow = pdHollow;
                        var args={};
                        args.mode=32;
                        args.params=p;
                        args.roistyle=parseInt(s,10);
                        args.opChn = this.opChn;                     
                        args.axis = axis;            
                        args.pdHollow = pdHollow;    
                        this.calibration_screen.quit();
                        this.calibrationEvent.notify(args);
                    }
                   this.EnterEvent(); //标定距离输入框，填入数据后需要回车键确定，这里预防没有按回车键，触发一次
                }
                else
                {
                    this.showCaliWarn();
                    return this;
                }
            }      
            else if(this.calimode=="normal")
            {
                if (this.pdRoiStyle <= 4)
                {
                    if(this.calibration_screen)
                    {
                        var p = this.calibration_screen.getPoints();
                        var s = this.calibration_screen.getStyle();       
                        var axis = this.calibration_screen.getMinorAxis();       
                        var pdHollow = this.calibration_screen.getHollow();
                        console.log(pdHollow);
                        this.pdRoiOutline = p;//更新this.pdRoiOutline，否则还是标定之前的坐标，尽管已经设置到设备端
                        this.pdRoiStyle = s;
                        this.pdMinorAxis = axis;
                        this.pdHollow = pdHollow;

                        var args={};
                        args.mode=32;
                        args.params=p;
                        args.roistyle=parseInt(s,10);
                        args.opChn = this.opChn;                         
                        args.axis = axis;
                        args.pdHollow = this.pdHollow;                   

                        this.calibration_screen.quit();
                        this.calibration_drawboard.quit();
                        this.calibrationEvent.notify(args);
                        
                        console.log(args);
                    }
                }
                else
                {
                    var args={};
                    args.mode=32;
                    args.opChn = this.opChn;
                    args.roistyle=parseInt(this.pdRoiStyle,10);
                    console.log(args.roistyle);
                    args.pdRoiBoard = this.calibration_drawboard.getPdRoiBoard();
                    this.pdRoiBoard = args.pdRoiBoard;
                    
                    this.calibrationEvent.notify(args);
                }
            }


            if(this.calibration_div) this.calibration_div.displayMeasureItems("none");
            if(this.calibration_board)this.calibration_board.quit();
            //alert(JSON.stringify(p));
            $("#calibration_start_div").hide();
            $("#switch-freemode-box").hide();
            $(".roi_board_class").hide();
            if(this.customer == "200032" || this.customer == "200889") 
            {
                $("#hollow_div").hide();
            }
            if(this.customer == "201244")
            {
                $("#alarmIcon").hide();
            }
            $("#select-roistyle").hide();
            $("#calibration_pre_div").show();
            $("#calibration_button").hide();
            
            this.$freemodeEnable.prop("checked", false);
	        this.freemode_onOff = false;
            this.$btnSubmitCalibrationBoard.hide();
            this.saveMeasureValue();
            if (this.hardware == "ADA32V3")
                var args= this.model.getConfigJson().mediaConfig.showGuiMask | 1 ; // 打开picture stream，ADA32V3的图片流用的是主码流的通道
            else
                var args= this.model.getConfigJson().mediaConfig.showGuiMask | 4 ; // 打开picture stream
            // this.guiMask.notify(args);
        }
    	if (0 != this.voSplit){
            this.$camImg.unbind().click(this.imgClickHandler);
        }
        $("#draw_canvas").hide();
        $("#change_ch_select").prop('disabled', false);
        return this;
    },

    saveMeasureValue:function(){
        if(this.CalibrationJson.pdWorkMode && this.CalibrationJson.bCalibrated){
            this.CalibrationJson.pdDetectWBorder = parseInt($("#24-input").val(),10);
            this.CalibrationJson.pdDetectRedFBorder = parseInt($("#3-y-input").val(),10);
            this.CalibrationJson.pdDetectYellowFBorder = parseInt($("#2-y-input").val(),10);
            this.CalibrationJson.pdDetectGreenFBorder = parseInt($("#1-y-input").val(),10);
        }
    },
    btnCalibrationStartCancel: function () {
        if (this.hardware == "DMS31V2" || this.hardware == "DMS885N" || this.hardware == "ADA47V1"){
			var args={};
			args.mode=0;
			this.calibrationEvent.notify(args);
            $("#calibration_button").hide();
            $("#calibration_pre_div").show();
            $("#index_menu").show();
            $("#btn-logout").show();
        }
        else if(this.hardware == "ADA900V1")
        {
            if(this.apcScreen) this.apcScreen.quit();
            $("#calibration_pre_div").show();
            $("#calibration_button").hide();
            $("#apc_direction_div").hide();
            var args= this.model.getConfigJson().mediaConfig.showGuiMask | 4 ;  //打开 picture stream
            // this.guiMask.notify(args);
        }
        else{
            $("#calibration_start_div").hide();
            $("#switch-freemode-box").hide();
            if(this.customer == "200032" || this.customer == "200889") 
            {
                $("#hollow_div").hide();
            }
            if(this.customer == "201244")
            {
                $("#alarmIcon").hide();
            }
            $("#select-roistyle").hide();
            $("#calibration_button").hide();
            $(".roi_board_class").hide();
            $("#calibration_pre_div").show();
            this.$btnSubmitCalibrationBoard.hide();
            this.pdRoiOutline = this.lastPdRoiOutline;
            this.pdRoiStyle = this.lastRoiStyle;
            if(this.calimode == "measure" && this.lastCaliMode == "normal")
            {
                this.$selRoiStyle.append(this.option_roistyle1);
                this.$selRoiStyle.append(this.option_roistyle2);
                if (this.board != 25) this.$selRoiStyle.append(this.option_roistyle3);
                if (this.board != 30) this.$selRoiStyle.append(this.option_roistyle4);
                //只在ADA32设备开放画板ROI属性
                if (this.hardware == "ADA32V2" || this.hardware == "ADA32V3" || this.hardware == "ADA32N1" || this.hardware == "ADA32C4" || this.hardware == "ADA32E1") this.$selRoiStyle.append(this.option_roistyle5);
                else $("#draw_board").remove()
            }
            this.calimode = this.lastCaliMode;

            if(this.calibration_screen) this.calibration_screen.quit();
            if(this.calibration_div) this.calibration_div.displayMeasureItems("none");
            if(this.calibration_board)this.calibration_board.quit();
            var args= this.model.getConfigJson().mediaConfig.showGuiMask | 4 ;  //打开 picture stream
            // this.guiMask.notify(args);
            // this.changeCaliModeEvent.notify(this.calimode);
        }
        if (0 != this.voSplit){
            this.$camImg.unbind().click(this.imgClickHandler);
        }
        $("#draw_canvas").hide();
        $("#change_ch_select").prop('disabled', false);
        return this;
    },

    drawAPCDetection:function(){
       
        this.$selAPCDirection.val(this.apcDirection);
        this.apcScreen = new APCDetectionScreen(document.getElementById("draw_canvas"));
        this.apcScreen.setPoints(this.astApcDetectionPoints,this.apcDivider);
        this.apcScreen.setDirection(this.apcDirection);
        this.apcScreen.reviseMouseX(this.widthpading);//修正鼠标x坐标
        this.apcScreen.show();

    },

    btnRegister: function () {
        $(".input-label").find("div").removeClass("ui-input-text");
        $(".index_hide_group").css("display", "none");
        $(".index_frs_group").css("display", "block");
        $("#face_footer").css("display", "block");
        this.registerEvent.notify({"command":"getUsers"});
        return this;
    },

	btnFrsDivBack: function () {
        $(".index_hide_group").css("display", "block");
        $(".index_frs_group").css("display", "none");	
        $("#face_footer").css("display", "none");
        document.getElementById("check-all").checked = false;
        $("#div-checkall").hide();
        return this;
    },

	btnFrstestLogin: function () {
		this.registerEvent.notify({"command":"frLogin"});	
        return this;
    },
		
	btnFrsnewUser: function () {
        var this_t=this;
        var lang = $("#sel-lang").val();
        createInputDialog(
			this_t.model.getKeyLang("edit", lang),
			[this_t.model.getKeyLang("confirm", lang),this_t.model.getKeyLang("cancel", lang)],
			[function (name) {
				if(name == "valueIsEmpty"){
					removeInputDialog();
					createNormalDialog(this_t.model.getKeyLang("empty", lang),[this_t.model.getKeyLang("confirm", lang)],[function(){removeNormalDialog("hidecover");}]);
				}else{
	            	var reg = new RegExp("[\\s`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】\\\\‘；：”“'。，、？]");
    				if(reg.test(name) || name.indexOf("\\") >= 0){
        				removeInputDialog();
        				createNormalDialog(this_t.model.getKeyLang("Illega", lang),[this_t.model.getKeyLang("confirm", lang)],[function(){removeNormalDialog("hidecover");}]);
					}else{
                        var namedOK = true;
                        var divArr = $('#userList div');
						$.each(divArr,function(i,n){
							var divNode = $(this)
                            var haduser = divNode.find(".username").text();
                            if(haduser == name){
                                namedOK=false;
                                this_t.$popupShowInfo.find("h1").text(this_t.model.getKeyLang("Named Error",lang));
                                this_t.$popupShowInfo.find("h3").text(this_t.model.getKeyLang("Name Exists",lang));
                                this_t.$popupShowInfo.find("p").text("");
                                this_t.$popupShowInfo.find("a").click(function(){
                                    removeInputDialog("hidecover");
                                });
                                $("#show-info").trigger("click");                   
                            }
                        });
                        if(namedOK){
                            removeInputDialog("hidecover");
						    this_t.registerEvent.notify({"command":"addUser", "userName":name});
                        }		         		
					}
				}	
			},
			function () {
				removeInputDialog("hidecover");
			}]
		);
        return this;
    },

    btnDeleteUser:function(){
        $(".deleteuser_checkbox").show();
        $("#div-checkall").show(); 
        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("deleteUser",this.lang));
        this.$popupShowInfo.find("h3").text(this.model.getKeyLang("checkUser",this.lang));
        this.$popupShowInfo.find("p").text("");
        $("#show-info").trigger("click");
        this.$btnDeleteUser.unbind().click(this.popupTriggerHandler);
        
    },

    btnSwitchStream:function (event) {
        console.log("btnSwitchStream:"+$(event.target).val());
        var host = window.location.host.slice(0, window.location.host.lastIndexOf(":8080"));
        if ($(event.target).val() == "snap0.jpeg") {
            this.imgWidth = 1920;
            this.imgHeight = 1080;
        } else {
            this.imgWidth = 704;
            this.imgHeight = 480;
        }
        this.imgUrls[4] = "http://"+host+":8081/"+$(event.target).val()+"?quality="+this.imgQuality;
        this.imgwsUrl = "ws://"+host+":8083/"+$(event.target).val()+"?quality="+this.imgQuality;
        this.adaptScreen();
        return this;
    },

    btnSwitchCam: function (event) {
        var win_id = "#"+$(event.target).jqmData("show-win");
        console.log("btnSwitchCam");
        this.$camWin4x.hide();
        this.$camWin1x.hide();
        if (win_id == "#cam-win-1x") {
            var idx = Number($(event.target).jqmData("img-idx"));
            this.imgUrls[4] = this.imgUrls[idx];
            this.$imgSolo[0].src = this.imgUrls[4];
            this.switchCamEvent.notify({"chnNum": 1});
        } else {
            this.switchCamEvent.notify({"chnNum": 4});
        }
        $(win_id).show();

        return this;
    },

    btnImageParam: function () {
        console.log("btnImageParam");
        this.getImageParamEvent.notify();
        return this;
    },

    sliImageParam: function (e) {
        console.log("sliImageParam");
        var json = {
            "imageParam": {
                "brightness": Number($("#brightness").val()),
                "contrast": Number($("#contrast").val()),
                "saturation": Number($("#saturation").val()),
                "sharpness": Number($("#sharpness").val())
            }
        };       
        this.imgTime && window.clearTimeout(this.imgTime);
        this.imgTime = window.setTimeout(function(){
            this.setImageParamEvent.notify(json);          
        }.bind(this), 200);
        return this;
    },

    sliAlgParam:function()
    {
        console.log("sliAlgParam");
        var json = {           
            "algConfig":{
                "audioVolume": Number($("#alg-volume").val())
            }
        };
        this.algTime && window.clearTimeout(this.algTime);
        this.algTime = window.setTimeout(function(){
            this.submitSliAlgParamEvent.notify(json);
        }.bind(this), 200);
        return this;

    },

    imageMirrorEnable:function()
    {
        console.log("imageMirrorEnable");
        var checkValue = this.model.getConfigJson().mediaConfig.mirrorEnable;
        if(this.$imageProcessMirror.is(":checked"))checkValue = true;
        else checkValue = false;
        var json = {           
            "mediaConfig":{
                "imageProcess": {
                    "mirrorEnable":checkValue
                }
            }
        };
        this.submitImageProcessEvent.notify(json);
        return this;

    },

    imageFilpEnable:function()
    {
        console.log("imageFilpEnable");
        var checkValue = this.model.getConfigJson().mediaConfig.flipEnable;
        if(this.$imageProcessFilp.is(":checked"))checkValue = true;
        else checkValue = false;
        var json = {           
            "mediaConfig":{
                "imageProcess": {
                    "flipEnable":checkValue
                }
            }
        };
        this.submitImageProcessEvent.notify(json);
        return this;
    },

    fullscreenChange: function() {
        var isSupportFull = document.fullscreenEnabled || document.msFullscreenEnabled; //当前浏览器是否支持全屏显示
        if (!isSupportFull) {
            console.log("Not support!");
            this.exitFullscreen();
        }
        var isFull = document.fullScreen || document.mozFullScreen || document.webkitIsFullScreen || document.msFullscreenEnabled;
        if (!isFull && this.isFullscreen) {
            console.log("exit fullscreen");
            this.exitFullscreen();
        }
    },

    adaptScreen: function () {
        var width = $(window).width();
        var height = $(window).height();
        if (!this.isFullscreen) {
            height -= 200;
        }
        var pading = "0px";
        var pading10px = "10px";
        var c_width = "0px";
        if (height / width < this.imgHeight / this.imgWidth) { //浏览器窗口宽度大于视频画面宽度
            var dw = width - (height * this.imgWidth / this.imgHeight);
            pading = Math.round(dw/2)+"px";
            pading10px = Math.round(dw/2)+10+"px";
            c_width = ($("#cam-win-1x").width()-Math.round(dw))+"px"; 
            this.widthpading= Math.round(dw/2);//限定视频画面宽度不变，但鼠标的x坐标仍然会随着浏览器窗口宽度变大而偏移，因此mouseX要减去这个pading
        } else {
            pading = "0px";
            pading10px = "10px";
            c_width = width+"px";
            this.widthpading=0;
        }
        $(".win-chn").css({"padding-left":pading, "padding-right":pading});
        $("#draw_canvas").css({"left":pading, "width":c_width});
        $(".div_warn").css({"left":pading10px});
        return this;
    },

    enterFullscreen: function () {
        var dom = document.getElementById("win-img");
        if (dom.requestFullscreen) {
            dom.requestFullscreen();
        } else if (dom.mozRequestFullScreen) {
            dom.mozRequestFullScreen();
        } else if (dom.webkitRequestFullScreen) {
            dom.webkitRequestFullScreen();
        } else if (dom.msRequestFullscreen) {
            dom.msRequestFullscreen();
        }
        this.$uiHeader.hide();
        this.$uiNavbar.hide();
        //this.$uiFooter.hide();
        $("#win-img").css("background-color", "#F9F9F9");
        //this.$btnFullscreen.css("background", "url(./css/images/default-skin.png) -44px 0 no-repeat");
        this.isFullscreen = true;
        this.adaptScreen();

        return this;
    },

    exitFullscreen: function () {
        var dom = document.getElementById("win-img");
        this.$uiHeader.show();
        this.$uiNavbar.show();
        //this.$uiFooter.show();
        if(dom.exitFullscreen){
            dom.exitFullscreen();
        }
        else if(dom.mozCancelFullScreen){
            dom.mozCancelFullScreen();
        }
        else if(dom.msExitFullscreen){
            dom.msExiFullscreen();
        }
        else if(dom.webkitCancelFullScreen){
            dom.webkitCancelFullScreen();
        }
        if (this.customer == "200032") {
            $("#win-img").css("background-color", "#1F2A44");
            //this.$btnFullscreen.css("background", "url(./css/images/default-skin-black.png) 0 0 no-repeat");
        } else {
            $("#win-img").css("background-color", "#F9F9F9");
            //this.$btnFullscreen.css("background", "url(./css/images/default-skin.png) 0 0 no-repeat");
        }
        this.isFullscreen = false;
        this.adaptScreen();

        return this;
    },
    ptzTopStart: function(e) {
        var ptz = $(event.target).jqmData("ptz");
        console.log("ptzTopStart:"+ptz);
        var ptzJson = {
            op:0,
            "ptz": {
                "step": {
                    "pStep": 0.0,
                    "tStep": 0.0,
                    "zStep": 0.0
                },
                "speed":{
                    "pSpeed": 0.0,
                    "tSpeed": 0.0,
                    "zSpeed": 0.0
                }
            }
        };
        switch (ptz) {
            case "up":
                ptzJson.ptz.op = 1;
                ptzJson.ptz.step.tStep = 0.1;
                ptzJson.ptz.speed.tSpeed = 0.1;
                break;
            case "down":
                ptzJson.ptz.op = 2;
                ptzJson.ptz.step.tStep = -0.1;
                ptzJson.ptz.speed.tSpeed = -0.1;
                break;
            case "left":
                ptzJson.ptz.op = 3;
                ptzJson.ptz.step.pStep = -0.1;
                ptzJson.ptz.speed.pSpeed = -0.1;
                break;
            case "right":
                ptzJson.ptz.op = 4;
                ptzJson.ptz.step.pStep = 0.1;
                ptzJson.ptz.speed.pSpeed = 0.1;
                break;
            case "zoomUp":
                ptzJson.ptz.op = 5;
                ptzJson.ptz.step.zStep = 0.1;
                ptzJson.ptz.speed.zSpeed = 0.1;
                break;
            case "zoomDown":
                ptzJson.ptz.op = 6;
                ptzJson.ptz.step.zStep = -0.1;
                ptzJson.ptz.speed.zSpeed = -0.1;
                break;
        }
        this.ptzJson = ptzJson;
        this.ptzNotify();

    },

    ptzTopStop: function(e) {
        console.log("ptzTopStop");
         let ptzJson = {
            op:0,
            "ptz": {
                "step": {
                    "pStep": 0.0,
                    "tStep": 0.0,
                    "zStep": 0.0
                },
                "speed":{
                    "pSpeed": 0.0,
                    "tSpeed": 0.0,
                    "zSpeed": 0.0
                }
            }
        };

        this.ptzJson = ptzJson;
        this.ptzNotify();
    },

    ptzNotify: function() {
        this.ptzEvent.notify(this.ptzJson);
    },
    
    getWarnSate:function(){
            var host;
            var url;
        //f(this.sversion=="ADA32_HW") {
            //document.getElementById("warning_text").innerHTML = "dmmWarning:-";
            if(window.location.host.indexOf("8443")!=-1)
            {
                host = window.location.host.slice(0, window.location.host.lastIndexOf(":8443"));
                url = "wss://"+host+":8447/ws";
            }
            else if(window.location.host.indexOf("8080")!=-1)
            {
                host = window.location.host.slice(0, window.location.host.lastIndexOf(":8080"));
                url= "ws://"+host+":8085/ws";
            }
            var ws = new WebSocket(url);
            ws.onopen = function (ev) {
                console.log(ev);
                ws.send("get_warning");
            };
            ws.onerror = function (ev) { console.log(ev); };
            ws.onclose = function (ev) { console.log(ev); };
            ws.onmessage = function (ev) {
                var warn = JSON.parse(ev.data);
                console.log(warn);
                //this.$txtWarn.text(warn.Warning);
                if (warn.code == 0) {
                    this.$divWarn.css("display","none").trigger("create");
                } else {
                    this.$divWarn.css("display","block").trigger("create");
                }
            }.bind(this);
        //}
        return this;
    },

    startWatchApc:function(){
        var host;
        var url;
        //f(this.sversion=="ADA32_HW") {
        //document.getElementById("warning_text").innerHTML = "dmmWarning:-";
        if(window.location.host.indexOf("8443")!=-1)
        {
            host = window.location.host.slice(0, window.location.host.lastIndexOf(":8443"));
            url = "wss://"+host+":8447/ws";
        }
        else if(window.location.host.indexOf("8080")!=-1)
        {
            host = window.location.host.slice(0, window.location.host.lastIndexOf(":8080"));
            url= "ws://"+host+":8085/ws";
        }
        var ws = new WebSocket(url);
        ws.onopen = function (ev) {
            console.log(ev);
            ws.send("get_warning");
        };
        ws.onerror = function (ev) { console.log(ev); };
        ws.onclose = function (ev) { console.log(ev); };
        ws.onmessage = function (ev) {
            var warn = JSON.parse(ev.data);
            console.log(warn);
            $("#apc-in").val(warn.In);
            $("#apc-out").val(warn.Out);
            $("#apc-rect").val(warn.Rect);
        }.bind(this);
        $("#btn-apc").unbind().click(function () {
            console.log("btn-apc:"+$("#apc-total").val());
            ws.send("set_totalNum="+$("#apc-total").val()+"&set_InNum="+$("#apc-in").val()+"&set_OutNum="+$("#apc-out").val());
            this.showSuccessInfo("setting-success"); 
        }.bind(this));
        //}
        return this;
    },

    refreshPreview: function () {
        var height = 0;
        if (!this.$camWin4x.is(":hidden")) {
            this.$camImg.each(function(i, d){
                (i < this.chnNum) && d.complete && (d.src = this.imgUrls[i]+"?total_chn_mask=f&random="+Math.random());
            }.bind(this));
        }

        if (!this.$camWin2x.is(":hidden")) {
            this.$camImg.each(function(i, d){
                (i>3) && (i < 6) && d.complete && (d.src = this.imgUrls[i-4]+"?total_chn_mask=f&random="+Math.random());
            }.bind(this));
        }
        if (!this.$camWin1x.is(":hidden")) {
            if (this.$imgSolo[0].complete) {
                const idx = window.ch === 'ch0' ? 0 : 1;
                (this.$imgSolo[0].src = this.imgUrls[idx]+"?random="+Math.random());
            }
        /*   
            if(!this.isFullscreen)
            {
                //以下是对旋转后的画面进行缩小处理
                $(".index_hide_group").each(function (i, n) {
                    height += $(this).height();
                })           
                var maxHeight = window.innerHeight - height;
                var img_w = $("#img-solo").width();
                var img_h = $("#img-solo").height();
                if((img_h > maxHeight) && (img_h < img_w))
                {
                    var w = maxHeight / 16 * 9;
                    $("#img-solo").css("width", w); 
                    $("#img-solo").css("height", maxHeight); 
                }
            }
        */    
        }

        if (window.localStorage.getItem('freshUserList') === 'true') {	           
            try {
                var displayStyle = document.getElementById('calibration_pre_div').style.display; 
            } catch (e) {
                return this;
            }
                
            if (displayStyle === 'none') {
                //console.log('该div的display属性等于none。');                
            } else {
                //console.log('该div的display属性不等于none。');
                this.btnRegister();
                window.localStorage.removeItem('freshUserList');
            }
        }
        return this;
    },

    refreshImageParam: function (data) {
        $("#brightness").val(data.imageParam.brightness).slider('refresh');
        $("#contrast").val(data.imageParam.contrast).slider('refresh');
        $("#saturation").val(data.imageParam.saturation).slider('refresh');
        $("#sharpness").val(data.imageParam.sharpness).slider('refresh');
        return this;
    },

    refreshRecBlink: function () {
        var show;
        if (this.blinkStat == "off") {
            show = true;
            this.blinkStat = "on";
        } else {
            show = false;
            this.blinkStat = "off";
        }
        this.$tagRec.each(function(i,d){
            var chn = Number($(d).jqmData("map-chn"));
            if (chn > 0 && chn <= 4) {
                if (this.statJson.recChnList[chn-1]) {
                    if (show) {
                        $(d).css("background", "red");
                    } else {
                        $(d).css("background", "none");
                    }
                } else {
                    $(d).css("background", "none");
                }
            }
        }.bind(this));
        return this;
    },

    iosSelectDeal: function()
    {
        if(this.hardware !="ADA32V2" && this.hardware != "ADA32C4") $("#sel-lang").find("option[value=TUR]").remove();
        if(this.customer !="201876" && this.customer !="202116") $("#sel-lang").find("option[value=INC]").remove();
        if(this.customer !="201876") $("#sel-lang").find("option[value=TEL]").remove();
        if((this.hardware == "DMS31V2") || (this.hardware == "DMS885N")) $("#sel-lang").find("option[value=DG]").remove();
        if((this.customer !="200032") || (this.hardware == "DMS31V2") || (this.hardware == "DMS885N")) $("#sel-lang").find("option[value=ITA]").remove();
        if((this.hardware =="DMS31V2") || (this.hardware == "DMS885N")) $("#sel-lang").find("option[value=FRA]").remove();
    },

    inputArticleNum:function(event)
    {
        if(window.event.keyCode==13)
        {
            var articleNum = $("#ipcIdentification-articleNum").val();
            var json_str;
            var json;
            json = {"accessApi": {"password":""},"articleNum":articleNum};
            json_str = JSON.stringify(json);
            console.log(json_str);
            $.ajax({
                type:"POST",
                url:"/change_artnum",
                data:json_str,
                success:function(){          
                    this.showSuccessInfo("setting-success"); 
                }.bind(this),
                error:function(XMLHttpRequest){
                    this.view.hideLoading();
                }.bind(this),
                dataType:"json"
            });
        }

    },

    inputspSerialNum:function(event)
    {
        if(window.event.keyCode==13)
        {
            var spSerialNum = $("#ipcIdentification-spSerialNum").val();
            var json_str;
            var json;
            json = {"accessApi": {"password":""},"spSerialNum":spSerialNum};
            json_str = JSON.stringify(json);
            console.log(json_str);
            $.ajax({
                type:"POST",
                url:"/change_spsnum",
                data:json_str,
                success:function(){       
                    this.showSuccessInfo("setting-success");    
                }.bind(this),
                error:function(XMLHttpRequest){
                    this.view.hideLoading();
                }.bind(this),
                dataType:"json"
            });
        }

    },

    startRecBlink: function () {
        this.blinkStat = "off";
        this.refreshRecBlink();
        this.timer = setInterval(this.refreshRecBlink.bind(this), 1000);
    },

    stopRecBlink: function () {
        this.timer && clearInterval(this.timer) && (this.timer = null);
    },

    switchDevice: function (event) {
        console.log("switchDevice:"+$(event.target));
        var tabId = $(event.target).jqmData("goto-id"), titleId = "#device-title";
        this.$btnNav.removeClass("ui-btn-active");
        $(event.target).addClass("ui-btn-active");
        this.$tabDevice.hide();
        $("#"+tabId).show();
        this.$btnCurTab = $(event.target);
        switch (tabId) {
            case "tab-cellular":
                $(titleId).text(this.model.getKeyLang("cellular", this.lang));
                break;
            case "tab-wifi":
                $(titleId).text(this.model.getKeyLang("wifi", this.lang));
                break;
            case "tab-storage":
                $(titleId).text(this.model.getKeyLang("storage", this.lang));
                break;
            case "tab-camera":
                $(titleId).text(this.model.getKeyLang("camera", this.lang));
                break;
            case "tab-power":
                $(titleId).text(this.model.getKeyLang("power", this.lang));
                break;
            case "tab-gpio":
                $(titleId).text(this.model.getKeyLang("gpio", this.lang));
                break;
            case "tab-usbdev":
                $(titleId).text(this.model.getKeyLang("usb", this.lang));
                break;
        }

        return this;
    },

    btnPing: function (event) {
        var inputId = "#"+$(event.target).jqmData("input-id"),
            resultId = "#"+$(event.target).jqmData("result-id"),
            network = $(event.target).jqmData("network");

        console.log("btnPing:"+$(event.target));
        console.log("network:"+$(event.target).jqmData("network"));
        console.log("input-id:"+inputId+":"+$(inputId).val());
        console.log("result-id:"+resultId+":"+$(resultId).val());
        if (!$(inputId).val()) {
            $(resultId).val(this.model.getKeyLang("addr-cannot-empty", this.lang));
        } else {
            this.pingNetworkEvent.notify({"network":network, "resultId":resultId, "destAddr":$(inputId).val()});
        }

        return this;
    },

    refreshPingResult: function (resultId, result) {
        console.log(resultId+":"+result);
        $(resultId).val(result);
    },

    btnRefreshSsid: function () {
        console.log("btnRefreshSsid");
        this.scanSsidEvent.notify();
        return this;
    },

    btnOuput: function () {
        console.log("btnOuput");
        var cmd_param = "";
        var dispChnEnum = 0;
        var audioOutput = 0;
        this.$btnCvbs.each(function(i, d){
            $(d).is(":checked") && (cmd_param += $(d).jqmData("param"));
            $(d).is(":checked") && (i<4) && (dispChnEnum |= (1<<i));
            $(d).is(":checked") && (i==4) && (audioOutput = 1);
        });
        console.log("param:"+cmd_param);
        console.log("dispChnEnum:"+dispChnEnum+",audioOutput:"+audioOutput);
        this.cvbsOutputEvent.notify({"dispChnEnum":dispChnEnum, "audioOutput":audioOutput});
        return this;
    },

    swipeLeft: function () {
        var nextBtnId = this.$btnCurTab.jqmData("next-btn");
        var $nextBtn = $("#"+nextBtnId);
        console.log("swipeLeft:"+nextBtnId);
        nextBtnId && $nextBtn.trigger("click");
        return this;
    },

    swipeRight: function () {
        var prevBtnId = this.$btnCurTab.jqmData("prev-btn");
        var $prevBtn = $("#"+prevBtnId);
        console.log("swipeRight:"+prevBtnId);
        prevBtnId && $prevBtn.trigger("click");
        return this;
    },

    refreshStatInfo: function (data) {
        $("#status-list").html(this.statusListTemplate(data)).trigger("create");
        this.changeChildrenLang("#status-list");
        $(".btn-popup").unbind().click(this.popupTriggerHandler);

        return this;
    },

    refreshCellInfo: function (data) {
        console.log("refreshCellInfo");
        $("#cell-info").html(this.cellInfoTemplate(data)).trigger("create");
        $(".input-addr").attr("placeholder", this.model.getKeyLang("input-ipaddr-domain", this.lang));
        $("#cell-test-result").val(this.model.getKeyLang("test-result:", this.lang));
        return this;
    },

    refreshWifiInfo: function (data) {
        console.log("refreshWifiInfo");
        $("#wifi-info").html(this.wifiInfoTemplate(data)).trigger("create");
        this.changeChildrenLang("#wifi-info");
        //$(".input-addr").attr("placeholder", this.model.getKeyLang("input-ipaddr-domain", this.lang));
        //$("#wifi-test-result").val(this.model.getKeyLang("test-result:", this.lang));
        $(".btn-popup").unbind().click(this.popupTriggerHandler);
        return this;
    },

    refreshScanSsid: function (data) {
        console.log("refreshScanSsid");
        this.scanSsidJson = $.extend({}, data);
        $("#scan-ssid-list").html(this.scanSsidTemplate(data)).listview("refresh");
        this.changeChildrenLang("#scan-ssid-list");
        $("#btn-refresh-ssid").unbind().click(this.btnRefreshSsidHandler);
        $(".btn-popup").unbind().click(this.popupTriggerHandler);

        return this;
    },

    refreshStorageInfo: function (data) {
        console.log("refreshStorageInfo");
        $("#storage-info").html(this.storageInfoTemplate(data)).trigger("create");
        this.changeChildrenLang("#storage-info");

        return this;
    },

    refreshCameraInfo: function (data) {
        console.log("refreshCameraInfo");
        $("#camera-info").html(this.cameraInfoTemplate(data)).trigger("create");
        this.changeChildrenLang("#camera-info");

        return this;
    },

    refreshPowerInfo: function (data) {
        console.log("refreshPowerInfo start");
        $("#power-info").html(this.powerInfoTemplate(data)).trigger("create");
        this.changeChildrenLang("#power-info");
        console.log("refreshPowerInfo end");

        return this;
    },

    refreshGpioInfo: function (data) {
        console.log("refreshGpioInfo");
        $("#gpio-info").html(this.gpioInfoTemplate(data)).trigger("create");
        this.changeChildrenLang("#gpio-info");
        console.log("refreshGpioInfo end");

        return this;
    },

    refreshUsbInfo: function (data) {
        console.log("refreshUsbInfo");
        $("#usb-info").html(this.usbInfoTemplate(data)).trigger("create");
        this.changeChildrenLang("#usb-info");
        console.log("refreshUsbInfo end");
    },

    refreshConfigs: function (data) {
        $('#main').html(this.configsTemplate(data)).trigger("create");

        this.createChildren()
            .setupHandlers()
            .enable()
            .changeLang()
            .changeTheme();
            
        InitCustomControl(this.view);
        if (data.hasOwnProperty("algConfig")&& data.algConfig.hasOwnProperty("dmsConf")) {
            $("#algConfig-dmsConf-dmsSensitivity").val(data.algConfig.dmsConf.dmsSensitivity);
        }

        const configJson = this.model.getConfigJson();
        console.log(this.model.getConfigJson(),'config');
        var lang = this.lang;
        if(lang == "INC"||lang=="TEL") lang = "EN";
        var str = this.model.getKeyLang("ifrm-interval", lang);
        str = str.replace("fps",configJson.mediaConfig.mainFramerate);
        $("#tab-mainifrm-interval").text(str);
        str = this.model.getKeyLang("ifrm-interval", lang);
        str = str.replace("fps",configJson.mediaConfig.subFramerate);
        $("#tab-subifrm-interval").text(str);
        
    

                if( data.algConfig.pd.pdAlarmOutInterval == -1 )
            {
                this.$checkPdAlarmAuto.trigger("click");
            }

     
        if(this.model.getMachineType() == "IPCR20S4" || this.model.getMachineType() == "ADA32V3"){
            this.$selMainStreamEncode.trigger("change");
            this.$selMainStreamRcMode.trigger("change");
        }
        // if(data.ipcIdentification.hardware == "WFTR20S3")
        // {
        //     document.getElementById('mediaConfig-osdConf-channelName').setAttribute('maxlength', '64');
        // }
        // else
        // {
        //     document.getElementById('mediaConfig-osdConf-channelName').setAttribute('maxlength', '32');
        // }


        // document.getElementById('mediaConfig-osdConf-channelName').setAttribute('maxlength', '32');
        this.$btnNav.each(function (i, d) {
            $(d).hasClass("menu_checked") && $(d).click();
        }.bind(this));

        this.$btnSelChn.each(function (i, d) {
            $(d).hasClass("menu_checked") && $(d).click();
        }.bind(this));
        
        // 在配置刷新后应用权限控制
        if (typeof clearAllRestrictedStates === 'function' && typeof hideRestrictedElements === 'function') {
            setTimeout(function() {
                clearAllRestrictedStates();
                setTimeout(hideRestrictedElements, 100);
            }, 100);
        }
        
        return this;
    },

    refreshSsidList: function () {
        $("#ssid-list").html(this.ssidListTemplate(this.wifiJson)).trigger("create");
        this.createChildren()
            .setupHandlers()
            .enable()
            .changeLang()
            .changeTheme();

        return this;
    },

    refreshSysInfo: function (data) {
        $("#sys-info").html(this.sysInfoTemplate(data)).trigger("create");     
        this.createChildren()
            .setupHandlers()
            .enable()
            .changeLang()
            .changeTheme();

        InitCustomControl();
        var watiSecMax=60;
        var uploader = {
            url: '/firmware',
            dataType: 'json',
            allowedTypes: '*',
            maxFileSize: 50*1024*1024,
            maxFiles: 1,
            extFilter: 'bin',
            checkSDcard:function(){          
                return this.model.getStatusJson();
            }.bind(this),
            
            getIPCType:function(){
                return config.ipcIdentification.hardware;
            }.bind(this),
            getBoardType:function(){
                return config.ipcIdentification.board;
            }.bind(this),
            warnning:function(){
                $('#popup-show-info').find("h1").text(this.model.getKeyLang("No SDcard"));
                $('#popup-show-info').find("h3").text(this.model.getKeyLang("Insert SDcard and Enable it"));
                $('#popup-show-info').find("p").text("");
                $('#show-info').click();
            }.bind(this),

            onInit: function(){
                console.log('Plugin initialized correctly');
            },
            onBeforeUpload: function(id){
                console.log('Starting the upload of #' + id);
                $("#show-error").text(" ");
            },
            onNewFile: function(id, file){
                function humanizeSize(size) {
                    var i = Math.floor( Math.log(size) / Math.log(1024) );
                    return ( size / Math.pow(1024, i) ).toFixed(2) * 1 + ' ' + ['B', 'kB', 'MB', 'GB', 'TB'][i];
                }
                console.log('#demo-files:'+id+':'+file);
                $("#show-error").text(" ");
                var template = '<div id="firmware-file">' + file.name +
                    '<span class="file-size">(' + humanizeSize(file.size) + ')</span> - Status: <span class="file-status">Waiting to upload</span>'+
                    '<div class="progress progress-striped active">'+
                    '<div class="progress-bar" role="progressbar" style="width: 0%;">'+
                    '<span class="sr-only">0%</span>'+
                    '</div>'+
                    '</div>'+
                    '</div>';
                $("#upload-status").html(template).trigger("create");
            },
            onComplete: function(){
                console.log('All pending tranfers completed');
            },
            onUploadProgress: function(id, percent){
                var percentStr = percent + '%';
                $('#firmware-file').find('div.progress-bar').width(percentStr);
                $('#firmware-file').find('span.sr-only').html(percentStr);
            },
            onUploadSuccess: function(id, data){
                console.log('Upload of file #' + id + ' completed');
                console.log('Server Response for file #' + id + ': ' + JSON.stringify(data));
                console.log(id+'success Upload Complete');
                console.log(id+'100%');
                $('#firmware-file').find('div.progress-bar').width('100%');
                $('#firmware-file').find('span.file-status').html("Upload Complete").addClass("file-status-success");
                $('#show-upgrade').click();
                var waitSec = watiSecMax;
                function waitCallback(){
                    waitSec--;
                    console.log(waitSec);
                    $("#wait-sec").text(waitSec);
                    if(waitSec > 0){
                        window.setTimeout(waitCallback, 1000)
                    } else {
                        window.location.href = "login.html";
                    }
                }
                window.setTimeout(waitCallback, 1000);
            },
            onUploadError: function(id, message){
                //alert(this.model.getKeyLang("firmware-download-fail", this.model.getDevLang()));
		alert("Firmware Download Fail");
		$('#firmware-file').remove();
                console.log('Failed to Upload file #' + id + ': ' + message);
            },
            onFileTypeError: function(file){
                console.log('File \'' + file.name + '\' cannot be added: must be an *.bin');
                $("#show-error").text('File \'' + file.name + '\' cannot be added: must be an *.bin');
            },
            onFileSizeError: function(file){
                console.log('File \'' + file.name + '\' cannot be added: size excess limit');
                $("#show-error").text('File \'' + file.name + '\' cannot be added: size excess limit');
            },
            onFileExtError: function(file){
                console.log('File \'' + file.name + '\' has a Not Allowed Extension');
                $("#show-error").text('File \'' + file.name + '\' cannot be added: must be an *.bin');
            },
            onFallbackMode: function(message){
                console.log('Browser not supported(do something else here!): ' + message);
                $("#show-error").text('Browser not supported(do something else here!): ' + message);
            }
        };
        var config = this.model.getConfigJson();
        if (config.hasOwnProperty("ipcIdentification")
            && config.ipcIdentification.hasOwnProperty("hardware")) {
            if (config.ipcIdentification.hardware == "ADA32V2" || config.ipcIdentification.hardware == "ADA32IR" || config.ipcIdentification.hardware == "ADA900V1" || config.ipcIdentification.hardware == "ADA32N1" || config.ipcIdentification.hardware == "ADA32V3" || config.ipcIdentification.hardware == "RCT16947V2" || config.ipcIdentification.hardware == "ADA32C4" || config.ipcIdentification.hardware == "ADA32E1") {
                watiSecMax = 60*3;
                uploader.maxFileSize = 74*1024*1024;
            } else if (config.ipcIdentification.hardware == "DMS885N" || config.ipcIdentification.hardware == "DMS31V2" || config.ipcIdentification.hardware == "ADA42V1" || config.ipcIdentification.hardware == "ADA47V1") {
                uploader.maxFileSize = 256*1024*1024;

            }
        }
        $('#drag-and-drop-zone').dmUploader(uploader);
        $('#click-import-key').dmUploader({
            url: '/import_key',
            dataType: 'json',
            allowedTypes: '*',
            maxFileSize: 256*1024,
            maxFiles: 1,
            extFilter: 'txt',
            checkSDcard:function(){
                return this.model.getStatusJson();
            }.bind(this),
            getIPCType:function(){
                return config.ipcIdentification.hardware;
            }.bind(this),
            getBoardType:function(){
                return config.ipcIdentification.board;
            }.bind(this),
            onInit: function(){
                console.log('Plugin initialized correctly');
            },
            onBeforeUpload: function(id){
                console.log('Starting the upload of #' + id);
            },
            onNewFile: function(id, file){
                console.log('#demo-files:'+id+':'+file);
            },
            onComplete: function(){
                console.log('All pending tranfers completed');
            },
            onUploadProgress: function(id, percent){

            },
            onUploadSuccess: function(id, data){
                console.log('Upload of file #' + id + ' completed');
                $("#show-error3").text('Import successful! Please reboot for active.').css({"color":"#00FF00"});
            },
            onUploadError: function(id, message){
                //console.log('Failed to Upload file #' + id + ': ' + message);
                $("#show-error3").text('Key file error!').css({"color":"#FF0000"});
            },
            onFileTypeError: function(file){
                console.log('File \'' + file.name + '\' cannot be added: must be an *.txt');
                $("#show-error3").text('File \'' + file.name + '\' cannot be added: must be an *.txt');
            },
            onFileSizeError: function(file){
                console.log('File \'' + file.name + '\' cannot be added: size excess limit');
                $("#show-error3").text('File \'' + file.name + '\' cannot be added: size excess limit');
            },
            onFileExtError: function(file){
                console.log('File \'' + file.name + '\' has a Not Allowed Extension');
                $("#show-error3").text('File \'' + file.name + '\' cannot be added: must be an *.txt');
            },
            onFallbackMode: function(message){
                console.log('Browser not supported(do something else here!): ' + message);
                $("#show-error3").text('Browser not supported(do something else here!): ' + message);
            }
        });
        $('#run-factory').dmUploader({
            url: '/run_factory',
            dataType: 'json',
            allowedTypes: '*',
            maxFileSize: 2*1024*1024,
            maxFiles: 1,
            extFilter: 'bin',
            checkSDcard:function(){
                return this.model.getStatusJson();
            }.bind(this),
            getIPCType:function(){
                return config.ipcIdentification.hardware;
            }.bind(this),
            getBoardType:function(){
                return config.ipcIdentification.board;
            }.bind(this),
            onInit: function(){
                console.log('Plugin initialized correctly');
            },
            onBeforeUpload: function(id){
                console.log('Starting the upload of #' + id);
                $("#show-error2").text(" ");
            },
            onNewFile: function(id, file){
                function humanizeSize(size) {
                    var i = Math.floor( Math.log(size) / Math.log(1024) );
                    return ( size / Math.pow(1024, i) ).toFixed(2) * 1 + ' ' + ['B', 'kB', 'MB', 'GB', 'TB'][i];
                }
                console.log('#demo-files:'+id+':'+file);
                $("#show-error2").text(" ");
                var template = '<div id="factory-file">' + file.name +
                    '<span class="file-size">(' + humanizeSize(file.size) + ')</span> - Status: <span class="file-status">Waiting to upload</span>'+
                    '<div class="progress progress-striped active">'+
                    '<div class="progress-bar" role="progressbar" style="width: 0%;">'+
                    '<span class="sr-only">0%</span>'+
                    '</div>'+
                    '</div>'+
                    '</div>';
                $("#upload-status2").html(template).trigger("create");
            },
            onComplete: function(){
                console.log('All pending tranfers completed');
            },
            onUploadProgress: function(id, percent){
                var percentStr = percent + '%';
                $('#factory-file').find('div.progress-bar').width(percentStr);
                $('#factory-file').find('span.sr-only').html(percentStr);
            },
            onUploadSuccess: function(id, data){
                console.log('Upload of file #' + id + ' completed');
                console.log('Server Response for file #' + id + ': ' + JSON.stringify(data));
                console.log(id+'success Upload Complete');
                console.log(id+'100%');
                $('#factory-file').find('div.progress-bar').width('100%');
                $('#factory-file').find('span.file-status').html("Upload Complete").addClass("file-status-success");
                $('#popup-show-info').find("h1").text("Run Facory Success");
                $('#popup-show-info').find("h3").text("Run Facory Success");
                $('#popup-show-info').find("p").text("");
                $('#show-info').click();
            },
            onUploadError: function(id, message){
                console.log('Failed to Upload file #' + id + ': ' + message);
                $('#popup-show-info').find("h1").text("Run Facory Failed.");
                $('#popup-show-info').find("h3").text("Run Facory Failed.");
                $('#popup-show-info').find("p").text("");
                $('#show-info').click();
            },
            onFileTypeError: function(file){
                console.log('File \'' + file.name + '\' cannot be added: must be an *.bin');
                $("#show-error2").text('File \'' + file.name + '\' cannot be added: must be an *.bin');
            },
            onFileSizeError: function(file){
                console.log('File \'' + file.name + '\' cannot be added: size excess limit');
                $("#show-error2").text('File \'' + file.name + '\' cannot be added: size excess limit');
            },
            onFileExtError: function(file){
                console.log('File \'' + file.name + '\' has a Not Allowed Extension');
                $("#show-error2").text('File \'' + file.name + '\' cannot be added: must be an *.bin');
            },
            onFallbackMode: function(message){
                console.log('Browser not supported(do something else here!): ' + message);
                $("#show-error2").text('Browser not supported(do something else here!): ' + message);
            }
        });

        this.queryStorageStatus();
        return this;
    },

    queryStorageStatus: function () {
        this.model.setSatusJson({});
         if (this.model.getIsSupportRecord()) {
             $.ajax({
                 type: "POST",
                 url: "query_status",
                 data: '{"module":"storage"}',
                 success: function(data,status){                  
                     //console.log("query_status_storage:"+JSON.stringify(data));
                     this.model.appendStorageStatus(data);
                     this.model.isStatusJsonComplete() && this.model.isStatusJsonHasStorage();               
                 }.bind(this),
                 error:  function(XMLHttpRequest){
                     this.showErrorInfo("query-fail", JSON.parse(XMLHttpRequest.responseText));
                 }.bind(this),
                 dataType:"json"
             });
         }
     },

     
    switchQuery: function (event) {
        var tabId = $(event.target).jqmData("goto-id"), titleId = "#query-title", calendarId="#sel-calendar";
        this.curTabQuery = tabId;
        this.$tabQuery.hide();
        $("#"+tabId).show();
        $(".xbtn-nav").each(function(i,d){
            ($(d).jqmData("goto-id") == tabId) && $(d).addClass("menu_checked");
            ($(d).jqmData("goto-id") != tabId) && $(d).removeClass("menu_checked");
        }.bind(this));
        if(tabId == "tab-recordlist" || tabId == "tab-alarmlist")
        {
            $(".footer").show();
        }
        else
        {
            $(".footer").hide();
        }
        this.$btnRefresh.css("display","none");
        this.NormalVideoPage = 0;
        this.AlarmVideoPage = 0;
        this.AlarmPicPage = 0;
        switch (tabId) {
            case "tab-status":
                this.showStatPage.notify();
                break;
            case "tab-log":
                this.showLogPage.notify();
                break;
			case "tab-recordlist":
                this.$btnRefresh.css("display","block");
                if((this.model.getMachineType() == "DMS31V2")                 
            	    ||(this.model.getMachineType() == "ADA32V2")
                    ||(this.model.getMachineType() == "ADA32IR")
                    ||(this.model.getMachineType() == "ADA32V3")
                    ||(this.model.getMachineType() == "RCT16947V2")
                    ||(this.model.getMachineType() == "ADA47V1")
                    ||(this.model.getMachineType() == "ADA32C4")) {
                    var args = {};
                    args.page= -1;
                    args.type=this.recordType;
                    args.filetype=this.fileType;
                    this.getRecordPages.notify(args);
                } 
                else{
                   $("#li_recordlist").css("disabled","disabled");
                }
                break;
            case "tab-alarmlist":
                var args = {};
                args.page=-1;
                this.getAlarmPages.notify(args);  
			    break;
        }
    },

    showCurPage:function()
    {
        if(this.curTabQuery=="tab-recordlist")
        {
            if($("#query_normal").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
            {
                $("#jump-page").val(this.NormalVideoPage+1);
                $("#total-pages").text(this.NormalVideoTotalPage+1);
            }    
            else if($("#query_alarm").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
            {
                $("#jump-page").val(this.AlarmVideoPage+1);
                $("#total-pages").text(this.AlarmVideoTotalPage+1);
            }   
            else if($("#query_alarm").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
            {
                $("#jump-page").val(this.AlarmPicPage+1);
                $("#total-pages").text(this.AlarmPicTotalPage+1);
            }
            else if($("#query_normal").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
            {
                $("#jump-page").val(0);
                $("#total-pages").text(0);
            }
        } else if(this.curTabQuery=="tab-alarmlist")
        {
            $("#jump-page").val(this.alarmLogTotal-this.alarmLogPage+1);
            $("#total-pages").text(this.alarmLogTotal+1);
        }
    },

    jumpPage:function()
    {
        var toJumpPage = $("#jump-page").val() - 1;
        if($("#query_normal").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
        {
            document.getElementById("recordlist-ul").innerHTML="";
            this.showCurPage();
            this.$popupShowInfo.find("h1").text(this.model.getKeyLang("No Support", this.lang));
            this.$popupShowInfo.find("h3").text(this.model.getKeyLang("Normal Type NoSupport JPG",this.lang));
            this.$popupShowInfo.find("p").text("");
            $("#show-info").trigger("click");
            return;
        }
        var args = {};
        args.type=this.recordType;
        args.filetype=this.fileType;
        if(this.curTabQuery=="tab-recordlist")
        {
            if($("#query_normal").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                this.NormalVideoPage = toJumpPage;
            else if($("#query_alarm").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                this.AlarmVideoPage = toJumpPage;
            else if($("#query_alarm").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
                this.AlarmPicPage = toJumpPage;
            if(this.NormalVideoPage > this.NormalVideoTotalPage || this.AlarmVideoPage > this.AlarmVideoTotalPage || this.AlarmPicPage > this.AlarmPicTotalPage)
            {
                this.$popupShowInfo.find("h1").text(this.model.getKeyLang("Last Page", this.lang));
                this.$popupShowInfo.find("h3").text(this.model.getKeyLang("Last Page,Select Pre",this.lang));
                this.$popupShowInfo.find("p").text("");
                this.NormalVideoPage = this.NormalVideoPage > this.NormalVideoTotalPage ? this.NormalVideoTotalPage : this.NormalVideoPage;
                this.AlarmVideoPage = this.AlarmVideoPage  > this.AlarmVideoTotalPage ? this.AlarmVideoTotalPage : this.AlarmVideoPage;
                this.AlarmPicPage = this.AlarmPicPage > this.AlarmPicTotalPage ? this.AlarmPicTotalPage : this.AlarmPicPage;
                $("#show-info").trigger("click");
                this.showCurPage();
                return;
            }
            if($("#query_normal").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                args.page = this.NormalVideoPage;
            else if($("#query_alarm").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                args.page = this.AlarmVideoPage;
            else if($("#query_alarm").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
                args.page = this.AlarmPicPage;
            this.showRecordPage.notify(args);
        }
        else if(this.curTabQuery=="tab-alarmlist")
        {
            this.alarmLogPage = this.alarmLogTotal - toJumpPage;
            if(this.alarmLogPage < 0 )
            {
                this.$popupShowInfo.find("h1").text(this.model.getKeyLang("Last Page", this.lang));
                this.$popupShowInfo.find("h3").text(this.model.getKeyLang("Last Page,Select Pre",this.lang));
                this.$popupShowInfo.find("p").text("");
                $("#show-info").trigger("click");
                this.alarmLogPage = 0;
                this.showCurPage();
                return;
            }
            args.page=this.alarmLogPage;
            this.showAlarmListPage.notify(args);
        }


    },

    pageChange:function(event)
    {
        var tabId = $(event.target).jqmData("page-id");
        $(".btn-page").each(function(i,d){
            ($(d).jqmData("page-id") == tabId) && $(d).addClass("menu_checked");
            ($(d).jqmData("page-id") != tabId) && $(d).removeClass("menu_checked");
        }.bind(this));
        if($("#query_normal").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
        {
            document.getElementById("recordlist-ul").innerHTML="";
            this.showCurPage();
            this.$popupShowInfo.find("h1").text(this.model.getKeyLang("No Support", this.lang));
            this.$popupShowInfo.find("h3").text(this.model.getKeyLang("Normal Type NoSupport JPG",this.lang));
            this.$popupShowInfo.find("p").text("");
            $("#show-info").trigger("click");
            return;
        }
        this.showCurPage();
        var args = {};
        args.type=this.recordType;
        args.filetype=this.fileType;
        switch(tabId)
        {
            case "pre":
                console.log("prepage");
                if(this.curTabQuery=="tab-recordlist")
                {
                    if($("#query_normal").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                        this.NormalVideoPage--;
                    else if($("#query_alarm").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                        this.AlarmVideoPage--;
                    else if($("#query_alarm").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
                        this.AlarmPicPage--;
                    if(this.NormalVideoPage<0 || this.AlarmVideoPage<0 || this.AlarmPicPage<0)
                    {
                        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("First Page", this.lang));
                        this.$popupShowInfo.find("h3").text(this.model.getKeyLang("First Page,Select Next",this.lang));
                        this.$popupShowInfo.find("p").text("");
                        $("#show-info").trigger("click");
                        this.NormalVideoPage = this.NormalVideoPage < 0 ? 0 : this.NormalVideoPage;
                        this.AlarmVideoPage = this.AlarmVideoPage < 0 ? 0 : this.AlarmVideoPage;
                        this.AlarmPicPage = this.AlarmPicPage < 0 ? 0 : this.AlarmPicPage;
                        this.showCurPage();
                        return;
                    }
                    if($("#query_normal").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                        args.page = this.NormalVideoPage;
                    else if($("#query_alarm").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                        args.page = this.AlarmVideoPage;
                    else if($("#query_alarm").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
                        args.page = this.AlarmPicPage;
                    this.showRecordPage.notify(args);
                }
                else if(this.curTabQuery=="tab-alarmlist")
                {
                    this.alarmLogPage++;
                    if(this.alarmLogPage > this.alarmLogTotal)
                    {              
                        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("First Page", this.lang));
                        this.$popupShowInfo.find("h3").text(this.model.getKeyLang("First Page,Select Next",this.lang));
                        this.$popupShowInfo.find("p").text("");
                        $("#show-info").trigger("click");
                        this.alarmLogPage=this.alarmLogTotal;
                        this.showCurPage();
                        return;
                    }              
                    args.page=this.alarmLogPage;
                    this.showAlarmListPage.notify(args);
                }
                
                break;
            case "next":
                console.log("nextpage");
                if(this.curTabQuery=="tab-recordlist")
                {
                    if($("#query_normal").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                        this.NormalVideoPage++;
                    else if($("#query_alarm").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                        this.AlarmVideoPage++;
                    else if($("#query_alarm").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
                        this.AlarmPicPage++;
                    if(this.NormalVideoPage > this.NormalVideoTotalPage || this.AlarmVideoPage > this.AlarmVideoTotalPage || this.AlarmPicPage > this.AlarmPicTotalPage)
                    {
                        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("Last Page", this.lang));
                        this.$popupShowInfo.find("h3").text(this.model.getKeyLang("Last Page,Select Pre",this.lang));
                        this.$popupShowInfo.find("p").text("");
                        $("#show-info").trigger("click");
                        this.NormalVideoPage = this.NormalVideoPage > this.NormalVideoTotalPage ? this.NormalVideoPage-1 : this.NormalVideoPage;
                        this.AlarmVideoPage = this.AlarmVideoPage  > this.AlarmVideoTotalPage ? this.AlarmVideoPage-1 : this.AlarmVideoPage;
                        this.AlarmPicPage = this.AlarmPicPage > this.AlarmPicTotalPage ? this.AlarmPicPage-1 : this.AlarmPicPage;
                        this.showCurPage();
                        return;
                    }
                    if($("#query_normal").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                        args.page = this.NormalVideoPage;
                    else if($("#query_alarm").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
                        args.page = this.AlarmVideoPage;
                    else if($("#query_alarm").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
                        args.page = this.AlarmPicPage;
                    this.showRecordPage.notify(args);
                }
                else if(this.curTabQuery=="tab-alarmlist")
                {
                    this.alarmLogPage--;
                    if(this.alarmLogPage<0)
                    {
                        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("Last Page", this.lang));
                        this.$popupShowInfo.find("h3").text(this.model.getKeyLang("Last Page,Select Pre",this.lang));
                        this.$popupShowInfo.find("p").text("");
                        $("#show-info").trigger("click");
                        this.alarmLogPage = 0;
                        this.showCurPage();
                        return;
                    }
                    args.page=this.alarmLogPage;
                    this.showAlarmListPage.notify(args);
                }
               
                break;
        }
        this.showCurPage();
    },

    recordFilter:function(event)
    {
        var filter = $(event.target).jqmData("sel-id");
        if(filter == "normal" || filter == "alarm")
        {
            $(".sel1").each(function(i,d){
                ($(d).jqmData("sel-id") == filter) && $(d).addClass("menu_checked") && (this.recordType=filter);
                ($(d).jqmData("sel-id") != filter) && $(d).removeClass("menu_checked");
            }.bind(this));
        }
        else
        {
            $(".sel2").each(function(i,d){
                ($(d).jqmData("sel-id") == filter) && $(d).addClass("menu_checked") && (this.fileType=filter);
                ($(d).jqmData("sel-id") != filter) && $(d).removeClass("menu_checked");
            }.bind(this));
        }
        if($("#query_normal").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
        {
            document.getElementById("recordlist-ul").innerHTML="";
            this.showCurPage();
            this.$popupShowInfo.find("h1").text(this.model.getKeyLang("No Support", this.lang));
            this.$popupShowInfo.find("h3").text(this.model.getKeyLang("Normal Type NoSupport JPG",this.lang));
            this.$popupShowInfo.find("p").text("");
            $("#show-info").trigger("click");
            return;
        }
        this.showCurPage();
        var args = {};
        if($("#query_normal").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
            args.page = this.NormalVideoPage;
        else if($("#query_alarm").hasClass("menu_checked") && $("#query_video").hasClass("menu_checked"))
            args.page = this.AlarmVideoPage;
        else if($("#query_alarm").hasClass("menu_checked") && $("#query_pic").hasClass("menu_checked"))
            args.page = this.AlarmPicPage;
        args.type=this.recordType;
        args.filetype=this.fileType;
        this.showRecordPage.notify(args);
    },

    switchTimeType: function () {
        console.log("switchTimeType:"+this.$selTimeType.val());
        if (this.$selTimeType.val() == "relative") {
            $(".relative-time").show();
            $(".absolute-time").hide();
        } else if (this.$selTimeType.val() == "absolute") {
            var beginDate = new Date(this.deviceTime.getTime()-300000);
            this.$inputBeginTime.val(beginDate.Format("yyyy-MM-ddThh:mm:ss"));
            this.$inputEndTime.val(this.deviceTime.Format("yyyy-MM-ddThh:mm:ss"));
            $(".relative-time").hide();
            $(".absolute-time").show();
        } else {
            this.$selTimeType.val("relative").selectmenu("refresh");
            $.mobile.changePage("#page-record-calendar");
        }
    },

    switchApFrequency: function () {
        console.log("switchApFrequency:"+this.$selApFrequency.val());
        this.$tabSet2gChannel.hide();
        this.$tabSet5gChannel.hide();
        switch (this.$selApFrequency.val()) {
            case "2.4G":
                this.$tabSet2gChannel.show();
                break;
            case "5G":
                this.$tabSet5gChannel.show();
                break;
        }
    },

    checkDeleteAll:function()
    {
        var checked = this.$inputDeleteAll.is(":checked");
        if(checked)
        {
            $(".smallcheckBtn").checked=true;
        }
        let chBox = document.getElementsByClassName('delete_user');
        let len = chBox.length;
        //输出观察
        for(let i=0;i<len;i++){
        //当全选按钮为true时，全部按钮都为true，否则相反
          if(checked){
            chBox[i].checked=true;
          }
          else {
            chBox[i].checked=false;
          }
        }
    },

    freemodeEnable:function()
    {
        if(this.calibration_screen == null) return;
        if(this.freemode_onOff == true) 
        {
            this.freemode_onOff = false;
            this.calibration_screen.setFreeMode(false);
        }       
        else if(this.freemode_onOff == false)
        {
            this.freemode_onOff = true;
            this.calibration_screen.setFreeMode(true);
        }       
        console.log(this.freemode_onOff);
    },

    switchSerNetType:function(){
        console.log("switchSerNetType:"+this.$selSerNetType.val());
        if(this.$selSerNetType.val()==1)
        {//wifi
            $("#networkConfig-wifi-staMode-enable").prop("checked", true);
            $("#sta-ip").show();
            $("#sta-ip-pwd").show();
            $(".apn").hide();
        }
        else if(this.$selSerNetType.val()==0){
            if(this.model.getMachineType() != "WFCR20S2")
            {
                $("#networkConfig-wifi-staMode-enable").prop("checked", false);
                $("#sta-ip").hide();
                $("#sta-ip-pwd").hide();
                $(".apn").hide();
            }
        }
        else if(this.$selSerNetType.val()==3)
        {//4g
		    $(".apn").show();
            if(this.model.getMachineType() != "WFCR20S2")
            {               
                $("#networkConfig-wifi-staMode-enable").prop("checked", false);
                $("#sta-ip").hide();
                $("#sta-ip-pwd").hide();
            }
        }
    },

    enableServer:function()
    {
        if(this.model.getMachineType() == "WFCR20S2") return ;
        if( $("#serverConfig-enable").prop("checked")==true){
            if(this.$selSerNetType.val()==1){
                $("#networkConfig-wifi-staMode-enable").prop("checked", true);
                $("#sta-ip").show();
                $("#sta-ip-pwd").show();
                $(".apn").hide();
            }
        }
        else{
            $("#networkConfig-wifi-staMode-enable").prop("checked", false);
            $("#sta-ip").hide();
            $("#sta-ip-pwd").hide();
        }
    },

    switchMainFramerate: function () {
        console.log("switchMainFramerate:"+this.$selMainStreamFramerate.val());
        var str = this.model.getKeyLang("ifrm-interval", this.lang);
        
        str = str.replace("fps",this.$selMainStreamFramerate.val());
        if(this.$selMainStreamFramerate.val()*1.0 > this.$selMainStreamIfrmInterval.val()*1.0)
            this.$selMainStreamIfrmInterval.val(this.$selMainStreamFramerate.val());

        $("#tab-mainifrm-interval").text(str);
    },

    switchSubFramerate: function () {
        if (this.model.getMachineType() != "ADA42V1") {
            console.log("switchSubFramerate:"+this.$selSubStreamFramerate.val());
            var str = this.model.getKeyLang("ifrm-interval", this.lang);
            str = str.replace("fps",this.$selSubStreamFramerate.val());
            if(this.$selSubStreamFramerate.val()*1.0 > this.$selSubStreamIfrmInterval.val()*1.0)
                this.$selSubStreamIfrmInterval.val(this.$selSubStreamFramerate.val());

            $("#tab-subifrm-interval").text(str);
        }
    },

    query: function () {
        console.log("query");
        var startTime, endTime, json={};
        if (this.$selTimeType.val() == "relative") {
            endTime = this.deviceTime.getTime() / 1000;
            switch (this.$selRelativeTime.val()) {
                case "last-5-min":
                    startTime = endTime - 300;
                    break;
                case "last-15-min":
                    startTime = endTime - 900;
                    break;
                case "last-30-min":
                    startTime = endTime - 1800;
                    break;
                case "last-1-hour":
                    startTime = endTime - 3600;
                    break;
                case "last-2-hour":
                    startTime = endTime - 7200;
                    break;
                case "last-1-day":
                    startTime = endTime - 86400;
                    break;
            }
        } else {
            var beginDate = new Date(this.$inputBeginTime.val()),
                endDate = new Date(this.$inputEndTime.val());
            startTime = beginDate.getTime()/1000;
            endTime = endDate.getTime()/1000;
        }
        console.log(this.curTabQuery+":"+startTime+"-"+endTime);
        switch (this.curTabQuery) {
            case "tab-record":
                json.scanType = 1;
                json.startTime = startTime;
                json.endTime = endTime;
                this.queryEvent.notify(json);
                break;

            case "tab-event":
                if (this.$selClipStat.val()=="cliped") {
                    json.scanType = 3;
                } else {
                    json.scanType = 2;
                }
                json.startTime = startTime;
                json.endTime = endTime;
                this.queryEvent.notify(json);
                break;

            case "tab-log":
                if (this.logTypeList.length == 0) {
                    console.log("length==0");
                    this.$popupShowInfo.find("h1").text(this.model.getKeyLang("request-error", this.lang));
                    this.$popupShowInfo.find("h3").text(this.model.getKeyLang("pls-sel-log", this.lang));
                    this.$popupShowInfo.find("p").text("");
                    $("#show-info").trigger("click");
                } else {
                    json.startTime = startTime;
                    json.endTime = endTime;
                    if (!this.allLogChecked) {
                        json.type = this.logTypeList.join(",");
                    }
                    this.queryLogEvent.notify(json);
                }
                break;
        }
    },

    btnDisplayPicture: function (event) {
        var filePath = $(event.target).jqmData("file-path");
        $imgPlay = $("#pic-player");
        $imgPlay[0].complete && ($imgPlay[0].src = filePath);
        console.log(filePath);
        $.mobile.changePage("#page-display-picture");
    },

    beforeshowQuery: function () {
        console.log("beforeshowQuery:"+this.curTabQuery);
        $(".btn-nav").removeClass("ui-btn-active").removeClass("ui-state-persist").each(function(i,d){
            ($(d).jqmData("goto-id") == this.curTabQuery) && $(d).addClass("ui-btn-active").addClass("ui-state-persist");
        }.bind(this));
    },

    beforshowDisplayPicture: function () {
        console.log("beforshowDisplayPicture");
        this.$sliTimeLeft = $("#time-slider-left");
        this.$sliTimeRight = $("#time-slider-right");
        this.$sliTimeLeft.unbind("change", this.sliderChangeHandler).change(this.sliderChangeHandler);
        this.$sliTimeRight.unbind("change", this.sliderChangeHandler).change(this.sliderChangeHandler);
        this.refreshSliderTime();
    },

    beforshowPlayVideo: function () {
        console.log("beforshowPlayVideo");
        !this.mediaPlayer && (this.mediaPlayer = new MediaElementPlayer(document.getElementById("mediaplayer"), {
            stretching: 'auto',
            pluginPath: './js/',
            success: function (media) {
                console.log(media.id);
                media.addEventListener('loadedmetadata', function () {
                    console.log("loadedmetadata");
                });
                media.addEventListener('error', function (e) {
                    console.log("media error!");
                });
            }
        }));
        this.$btnSwitchVideoChn.checkboxradio("refresh");
    },

    beforshowRecordCalendar: function () {
        console.log("beforshowRecordCalendar");
        /*if (!this.calendar) {
            this.calendar = new Calendar("#calendar", null);
            var dayJson = {
                "year":2017,
                "month": 11,
                "daylist":[
                    [29, 30,  1,  2,  3,  4,  5],
                    [ 6,  7,  8,  9, 10, 11, 12],
                    [13, 14, 15, 16, 17, 18, 19],
                    [20, 21, 22, 23, 24, 25, 26],
                    [27, 28, 29, 30, 31,  1,  2]
                ]
            };
            this.calendar.drawMonthSlide("#slide-month1", dayJson);
            this.calendar.drawMonthSlide("#slide-month2", dayJson);
            this.calendar.drawMonthSlide("#slide-month3", dayJson);
        }*/
    },

    pagecreateRecordCalendar: function () {
        console.log("pagecreateRecordCalendar");
        if (!this.calendar) {
            this.calendar = new Calendar("#calendar", null);
            var dayJson = {
                "year":2017,
                "month": 11,
                "preDayList":[
                    [30, 31,  1,  2,  3,  4,  5],
                    [ 6,  7,  8,  9, 10, 11, 12],
                    [13, 14, 15, 16, 17, 18, 19],
                    [20, 21, 22, 23, 24, 25, 26],
                    [27, 28,  1,  2,  3,  4,  5]
                ],
                "curDayList":[
                    [27, 28,  1,  2,  3,  4,  5],
                    [ 6,  7,  8,  9, 10, 11, 12],
                    [13, 14, 15, 16, 17, 18, 19],
                    [20, 21, 22, 23, 24, 25, 26],
                    [27, 28, 29, 30, 31,  1,  2]
                ],
                "nextDayList":[
                    [27, 28, 29, 30, 31,  1,  2],
                    [ 3,  4,  5,  6,  7,  8,  9],
                    [10, 11, 12, 13, 14, 15, 16],
                    [17, 18, 19, 20, 21, 22, 23],
                    [24, 25, 26, 27, 28, 29, 30],
                    [31,  1,  2,  3,  4,  5,  6]
                ]
            };
            this.calendar.drawMonthSlide("#month-wrapper", dayJson);
        }
    },

    btnPlayVideo: function (event) {
        var writing = $(event.target).jqmData("writing");
        if (writing) {
            return;
        }
        var filePath = $(event.target).jqmData("file-path");
        var aviVideo = filePath.indexOf('.avi');
        if(aviVideo!=-1)
        {
            this.$popupShowInfo.find("h1").text(this.model.getKeyLang("No Support", this.lang));
            this.$popupShowInfo.find("h3").text(this.model.getKeyLang("Not Support PlayBack AVI Video",this.lang));
            this.$popupShowInfo.find("p").text("");
            $("#show-info").trigger("click");
            return;
        }
        var filename = filePath.substring(filePath.lastIndexOf('/') + 1);
        console.log("btnPlayVideo:"+filePath);
        this.mediaPlayer.pause();
        this.mediaPlayer.setSrc(filePath);
        this.mediaPlayer.load();
        $.mobile.changePage("#page-play-video");
    },

    btnSwitchVideoChn: function (event) {
        console.log("btnSwitchVideoChn:"+$(event.target).jqmData("url"));
        this.$btnSwitchVideoChn.each(function(i,d){
            d.checked = false;
        });
        event.target.checked = true;
        this.$btnSwitchVideoChn.checkboxradio("refresh");
        this.mediaPlayer.pause();
        this.mediaPlayer.setSrc($(event.target).jqmData("url"));
        this.mediaPlayer.load();
        this.mediaPlayer.play();
    },

    btnSelectAll: function (event) {
        var checked = $(event.target).is(":checked");
        console.log("btnSelectAll:"+checked);
        this.$btnSelectAll.checkboxradio("refresh");
        this.logTypeList = [];
        this.$checkLogType.each(function(i,d){
            d.checked = checked;
            $(d).checkboxradio("refresh");
            checked && this.logTypeList.push(d.id);
        }.bind(this));
        if (checked) {
            this.$btnPopupType.text(this.model.getKeyLang("log-type:all", this.lang));
            this.allLogChecked = true;
        } else {
            this.$btnPopupType.text(this.model.getKeyLang("log-type:none", this.lang));
            this.allLogChecked = false;
        }
        console.log(this.logTypeList);
    },

    checkLogType: function (event) {
        var checked = $(event.target).is(":checked"),allChecked;
        console.log("checkLogType:"+checked);
        !checked && this.$btnSelectAll.is(":checked") && this.$btnSelectAll.attr("checked", false).checkboxradio("refresh");
        if (checked) {
            this.logTypeList.push(event.target.id);
        } else {
            for (var i=0; i<this.logTypeList.length; i++) {
                (this.logTypeList[i] == event.target.id) && this.logTypeList.splice(i,1);
            }
        }
        this.allLogChecked = false;
        if (this.logTypeList.length == 0) {
            this.$btnPopupType.text(this.model.getKeyLang("log-type:none", this.lang));
        } else if ((allChecked=true) && this.$checkLogType.each(function(i,d){
                    !$(d).is(":checked") && (allChecked=false);
                }) && allChecked) {
            this.$btnPopupType.text(this.model.getKeyLang("log-type:all", this.lang));
            this.allLogChecked = true;
        } else {
            this.$btnPopupType.text(this.model.getKeyLang("log-type:...", this.lang));
        }
        console.log(this.logTypeList);
    },

    btnSearchLog: function () {
        console.log("btnSearchLog");
        var logType="event";
        $(".btn-log-type").each(function(i,d){
            $(d).hasClass("menu_checked") && (logType=$(d).attr("value"));
        });
        this.queryLogEvent.notify({"type":logType});
        return this;
    },

    btnDownload: function (event) {

        /*$(".btn-download").css("pointer-events","none");
        let startInterval = setInterval(function() {
            $(".btn-download").css("pointer-events","auto");
            clearInterval(startInterval);
        }, 1000*1);*/


        var writing = $(event.target).jqmData("writing");
        if (writing) {
            return;
        }
        var filePath = $(event.target).jqmData("file-path");
        var filename = filePath.substring(filePath.lastIndexOf('/') + 1);
        
        var protocol = window.location.protocol;
        var host = window.location.host;
        var url = protocol + "//" + host + filePath;
        console.log("btnDownload:"+url);
        handleXHR(url,filename);
    },

    refreshSliderTime: function (beginTime, endTime) {
        console.log("refreshSliderTime");
        this.recordBeginTimestamp = beginTime || (this.deviceTime.getTime()/1000 - 300);
        this.recordEndTimestamp = endTime || (this.deviceTime.getTime()/1000);
        $("#record-begin-time").val(new Date(this.recordBeginTimestamp*1000).Format("yyyy-MM-ddThh:mm:ss"));
        $("#record-end-time").val(new Date(this.recordEndTimestamp*1000).Format("yyyy-MM-ddThh:mm:ss"));
        this.$sliTimeLeft.attr("min", this.recordBeginTimestamp).attr("max", this.recordEndTimestamp).val(this.recordBeginTimestamp);
        this.$sliTimeRight.attr("min", this.recordBeginTimestamp).attr("max", this.recordEndTimestamp).val(this.recordEndTimestamp);

        return this;
    },

    refreshSlider: function () {
        this.$sliTimeLeft.slider("refresh");
        this.$sliTimeRight.slider("refresh");

        return this;
    },

    sliderChange: function (event) {
        var $slider = $(event.target),
            dateId = "#"+$slider.jqmData("attach-id"),
            value = Number($slider.val()),
            startPts = Number($("#time-slider-left").val()),
            endPts = Number($("#time-slider-right").val());
        $(dateId).val(new Date(value*1000).Format("yyyy-MM-ddThh:mm:ss"));
        this.$galleryImgs && this.$galleryImgs.each(function(i,d){
            var timestamp = Number($(d).jqmData("timestamp"));
            if (timestamp < startPts || timestamp > endPts) {
                $(d).hide();
            } else {
                $(d).show();
            }
        });
    },

    btnRefreshPicture: function () {
        var chnList = [];
        $(".btn-pic-chn").each(function(i, d){
            $(d).is(":checked") && chnList.push(Number($(d).val()));
        });
        console.log(chnList);
        console.log("btnRefreshPicture:"+this.$sliTimeLeft.val()+"-"+this.$sliTimeRight.val());
        if (chnList.length == 0) {
            this.$popupShowInfo2.find("h1").text(this.model.getKeyLang("request-error", this.lang));
            this.$popupShowInfo2.find("h3").text(this.model.getKeyLang("sel-at-less-one-chn.", this.lang));
            this.$popupShowInfo2.find("p").text("");
            $("#show-info2").trigger("click");
        } else {
            this.queryImgEvent.notify({"chn":chnList,"startTime":Number(this.$sliTimeLeft.val()), "endTime":Number(this.$sliTimeRight.val())});
        }
    },

    btnRequestPlayback: function () {
        var chnList = [];
        var chnStr = "", startTime = Number(this.$sliTimeLeft.val()), endTime = Number(this.$sliTimeRight.val());
        $(".btn-pic-chn").each(function(i, d){
            var chn = Number($(d).val());
            $(d).is(":checked") && chnList.push(chn) && (chnStr+="CH"+chn+" ");
        });
        console.log(chnList);
        console.log("btnRequestPlayback:"+this.$sliTimeLeft.val()+"-"+this.$sliTimeRight.val());
        if (chnList.length == 0) {
            this.$popupShowInfo2.find("h1").text(this.model.getKeyLang("request-error", this.lang));
            this.$popupShowInfo2.find("h3").text(this.model.getKeyLang("sel-at-less-one-chn.", this.lang));
            this.$popupShowInfo2.find("p").text("");
            $("#show-info2").trigger("click");
        } else if (endTime - startTime > 1800) {
            this.$popupShowInfo2.find("h1").text(this.model.getKeyLang("request-error", this.lang));
            this.$popupShowInfo2.find("h3").text(this.model.getKeyLang("time-exceed-limit-30min", this.lang));
            this.$popupShowInfo2.find("p").text("");
            $("#show-info2").trigger("click");
        } else {
            this.recordChnList = chnList;
            this.$popupShowRequestPlayback.find("h1").text(this.model.getKeyLang("request-play", this.lang));
            this.$popupShowRequestPlayback.find("h3").text(this.model.getKeyLang("sure-request-play?", this.lang));
            this.$popupShowRequestPlayback.find("#show-begin-time").text(new Date(startTime*1000).Format("yyyy-MM-ddThh:mm:ss"));
            this.$popupShowRequestPlayback.find("#show-end-time").text(new Date(endTime*1000).Format("yyyy-MM-ddThh:mm:ss"));
            this.$popupShowRequestPlayback.find("#show-chn").text(chnStr);
            $("#show-request-playback").trigger("click");
        }
    },

    btnSurePlayback: function () {
        console.log("btnSurePlayback:"+this.$sliTimeLeft.val()+"-"+this.$sliTimeRight.val()+":"+this.recordChnList);
        this.requestPlaybackEvent.notify({
            "startTime":Number(this.$sliTimeLeft.val()),
            "endTime":Number(this.$sliTimeRight.val()),
            "channels":this.recordChnList
        });
    },

    showPlaybackSuccess: function () {
        this.$popupShowInfo2.find("h1").text(this.model.getKeyLang("request-success", this.lang));
        this.$popupShowInfo2.find("h3").text(this.model.getKeyLang("query-clip-after-mins", this.lang));
        this.$popupShowInfo2.find("p").text("");
        $("#show-info2").trigger("click");
        return this;
    },

    showPlaybackError: function (json) {
        this.$popupShowInfo2.find("h1").text("Error");
        this.$popupShowInfo2.find("h3").text("request-playback-error");
        this.$popupShowInfo2.find("p").text(JSON.stringify(json));
        $("#show-info2").trigger("click");
        return this;
    },

    refreshDeviceTime: function (timestamp) {
        this.deviceTime.setTime(timestamp*1000);
        if (this.lang == "EN") {
            this.$btnDevTime.val(this.deviceTime.Format("yyyy-MM-dd hh:mm:ss"));
        } else {
            this.$btnDevTime.val(this.deviceTime.Format("yyyy-MM-dd hh:mm:ss"));
        }
        return this;
    },

    refreshRecordList: function (data) {
        this.recordListJson = $.extend({},data);
        $("#record-list").html(this.recordListTemplate(data)).trigger("create");
        
        this.createChildren()
            .setupHandlers()
            .enable()
            .changeLang()
            .changeTheme();

        return this;
    },

    refreshAlarmList:function(data){
        console.log("refreshAlarmList");
        $("#tbody-alarmlist tr:not(:first)").remove();//保留表头
        frag = document.createDocumentFragment();
        List = data.split("\n");
        for(i=List.length-1;i>=0;i--){
            ary = List[i].split(",");
            //console.log(ary);
            tr = document.createElement("tr");
            tr.style.fontSize =15+"px";
            for(j = 0;j<ary.length;j++){
                td = document.createElement("td");
                td.append(ary[j]);
                tr.appendChild(td);
            }
            if(ary.length!=1)
            {
                frag.appendChild(tr);
            }     
        }      
        document.getElementById("tbody-alarmlist").appendChild(frag);           
        this.createChildren()
            .setupHandlers()
            .enable()
            .changeLang()
            .changeTheme();

        return this;
    },
     
    refreshEventList: function (data, clipStat) {
        console.log("refreshEventList");
        this.model.setEventListJson(data);
        this.eventListJson = this.model.getEventListJson();
        this.clipStat = clipStat;
        $("#event-list").html(this.eventListTemplate(data)).trigger("create");
        this.createChildren()
            .setupHandlers()
            .enable()
            .changeLang()
            .changeTheme();
        if (clipStat == "clip") {
            this.$btnPlayVideo.show();
        } else {
            this.$btnPlayVideo.hide();
        }

        return this;
    },

    refreshLogList: function (data) {
        console.log("refreshLogList");
        $("#log-list").html(this.logListTemplate(data)).trigger("create");
        document.getElementById("filter_input").setAttribute("placeholder",this.model.getKeyLang("filter",this.lang));
		$("#log-list").find("input").bind('input propertychange', function() {
	    	var filter = $(this).val();
		  	$(this).parents("ol").find("li").each(function() {
				if($(this).text().indexOf(filter) != -1){
					$(this).show();
				}else{
					$(this).hide();
				}
			});
	   	});
        return this;
    },

    initPhotoSwipe: function () {
        var gallerySelector = ".pswp-gallery";
        var parseThumbnailElements = function(el) {
            var thumbElements = el.childNodes,
                numNodes = thumbElements.length,
                items = [],
                el,
                childElements,
                thumbnailEl,
                size,
                item;

            for(var i = 0; i < numNodes; i++) {
                el = thumbElements[i];
                // include only element nodes
                if(el.nodeType !== 1) {
                    continue;
                }
                childElements = el.children;
                size = el.getAttribute('data-size').split('x');
                // create slide object
                item = {
                    src: el.getAttribute('href'),
                    w: parseInt(size[0], 10),
                    h: parseInt(size[1], 10),
                    author: el.getAttribute('data-author')
                };
                item.el = el; // save link to element for getThumbBoundsFn
                if(childElements.length > 0) {
                    item.msrc = childElements[0].getAttribute('src'); // thumbnail url
                    if(childElements.length > 1) {
                        item.title = childElements[1].innerHTML; // caption (contents of figure)
                    }
                }
                var mediumSrc = el.getAttribute('data-med');
                if(mediumSrc) {
                    size = el.getAttribute('data-med-size').split('x');
                    // "medium-sized" image
                    item.m = {
                        src: mediumSrc,
                        w: parseInt(size[0], 10),
                        h: parseInt(size[1], 10)
                    };
                }
                // original image
                item.o = {
                    src: item.src,
                    w: item.w,
                    h: item.h
                };
                items.push(item);
            }
            return items;
        };

        // find nearest parent element
        var closest = function closest(el, fn) {
            return el && ( fn(el) ? el : closest(el.parentNode, fn) );
        };

        var onThumbnailsClick = function(e) {
            e = e || window.event;
            e.preventDefault ? e.preventDefault() : e.returnValue = false;
            var eTarget = e.target || e.srcElement;
            var clickedListItem = closest(eTarget, function(el) {
                return el.tagName === 'A';
            });
            if(!clickedListItem) {
                return;
            }
            var clickedGallery = clickedListItem.parentNode;
            var childNodes = clickedListItem.parentNode.childNodes,
                numChildNodes = childNodes.length,
                nodeIndex = 0,
                index;
            for (var i = 0; i < numChildNodes; i++) {
                if(childNodes[i].nodeType !== 1) {
                    continue;
                }
                if(childNodes[i] === clickedListItem) {
                    index = nodeIndex;
                    break;
                }
                nodeIndex++;
            }
            if(index >= 0) {
                openPhotoSwipe( index, clickedGallery );
            }
            return false;
        };

        var photoswipeParseHash = function() {
            var hash = window.location.hash.substring(1),
                params = {};
            if(hash.length < 5) { // pid=1
                return params;
            }
            var vars = hash.split('&');
            for (var i = 0; i < vars.length; i++) {
                if(!vars[i]) {
                    continue;
                }
                var pair = vars[i].split('=');
                if(pair.length < 2) {
                    continue;
                }
                params[pair[0]] = pair[1];
            }
            if(params.gid) {
                params.gid = parseInt(params.gid, 10);
            }
            return params;
        };

        var openPhotoSwipe = function(index, galleryElement, disableAnimation, fromURL) {
            var pswpElement = document.querySelectorAll('.pswp')[0],
                gallery,
                options,
                items;
            items = parseThumbnailElements(galleryElement);
            // define options (if needed)
            options = {

                galleryUID: galleryElement.getAttribute('data-pswp-uid'),

                getThumbBoundsFn: function(index) {
                    // See Options->getThumbBoundsFn section of docs for more info
                    var thumbnail = items[index].el.children[0],
                        pageYScroll = window.pageYOffset || document.documentElement.scrollTop,
                        rect = thumbnail.getBoundingClientRect();

                    return {x:rect.left, y:rect.top + pageYScroll, w:rect.width};
                },
                addCaptionHTMLFn: function(item, captionEl, isFake) {
                    if(!item.title) {
                        captionEl.children[0].innerText = '';
                        return false;
                    }
                    captionEl.children[0].innerHTML = item.title +  '<br/><small>Photo: ' + item.author + '</small>';
                    return true;
                }
            };
            if(fromURL) {
                if(options.galleryPIDs) {
                    // parse real index when custom PIDs are used
                    // http://photoswipe.com/documentation/faq.html#custom-pid-in-url
                    for(var j = 0; j < items.length; j++) {
                        if(items[j].pid == index) {
                            options.index = j;
                            break;
                        }
                    }
                } else {
                    options.index = parseInt(index, 10) - 1;
                }
            } else {
                options.index = parseInt(index, 10);
            }
            // exit if index not found
            if( isNaN(options.index) ) {
                return;
            }
            var radios = document.getElementsByName('gallery-style');
            for (var i = 0, length = radios.length; i < length; i++) {
                if (radios[i].checked) {
                    if(radios[i].id == 'radio-all-controls') {

                    } else if(radios[i].id == 'radio-minimal-black') {
                        options.mainClass = 'pswp--minimal--dark';
                        options.barsSize = {top:0,bottom:0};
                        options.captionEl = false;
                        options.fullscreenEl = false;
                        options.shareEl = false;
                        options.bgOpacity = 0.85;
                        options.tapToClose = true;
                        options.tapToToggleControls = false;
                    }
                    break;
                }
            }
            if(disableAnimation) {
                options.showAnimationDuration = 0;
            }
            // Pass data to PhotoSwipe and initialize it
            gallery = new PhotoSwipe( pswpElement, PhotoSwipeUI_Default, items, options);

            // see: http://photoswipe.com/documentation/responsive-images.html
            var realViewportWidth,
                useLargeImages = false,
                firstResize = true,
                imageSrcWillChange;

            gallery.listen('beforeResize', function() {
                var dpiRatio = window.devicePixelRatio ? window.devicePixelRatio : 1;
                dpiRatio = Math.min(dpiRatio, 2.5);
                realViewportWidth = gallery.viewportSize.x * dpiRatio;
                if(realViewportWidth >= 1200 || (!gallery.likelyTouchDevice && realViewportWidth > 800) || screen.width > 1200 ) {
                    if(!useLargeImages) {
                        useLargeImages = true;
                        imageSrcWillChange = true;
                    }
                } else {
                    if(useLargeImages) {
                        useLargeImages = false;
                        imageSrcWillChange = true;
                    }
                }
                if(imageSrcWillChange && !firstResize) {
                    gallery.invalidateCurrItems();
                }
                if(firstResize) {
                    firstResize = false;
                }
                imageSrcWillChange = false;
            });

            gallery.listen('gettingData', function(index, item) {
                if( useLargeImages ) {
                    item.src = item.o.src;
                    item.w = item.o.w;
                    item.h = item.o.h;
                } else {
                    item.src = item.m.src;
                    item.w = item.m.w;
                    item.h = item.m.h;
                }
            });

            gallery.init();
        };

        // select all gallery elements
        var galleryElements = document.querySelectorAll( gallerySelector );
        for(var i = 0, l = galleryElements.length; i < l; i++) {
            galleryElements[i].setAttribute('data-pswp-uid', i+1);
            galleryElements[i].onclick = onThumbnailsClick;
        }

        // Parse URL and open gallery if it contains #&pid=3&gid=1
        var hashData = photoswipeParseHash();
        if(hashData.pid && hashData.gid) {
            openPhotoSwipe( hashData.pid,  galleryElements[ hashData.gid - 1 ], true, true );
        }
    },

    refreshGallery: function (data) {
        console.log("refreshGallery");
        $("#gallery").html(this.galleryTemplate(data)).trigger("create");
        this.$galleryImgs = $("#gallery").children("a");

        return this;
    },

    collectValue: function () {
        var jsonTmpl;
        jsonTmpl = $.extend(true, {}, this.model.getConfigJsonTmpl());
        this.model.traverseJson(jsonTmpl);
        
        let isCh0 = window.ch === 'ch0'
        if(toggle==1)
            jsonTmpl = {
                'mediaConfig': {
                    [isCh0 ? 'chn0' : 'chn1']:jsonTmpl.mediaConfig
                }
            };
        else if(toggle==2)
        {
            /*if(this.model.getMachineType().indexOf("IPC") != -1)
                jsonTmpl={'networkConfig':jsonTmpl.networkConfig};
            else*/
                jsonTmpl={'networkConfig':jsonTmpl.networkConfig,'serverConfig':jsonTmpl.serverConfig};
        }   
        else if(toggle==3){
            jsonTmpl={'systemConfig':jsonTmpl.systemConfig};
        }
        else if(toggle==4){
            jsonTmpl = {
                'algConfig': {
                [isCh0 ? 'algChn0': 'algChn1']:jsonTmpl.algConfig
            }};
        }

        this.collectJson = jsonTmpl;

        console.log('jsonTmpl',jsonTmpl);

     
        if(this.collectJson.hasOwnProperty("systemConfig"))
            this.collectJson.systemConfig.language = this.lang;
        return this.collectJson;
    },
    TestPreview:function(){
        var password = prompt("Please enter passsword", "");
        if (password === "engineer"){
            var value = prompt("access to the Config?", "false");
            if (value == "" || value == null) {
                return ;
            }

            if(value == "true")
                $("#index_config").show();
            
        }
        if (password === "test"){
            var ensdcard;
            var normal_value = prompt("switch on \"normal\" video capture?'", "false");
            if (normal_value == "" || normal_value == null) {
                return ;
            }
            var bool_normal_value = normal_value == "true" ? 1 : 0;

            var alarm_value = prompt("switch on \"alarm\" video capture?'", "false");
            if (alarm_value == "" || alarm_value == null) {
                return ;
            }
            var bool_alarm_value = alarm_value == "true" ? 1 : 0;

            if(bool_alarm_value == false && bool_normal_value == false)
            {
                ensdcard = false;
            }
            else
            {
                ensdcard = true;
            }
            json = {"accessApi": {"password": ""},"systemConfig": {"enableStorage": ensdcard,"normalRecord":bool_normal_value,"alarmRecord":bool_alarm_value,"loopOverwrite":true}};
            json_str = JSON.stringify(json);
            console.log(json_str);
            $.ajax({
                type:"POST",
                url:"/config",
                data:json_str,
                success:function(data){
                    alert("success");
                }.bind(this),
                error:function(XMLHttpRequest){
                    alert("fail");
                }.bind(this),
                dataType:"json"
            });
            
        }

        if (password === "webui"){
            var value = prompt("Webui Full: input true or false", "false");
            if (value == "" || value == null) {
                return ;
            }

            var bool_value = value == "true" ? true : false;
            if(bool_value == true)
            {
                $("#index_query").show();
                $("#index_config").show();
                $("#index_system").show();
                $("#td-btn-calibration").show();
                $("#td-btn-imageparam").show();
                $("#td-btn-fullscreen").show();
                $("#td-btn-algparam").hide();
            }
            else if(bool_value == false)
            {
                $("#index_query").hide();
                $("#index_config").hide();
                $("#index_system").hide();
                $("#td-btn-calibration").hide();
                $("#td-btn-imageparam").hide();
                $("#td-btn-fullscreen").hide();
                $("#td-btn-algparam").show();
            }
            else
            {
                alert("fail");
                return;
            }

            json = {"accessApi": {"password": ""},"systemConfig": {"webuiFull": bool_value}};
            json_str = JSON.stringify(json);
            console.log(json_str);
            $.ajax({
                type:"POST",
                url:"/config",
                data:json_str,
                success:function(data){
                    alert("success");
                }.bind(this),
                error:function(XMLHttpRequest){
                    alert("fail");
                }.bind(this),
                dataType:"json"
            });  
        }
    },
    
  

    switchConfigs: function (event) {
        var tabId = $(event.target).jqmData("goto-id"), titleId = "#config-title";
        this.$tabConfigs.hide();
        $("#"+tabId).show();
        switch (tabId) {
            case "dvrIdentification":
                $(titleId).text(this.model.getKeyLang("system-info", this.lang));
                break;
            case "videoChannelConfig":
                $(titleId).text(this.model.getKeyLang("channel-conf", this.lang));
                break;
            case "networkConfig":
                $(titleId).text(this.model.getKeyLang("network-conf", this.lang));
                break;
            case "recordingSettings":
                $(titleId).text(this.model.getKeyLang("record-conf", this.lang));
                break;
            case "eventConfig":
                $(titleId).text(this.model.getKeyLang("alarm-conf", this.lang));
                break;
            case "algConfig":
                if (this.model.getMachineType() == "ADA42V1") {
                    $("#alg-ch1").trigger("click");
                }
                break;
        }
    },

    switchChannelConfig: function (event) {
        this.$tabChnConf.hide();
        $("#"+$(event.target).jqmData("tabchn-id")).show();

        return this;
    },

  



    

  

    switchChnAlgType: function(event) {
        chnIdx = $(event.target).jqmData("chn-idx");
        $domSelAlg = $(event.target);
        console.log("ch:"+chnIdx + ", type:"+$domSelAlg.val());
        this.$tabAlgConf.hide();
        $tagPdsConf = $("#tab-pds-conf");
        switch (chnIdx) {
            case 0:
                switch ($domSelAlg.val()) {
                    case "1":
                        $("#tab-adas-conf").show();
                        break;
                    case "3":
                        $tagPdsConf.show();
                        $(".pd-chn-0").show();
                        break;
                }
                break;
            case 1:
                switch ($domSelAlg.val()) {
                    case "2":
                        $("#tab-dms-conf").show();
                        break;
                    case "3":
                        $tagPdsConf.show();
                        $(".pd-chn-1").show();
                        break;
                }
                break;
            case 2:
                switch ($domSelAlg.val()) {
                    case "3":
                        $tagPdsConf.show();
                        $(".pd-chn-2").show();
                        break;
                }
                break;
        }

        return this;
    },

    showErrorInfo: function (action, json) {
        this.lang = this.model.getDevLang();
        console.log("showErrorInfo:"+JSON.stringify(json));
        var actStr = this.model.getKeyLang(action, this.lang) || action;
        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("failure", this.lang));
        this.$popupShowInfo.find("h3").text(actStr);
        switch (json.errorType) {
            case 1:
                json.description = this.model.getKeyLang("err-default-error", this.lang);
                break;
            case 2:
                json.description = this.model.getKeyLang("err-timeout", this.lang);
                break;
            case 3:
                json.description = this.model.getKeyLang("err-no-register", this.lang);
                break;
            case 4:
                json.description = this.model.getKeyLang("err-authority-fail", this.lang);
                window.location.href = "./login.html";
                break;
            case 5:
                json.description = this.model.getKeyLang("err-json-format", this.lang);
                break;
            case 6:
                json.description = this.model.getKeyLang("err-no-enough-memory", this.lang);
                break;
            case 7:
                json.description = this.model.getKeyLang("err-rang-out", this.lang);
                break;
            case 8:
                json.description = this.model.getKeyLang("err-old-pwd-error", this.lang);
                break;
            case 9:
                json.description = this.model.getKeyLang("err-repeat-request", this.lang);
                break;
            case 10:
                json.description = this.model.getKeyLang("err-device-busy", this.lang);
                break;
            case 11:
                json.description = this.model.getKeyLang("err-device-lack", this.lang);
                break;
            case 12:
                json.description = this.model.getKeyLang("err-file-lack", this.lang);
                break;
            case 13:
                json.description = this.model.getKeyLang("err-param-lack", this.lang);
                break;
            case 14:
                json.description = this.model.getKeyLang("err-not-support", this.lang);
                break;
            case 15:
                json.description = this.model.getKeyLang("err-data-not-match", this.lang);
                break;
            default:
                json.description = this.model.getKeyLang("err-unknown", this.lang);
        }
        this.$popupShowInfo.find("p").text(this.model.getKeyLang("reason:", this.lang)+json.description);
        $("#show-info").trigger("click");

        return this;
    },

    showRegRes:function(action,json)
    {
        this.lang = this.model.getDevLang();
        var actStr = this.model.getKeyLang(action, this.lang) || action;
        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("FaceID result", this.lang));
        this.$popupShowInfo.find("h3").text(actStr);      
        json.description = this.model.getKeyLang("err-unknown", this.lang);
        this.$popupShowInfo.find("p").text(this.model.getKeyLang("reason:", this.lang)+json.description);
        $("#show-info").trigger("click");

        return this;
    },

    showSuccessInfo: function (action) {
        this.lang = this.model.getDevLang();
        var actStr = this.model.getKeyLang(action, this.lang) || action;
        console.log(this.lang + ":" + actStr);
        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("success", this.lang));
        this.$popupShowInfo.find("h3").text(actStr);
        this.$popupShowInfo.find("p").text("");
        if (action && action == "rediret-address") {
            this.$popupShowInfo.find("a").hide();
        }
        $("#show-info").trigger("click");

        return this;
    },

    showCalibrationResult: function (action) {
        this.lang = this.model.getDevLang();
        var actStr = this.model.getKeyLang(action, this.lang) || action;
        console.log(this.lang + ":" + actStr);
        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("caliResTitle", this.lang));
        this.$popupShowInfo.find("h3").text(actStr);
        this.$popupShowInfo.find("p").text("");
        $("#show-info").trigger("click");

        return this;
    },

    showUseDhcpInfo: function (action) {
        this.lang = this.model.getDevLang();
        var actStr = this.model.getKeyLang(action, this.lang) || action;
        this.$popupShowInfo.find("h1").text(this.model.getKeyLang("success", this.lang));
        this.$popupShowInfo.find("h3").text(actStr);
        this.$popupShowInfo.find("p").text(this.model.getKeyLang("notify-search", this.lang));
        $("#show-info").trigger("click");

        return this;
    },

    showRebooting: function () {
        $('#show-reboot').click();
        var waitSec = 20;
        function waitCallback(){
            waitSec--;
            console.log(waitSec);
            $("#wait2-sec").text(waitSec);
            if(waitSec > 0){
                window.setTimeout(waitCallback, 1000)
            } else {
                window.location.href = "login.html";
            }
        }
        window.setTimeout(waitCallback, 1000);
        return this;
    },

    showRestoring: function () {
        $('#show-reboot').click(); 
        var waitSec = 20;   
        function waitCallback(){
            waitSec--;
            console.log(waitSec);
            $("#wait2-sec").text(waitSec);
            if(waitSec > 0){
                window.setTimeout(waitCallback, 1000)
            } else {
                if (this.model.getMachineType().indexOf("IPC") != -1) {
                    window.location.href = "http://*************:8080/login.html";
                }
                else if (this.model.getMachineType().indexOf("DMS") != -1) {
                    window.location.href = "http://**************:8080/login.html";
                }
                else if (this.model.getMachineType().indexOf("ADA32") != -1) {
                    window.location.href = "http://**************:8080/login.html";
                }
                else if (this.model.getMachineType().indexOf("ADA42") != -1) {
                    window.location.href = "http://**************:8080/login.html";
                }
                else {
                    window.location.href = "http://************:8080/login.html";
                }
            }
        }
        waitCallback = waitCallback.bind(this);
        window.setTimeout(waitCallback(), 1000);
        return this;
    },

    showLoading: function () {
        $.mobile.loading("show");
        return this;
    },

    hideLoading: function () {
        $.mobile.loading("hide");
        //this.downScroll && this.downScroll.endDownScroll();
        return this;
    },

    popupTrigger: function (event) {
        if ($(event.target).hasClass("btn-popup")) {
            var $btnPopupObj = $(event.target);
        } else {
            $btnPopupObj = $(event.target).parent();
        }
        var popupType = $btnPopupObj.jqmData("popup-type"),
            popupTitle = $btnPopupObj.jqmData("popup-title"), ssid, pwd, index;
        switch (popupType) {
            case "ask-for-sure":
                switch (popupTitle) {
                    case "restore":
                        // if(this.hardware == "ADA32V2" || this.hardware == "ADA32V3" || this.hardware == "ADA32IR" || this.hardware == "ADA32C4")
                        // {
                        //     var restoreType = "hard";
                        // }
                        // else
                        // {
                        //     var restoreType = $("#restore-type").val();
                        // }              
                        var restoreType = "hard";
                        this.$popupAskForSure.find("h1").text(this.model.getKeyLang("restore-factory", this.lang));
                        this.$popupAskForSure.find("h3").text(this.model.getKeyLang("sure-restore?", this.lang));
                        this.$popupAskForSure.find("p").text("");
                        this.$popupAskForSure.jqmData("json", {"type":restoreType});
                        break;
                    case "reboot":
                        this.$popupAskForSure.find("h1").text(this.model.getKeyLang("reboot-system", this.lang));
                        this.$popupAskForSure.find("h3").text(this.model.getKeyLang("sure-reboot?", this.lang));
                        this.$popupAskForSure.find("p").text("");
                        break;
                    case "af-cal":
                        this.$popupAskForSure.find("h1").text(this.model.getKeyLang("recalibrate-camera", this.lang));
                        this.$popupAskForSure.find("h3").text(this.model.getKeyLang("sure-recal?", this.lang));
                        this.$popupAskForSure.find("p").text("");
                        break;
                    case "change-time":
                        this.$popupAskForSure.find("h1").text(this.model.getKeyLang("change-time", this.lang));
                        this.$popupAskForSure.find("h3").text(this.model.getKeyLang("sure-change-time?", this.lang));
                        this.$popupAskForSure.find("p").text("");
                        break;
                    case "format":
                        console.log(this.model.getKeyLang("sure-format-sd?", this.lang));
                        var pos = $("#sel-blk-dev").val(),
                            fsType = "fat32", json = {}, device;
                        if(pos == "-1"){
                            this.$popupAskForSure.find("h1").text(this.model.getKeyLang("format-sd", this.lang));
                            this.$popupAskForSure.find("h3").text(this.model.getKeyLang("Cannot reload sdcard", this.lang));
                            this.$popupAskForSure.find("p").text(this.model.getKeyLang("No Device", this.lang));
                            break;
                        } else if (pos == "9") {
                            if (this.hardware == "ADA47V1") {
                                device = "SD1,EMMC,USB"
                            } else if (this.hardware != "ADA42V1") {
                                device = "SD1,USB"
                            } else {
                                device = "SD1,SD2,USB"
                            }
                        } else {
                            switch (Number(pos)) {
                                case 0:
                                    device = "SD1";
                                    break;
                                case 1:
                                    device = "SD2";
                                    break;
                                case 3:
                                    device = "EMMC";
                                    break;
                                case 4:
                                    device = "USB";
                                    break;
                                default :
                                    device = "unknown";
                                    break;
                            }
                        }
                        json.args = [];
                        var item = {}, item2 = {}, item3 = {};
                        if (pos != "9") {
                            item.formatPos = Number(pos);
                            item.filesystem = fsType;
                            item.formatPartiton = 0;
                            json.args.push(item);
                        } else {
                            item.formatPos = 0;
                            item.filesystem = fsType;
                            item.formatPartiton = 0;
                            json.args.push(item);
                            if (this.hardware == "ADA42V1") {
                                item2.formatPos = 1;
                                item2.filesystem = fsType;
                                item2.formatPartiton = 0;
                                json.args.push(item2);
                            }
                            item3.formatPos = 4;
                            item3.filesystem = fsType;
                            item3.formatPartiton = 0;
                            json.args.push(item3);
                        }

                        console.log(json);
                        this.$popupAskForSure.find("h1").text(this.model.getKeyLang("format-sd", this.lang));
                        this.$popupAskForSure.find("h3").text(this.model.getKeyLang("sure-format-sd?", this.lang));
                        this.$popupAskForSure.find("p").text(device);
                        this.$popupAskForSure.jqmData("json", json);
                        break;
                    case "get-dev-time":
                        this.$popupAskForSure.find("h1").text(this.model.getKeyLang("get-dev-time", this.lang));
                        this.$popupAskForSure.find("h3").text(this.model.getKeyLang("sure-get-time?", this.lang));
                        this.$popupAskForSure.find("p").text("");
                    case "batch-delete":
                        var divArr = $('#userList div ');
                        var deleteUser_list = [];
                        $.each(divArr,function(i,n){
                            var divNode = $(this)
                            divNode.find(".delete_user").is(":checked") && divNode.find(".username").text()!='' &&(deleteUser_list.push(divNode.find(".username").text()));
                        });  
                        var json = {"command":"batchDelete", "userList":deleteUser_list};
                        this.$popupAskForSure.find("h1").text(this.model.getKeyLang("deleteUser", this.lang));
                        this.$popupAskForSure.find("h3").text(this.model.getKeyLang("suredelete", this.lang));
                        this.$popupAskForSure.find("p").text("");
                        this.$popupAskForSure.jqmData("json", json);
                        this.$btnDeleteUser.unbind().click(this.btnDeleteUserHandler);
                }
                this.$popupAskForSure.jqmData("popup-title", popupTitle);
                $("#ask-for-sure").trigger("click");
                break;
            case "form-input2":
                switch (popupTitle) {
                    case "edit-ssid":
                        index = Number($btnPopupObj.jqmData("list-index"));
                        ssid = this.wifiJson[index].ssid;
                        pwd = this.wifiJson[index].password;
                        this.$popupFormInput2.find("h1").text(this.model.getKeyLang("edit-ssid", this.lang));
                        this.$popupFormInput2.find("#form-input2-1").attr("placeholder", this.model.getKeyLang("ssid", this.lang)).val(ssid);
                        this.$popupFormInput2.find("#form-input2-2").attr("placeholder", this.model.getKeyLang("password", this.lang)).val(pwd);
                        this.$popupFormInput2.find("p").text("");
                        this.$popupFormInput2.jqmData("json", {"ssid":ssid, "password":pwd});
                        break;
                    case "add-ssid":
                        this.$popupFormInput2.find("h1").text(this.model.getKeyLang("add-ssid", this.lang));
                        this.$popupFormInput2.find("#form-input2-1").attr("placeholder", this.model.getKeyLang("ssid", this.lang)).val("");
                        this.$popupFormInput2.find("#form-input2-2").attr("placeholder", this.model.getKeyLang("password", this.lang)).val("");
                        this.$popupFormInput2.find("p").text("");
                        break;
                    case "add-scan-ssid":
                        index = Number($btnPopupObj.jqmData("list-index"));
                        ssid = this.scanSsidJson.wifiNewScan[index].ssid;
                        var signal = this.scanSsidJson.wifiNewScan[index].signal;
                        this.$popupFormInput2.find("h1").text(this.model.getKeyLang("add-ssid", this.lang));
                        this.$popupFormInput2.find("#form-input2-1").attr("placeholder", this.model.getKeyLang("ssid", this.lang)).val(ssid);
                        this.$popupFormInput2.find("#form-input2-2").attr("placeholder", this.model.getKeyLang("password", this.lang)).val("");
                        this.$popupFormInput2.find("p").text("");
                        this.$popupFormInput2.jqmData("json", {"ssid":ssid, "signal":signal});
                        break;
                }
                this.$popupFormInput2.jqmData("popup-title", popupTitle);
                $("#form-input2").trigger("click");
                break;
            case "form-input3":
                switch (popupTitle) {
                    case "change-pwd":
                        this.$popupFormInput3.find("p").text("");
                        this.$popupFormInput3.find("h1").text(this.model.getKeyLang("change-pwd", this.lang));
                        this.$popupFormInput3.find("#form-input3-0").attr("placeholder", this.model.getKeyLang("change-user", this.lang)).val("");
                        this.$popupFormInput3.find("#form-input3-1").attr("placeholder", this.model.getKeyLang("old-pwd", this.lang)).val("");
                        this.$popupFormInput3.find("#form-input3-2").attr("placeholder", this.model.getKeyLang("new-pwd", this.lang)).val("");
                        this.$popupFormInput3.find("#form-input3-3").attr("placeholder", this.model.getKeyLang("renew-pwd", this.lang)).val("");
                        break;
                }
                this.$popupFormInput3.jqmData("popup-title", popupTitle);
                $("#form-input3").trigger("click");
                break;
			case "form-input4":
                switch (popupTitle) {
                    case "change-time":
                        this.$popupFormInput4.find("h1").text(this.model.getKeyLang("change-time", this.lang));
                        break;
                }
                this.$popupFormInput4.jqmData("popup-title", popupTitle);
                $("#form-input4").trigger("click");
                break;
        }
    },

    btnAskForCancel:function(event){
        var popupTitle = this.$popupAskForSure.jqmData("popup-title"), json;
        switch (popupTitle) {
            case "batch-delete":
                $(".deleteuser_checkbox").hide();
                $("#div-checkall").hide(); 
                document.getElementById("check-all").checked = false;
                this.$inputDeleteAll.trigger("change");
                this.$btnDeleteUser.unbind().click(this.btnDeleteUserHandler);
        }
    },

    btnAskForSure: function (event) {
        var popupTitle = this.$popupAskForSure.jqmData("popup-title"), json;
        switch (popupTitle) {
            case "restore":
                json = this.$popupAskForSure.jqmData("json");
                this.restoreFactoryEvent.notify(json);
                break;
            case "reboot":
                this.rebootEvent.notify();
                break;

            case "af-cal":
                this.afCalEvent.notify();
                break;
            case "change-time":
                this.changeTimeEvent.notify();
                break;

            case "format":
                json = this.$popupAskForSure.jqmData("json");
                console.log(json);
                this.formatSdEvent.notify(json);
                break;
            case "get-dev-time":
                this.btnGetDevTimeEvent.notify();
                break;
            case "batch-delete":
                json = this.$popupAskForSure.jqmData("json");
                this.registerEvent.notify(json);
                document.getElementById("check-all").checked = false;
                $("#div-checkall").hide();             

        }
     },

    btnFormInput2: function (event) {
        console.log($(event.target));
        var popupTitle = this.$popupFormInput2.jqmData("popup-title"), json, ssid, password;
        console.log(popupTitle);
        switch (popupTitle) {
            case "edit-ssid":
                json = this.$popupFormInput2.jqmData("json");
                ssid = this.$popupFormInput2.find("#form-input2-1").val();
                password = this.$popupFormInput2.find("#form-input2-2").val();
                console.log(ssid+":"+password);
                if (ssid.length == 0 || password.length == 0) {
                    this.$popupFormInput2.find("p").text(this.model.getKeyLang("ssid-pwd-cannot-empty!", this.lang));
                } else if (password.length < 8) {
                    this.$popupFormInput2.find("p").text(this.model.getKeyLang("pwd-length-atless-8char", this.lang));
                } else {
                    for (var i=0; i<this.wifiJson.length; i++) {
                        if (this.wifiJson[i].ssid == json.ssid && this.wifiJson[i].password == json.password) {
                            this.wifiJson[i].ssid = ssid;
                            this.wifiJson[i].password = password;
                            break;
                        }
                    }
                    this.$popupFormInput2.popup("close");
                    this.refreshSsidList();
                }
                break;

            case "add-ssid":
                json = this.$popupFormInput2.jqmData("json");
                ssid = this.$popupFormInput2.find("#form-input2-1").val();
                password = this.$popupFormInput2.find("#form-input2-2").val();
                console.log(ssid+":"+password);
                if (ssid.length == 0 || password.length == 0) {
                    this.$popupFormInput2.find("p").text(this.model.getKeyLang("ssid-pwd-cannot-empty!", this.lang));
                } else if (password.length < 8) {
                    this.$popupFormInput2.find("p").text(this.model.getKeyLang("pwd-length-atless-8char", this.lang));
                } else if (this.wifiJson.length >= 32) {
                    this.$popupFormInput2.find("p").text(this.model.getKeyLang("save-ssid-list-full!", this.lang));
                } else {
                    this.wifiJson.push({"ssid":ssid, "password":password});
                    this.$popupFormInput2.popup("close");
                    this.refreshSsidList();
                }
                break;
            case "add-scan-ssid":
                json = this.$popupFormInput2.jqmData("json");
                ssid = this.$popupFormInput2.find("#form-input2-1").val();
                password = this.$popupFormInput2.find("#form-input2-2").val();
                var signal = json.signal;
                if (ssid.length == 0 || password.length == 0) {
                    this.$popupFormInput2.find("p").text(this.model.getKeyLang("ssid-pwd-cannot-empty!", this.lang));
                } else if (password.length < 8) {
                    this.$popupFormInput2.find("p").text(this.model.getKeyLang("pwd-length-atless-8char", this.lang));
                } else if (this.model.getScanSsidRememberLenght() >= 11) {
                    this.$popupFormInput2.find("p").text(this.model.getKeyLang("save-ssid-list-full!", this.lang));
                } else {
                    this.addScanSsidEvent.notify({"ssid":ssid, "password":password, "signal":signal});
                    this.$popupFormInput2.popup("close");
                }
                break;
        }
    },

    btnFormInput3: function () {
        var popupTitle = this.$popupFormInput3.jqmData("popup-title");
        console.log(popupTitle);
        var invalidPwd = false;
        switch (popupTitle) {
            case "change-pwd":
                var change_user = this.$popupFormInput3.find("#form-input3-0").val(),
                    old_pwd = this.$popupFormInput3.find("#form-input3-1").val(),
                    new_pwd = this.$popupFormInput3.find("#form-input3-2").val(),
                    renew_pwd = this.$popupFormInput3.find("#form-input3-3").val();
                $(".pwd-limit").each(function(i, e){
                    var exp=/[~！￥……（）——{}|：“”《》？；【】。，·、‘’\s]/;
                    if(e.value.match(exp))
                    {
                        invalidPwd= true;
                    }
                });
                
                if(invalidPwd)
                {
                    this.$popupFormInput3.find("p").text(this.model.getKeyLang("pwd-illegal-character!", this.lang));
                }
                else if (new_pwd != renew_pwd)
                {
                    this.$popupFormInput3.find("p").text(this.model.getKeyLang("pwd-not-accordance!", this.lang));
                } 
                else
                {
                    this.$popupFormInput3.popup("close");
                    this.changePasswordEvent.notify({"changeUser":change_user, "oldPassword":old_pwd, "newPassword":new_pwd});
                }
                break;
        }
    },
    
	btnFormInput4: function () {
        var popupTitle = this.$popupFormInput4.jqmData("popup-title");
        console.log(popupTitle);
        switch (popupTitle) {
            case "change-time":   
                this.$popupFormInput4.popup("close");
                this.changeTimeEvent.notify();   
                break;
        }
    },

    checkDHCP: function (event) {
        var checked = $(event.target).is(":checked"),
            $Ipaddr = $("#networkConfig-ethernet-ipAddress"),
            $SubMask=$("#networkConfig-ethernet-subnetMask"),
            $Gateway=$("#networkConfig-ethernet-gateway");
        console.log("checkDHCP:"+checked);
        if (checked) {
            $Ipaddr.attr("readonly", true).css("color", "#aaa");
            $SubMask.attr("readonly", true).css("color", "#aaa");
            $Gateway.attr("readonly", true).css("color", "#aaa");
        } else {
            $Ipaddr.attr("readonly", false).css("color", "#666");
            $SubMask.attr("readonly", false).css("color", "#666");
            $Gateway.attr("readonly", false).css("color", "#666");
        }
    },

    checkNTP: function (event) {
        var checked = $(event.target).is(":checked"),
            $ntpIpaddr= $("#systemConfig-time-serverAddress"),
            $ntpInterval = $("#systemConfig-time-interval");
        console.log("checkNTP:"+checked);
        if (checked) {
            $ntpIpaddr.attr("readonly", false).css("color", "#666");
            $ntpInterval.attr("readonly", false).css("color", "#333");
        } else {
            $ntpIpaddr.attr("readonly", true).css("color", "#aaa");
            $ntpInterval.attr("readonly", true).css("color", "#666");
        }
    },

    sendmediaJson:function(){
        toggle=1;
    },
    sendalgJson:function(){
        toggle=4;
    },
    sendnetworkJson:function(){
        toggle=2;
    },
    sendsysJson:function(){
        toggle=3;
    },
    btnSubmit: function () {
        var inputEmpty = false, invalidIp = false, invalidMac = false, ifrmRangOut = false, invalidRcMode = false, invalidRcMode2 = false, invalidRcMode3 = false, invalidFramerate = false, prefixErr = false, invalidSsid = false, invalidSsid22 = false, invalidPwd = false,invaliCanId = false;
        var mainQRangOut = false;
        var id = "";
        $(".empty-limit").each(function(i, e){
            var display = $('#'+e.id).parent().css('display');        
            (display == 'block') && (e.value.length == 0) && (inputEmpty = true) && (id = e.id);
        });
        $(".ipaddr-limit").each(function(i, e){
            var exp=/^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-4])$/;
            (!e.value.match(exp)) && (invalidIp = true);
        });
        $(".canid-limit").each(function(i,e){
            var exp=/0x[0-9a-f]{8}/;
            console.log("canid length:"+e.value.length);
            if((e.value.length != 10) || (!e.value.match(exp)))
            {
                invaliCanId = true;
                return false;
            }
            else
            {
                invaliCanId = false;
                return true;
            }           
        });

        $(".macaddr-limit").each(function(i, e){
            var exp=/^[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}:[A-Fa-f0-9]{2}$/;
            (!e.value.match(exp)) && (invalidMac = true);
        });
        if (this.model.getMachineType() != "ADA42V1") {
            var mainFramerate = this.$selMainStreamFramerate.val();
            var subFramerate = this.$selSubStreamFramerate.val();
            var mainIfrmInvalid = false, subIfrmInvalid = false;
            $(".ifrm-limit").each(function(i, e){
                if (e.id == "mediaConfig-mainStream-ifrmInterval" && Number(e.value) < Number(mainFramerate)) {
                    ifrmRangOut = true;
                    mainIfrmInvalid = true;
                } else if (e.id == "mediaConfig-subStream-ifrmInterval" && Number(e.value) < Number(subFramerate)) {
                    ifrmRangOut = true;
                    subIfrmInvalid = true;
                }
            });
        }
        if(this.model.getMachineType() =="IPCR20S2"){
            var mainEncode=this.$selMainStreamEncode.val();
            var mainRcMode = this.$selMainStreamRcMode.val();
            if(mainEncode=="MJPEG" && mainRcMode=="ABR"){
                invalidRcMode = true;
            }
        }
        if(this.model.getMachineType() =="IPCR20S4" || this.model.getMachineType() =="IPCR20S3" || this.model.getMachineType() =="IPTR20S1" || this.model.getMachineType() =="WFCR20S2"){           
            var mainEncode= this.$selMainStreamEncode.val();
            var mainRcMode = this.$selMainStreamRcMode.val();
            if(mainEncode=="MJPEG" && mainRcMode!="FIXQP" && mainRcMode!="CBR"){
                invalidRcMode2 = true;
            }
            if(mainEncode!="MJPEG" && mainRcMode=="FIXQP"){
                invalidRcMode3 = true;
            }
        }
        if(this.model.getMachineType() =="IPCR20S3" || this.model.getMachineType() =="IPCR20S4"){
            var videoMode = this.$selVideoMode.val();
            var mainFramerate = this.$selMainStreamFramerate.val();
            var subFramerate = this.$selSubStreamFramerate.val();
            if(videoMode=="PAL" && (mainFramerate > 25 || subFramerate > 25)){
                invalidFramerate = true;
            }
        }
        var config = this.model.getConfigJson();
        $(".pwdlen-limit").each(function(i, e){
            (e.value.length != 0) && (e.value.length < 8) && (invalidPwd = true) && (id = e.id);
        });
        if (inputEmpty || invalidIp || invalidMac || ifrmRangOut || prefixErr || invalidSsid || invalidSsid22 || invalidPwd || invaliCanId || invalidRcMode || invalidRcMode2 || invalidRcMode3 || invalidFramerate || mainQRangOut) {
            var errType, errReason,ENerr,ENreason;
            if (inputEmpty) {
                var ENerr =  "Some input cannot be empty!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                var ENreason =  "Item:[] cannot be empty.";
                tmp = this.model.getKeyLang(ENreason, this.lang) || ENreason;
                preRea = tmp.substring(0, tmp.indexOf('['));
                sufRea = tmp.substring(tmp.indexOf(']') + 1);
                errReason = preRea+"["+id+"]"+sufRea;
            } else if (invalidIp) {       
                ENerr = "Network Config Error!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                ENreason = "there is some ip address is illegal";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            } else if (invalidMac) {
                ENerr = "Network Config Error!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                ENreason = "Network Config Error!";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            }
            else if (ifrmRangOut) {
                ENerr = "Video Config Error!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                ENreason = mainIfrmInvalid ? "Main Stream " : "Sub Stream ";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
                var sufrea = "Iframe Interval is out of range.";
                errReason += this.model.getKeyLang(sufrea, this.lang) || sufrea;
            }      
            else if (prefixErr) {
                ENerr = "WiFi Config Error!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                ENreason = "The AP SSID name must be prefix as: ";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
                // errReason =+ config.networkConfig.wifi.apMode.ssid.substr(0, ssidPreLen);
            }
            else if (invalidSsid) {
                ENerr = "SSID lenght Error!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                ENreason = "STA SSID lenght can not exceed 32";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            }
            else if (invalidSsid22) {
                ENerr = "SSID lenght Error!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                ENreason = "AP SSID lenght can not exceed 22";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            }
            else if (invalidPwd) {
                ENerr ="SSID lenght Error!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                ENreason =  "SSID lenght must be empty or more than 8";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            }
            else if(invaliCanId){
                ENerr ="CanId Config Error!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                ENreason =  "CanId is illegal";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            }
			else if(invalidRcMode){
                ENerr="RcMode Config Error!";
                errType = this.model.getKeyLang(ENerr,this.lang) || ENerr;
                ENreason = "Can't select ABR when encode is MJPEG !";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            }
			else if(invalidRcMode2){
                ENerr="RcMode Config Error!";
                errType = this.model.getKeyLang(ENerr,this.lang) || ENerr;
                ENreason = "Only Support CBR and FIXQP when encode is MJPEG !";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            }
            else if(invalidRcMode3){
                ENerr="RcMode Config Error!";
                errType = this.model.getKeyLang(ENerr,this.lang) || ENerr;
                ENreason = "Only MJPEG Support FIXQP !";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            }
            else if(invalidFramerate){
                ENerr="FrameRate Config Error!";
                errType = this.model.getKeyLang(ENerr,this.lang) || ENerr;
                ENreason = "The framerate should not exceed 25 in PAL !";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            }
            else if (mainQRangOut) {
                ENerr = "mainQ Config Error!";
                errType = this.model.getKeyLang(ENerr, this.lang) || ENerr;
                ENreason = "The value of mainQ must be greater than 0 but less than 100!";
                errReason = this.model.getKeyLang(ENreason, this.lang) || ENreason;
            } 
            this.$popupShowInfo.find("h1").text(this.model.getKeyLang("failure", this.lang));
            this.$popupShowInfo.find("h3").text(errType);
            this.$popupShowInfo.find("p").text(errReason);
            $("#show-info").trigger("click");
        } else {
            // console.log($("#mediaConfig-jpegStream-resolution").val());
            this.submitConfigsEvent.notify(this.collectValue());
        }
    },

    btnCancel: function () {
        console.log("btnCancel");
        this.cancelConfigsEvent.notify();
    },

    selectDMSsensitivity: function(event){
        var value = $(event.target).val(); 
        $speedlimit = $("#algConfig-dmsSpeedLimit");
        console.log("selectDMSsensitivity:"+value);
        if (value >= 0) {
            $speedlimit.css("display", "none");
        } else {
            $speedlimit.css("display", "block");
        }
    },

    selMainStreamEncode:function(event)
    {
        var mainEncode=this.$selMainStreamEncode.val();
        console.log(mainEncode);
        if(this.model.getMachineType() =="IPCR20S4" || this.model.getMachineType() =="IPTR20S1" || this.model.getMachineType() =="WFCR20S2")
        {
            if(mainEncode == "MJPEG")
            {
                $(".mainstream_rc1").hide();
                $(".mainstream_rc2").show();
            }
            else
            {
                $(".mainstream_rc1").show();
                $(".mainstream_rc2").hide();
            }
        }

        if(this.model.getMachineType() =="ADA32V3")
        {
            if(mainEncode == "H264" || mainEncode == "H265")
            {
                $(".br_hide1").hide();
            }
            else
            {
                $(".br_hide1").show();
            }
        }  

        if(this.model.getMachineType() =="IPCR20S4")
        {
            if(mainEncode == "H264" || mainEncode == "H265")
            {
                $(".br_hide1").hide();
                $(".br_hide2").hide();
            }
            else
            {
                $(".br_hide1").show();
                $(".br_hide2").show();
            }
        }
    },

    selMainStremRcMode:function(event)
    {
        var mainRcMode =this.$selMainStreamRcMode.val();
        if(mainRcMode == "FIXQP")
        {
            //$("#div-mainQ-value").show();
        }
        else
        {
            //$("#div-mainQ-value").hide();
        }
    },

    enablePdAlarmOutAuto:function()
    {
        var auto = this.$checkPdAlarmAuto.is(":checked");
        var pdAlarmOut_range = $("#algConfig-pd-pdAlarmOutInterval");
        var pdAlarmOut_p = $("#pdalarm_interval_value");
        if(auto == true)
        {
         
                pdAlarmOut_range.css("background","-webkit-linear-gradient(var(--theme_disenable_text_color),var(--theme_disenable_text_color)) no-repeat #ececec");
                pdAlarmOut_p.css("color","var(--theme_disenable_text_color)");
            
            document.getElementById("algConfig-pd-pdAlarmOutInterval").disabled = true;           
            pdAlarmOut_range.val(-1);   
            pdAlarmOut_p.text(" ");
            pdAlarmOut_range.attr({"min" : -1});
            pdAlarmOut_range.css("background-size","0.0%");
        }
        else
        {
          
            pdAlarmOut_range.css("background","-webkit-linear-gradient(var(--theme_color),var(--theme_color)) no-repeat #ececec");
            pdAlarmOut_p.css("color","var(--theme_text_color)");
            document.getElementById("algConfig-pd-pdAlarmOutInterval").disabled = false;
            pdAlarmOut_range.val(2000);
            pdAlarmOut_p.text(2000);
            pdAlarmOut_range.attr({"min" : 0});
            pdAlarmOut_range.css("background-size","20%");
        }
    },

    enableIrPdAlarmOutAuto:function()
    {
        var auto = this.$checkIrPdAlarmAuto.is(":checked");
        var pdAlarmOut_range = $("#algConfig-irAlgConfig-pdsConf-pdAlarmOutInterval");
        var pdAlarmOut_p = $("#ir_pdalarm_interval_value");
        if(auto == true)
        {
            pdAlarmOut_range.css("background","-webkit-linear-gradient(var(--theme_disenable_text_color),var(--theme_disenable_text_color)) no-repeat #ececec");
            pdAlarmOut_p.css("color","var(--theme_disenable_text_color)");
            document.getElementById("algConfig-irAlgConfig-pdsConf-pdAlarmOutInterval").disabled = true;           
            pdAlarmOut_range.val(-1);   
            pdAlarmOut_p.text(" ");
            pdAlarmOut_range.attr({"min" : -1});
            pdAlarmOut_range.css("background-size","0.0%");
        }
        else
        {
            pdAlarmOut_range.css("background","-webkit-linear-gradient(var(--theme_color),var(--theme_color)) no-repeat #ececec");
            pdAlarmOut_p.css("color","var(--theme_text_color)");
            document.getElementById("algConfig-irAlgConfig-pdsConf-pdAlarmOutInterval").disabled = false;
            pdAlarmOut_range.val(2000);
            pdAlarmOut_p.text(2000);
            pdAlarmOut_range.attr({"min" : 0});
            pdAlarmOut_range.css("background-size","20%");
        }
    },

    enableZoomAlarmOutAuto:function()
    {
        let auto = this.$checkZoomAlarmAuto.is(":checked");
        let zoomAlarmOut_range = $("#algConfig-zoomConf-alarmOutInterval");
        let zoomAlarmOut_p = $("#zoomalarm_interval_value");
        if(auto == true)
        {
            zoomAlarmOut_range.css("background","-webkit-linear-gradient(var(--theme_disenable_text_color),var(--theme_disenable_text_color)) no-repeat #ececec");
            zoomAlarmOut_p.css("color","var(--theme_disenable_text_color)");
            document.getElementById("algConfig-zoomConf-alarmOutInterval").disabled = true;           
            zoomAlarmOut_range.val(-1);   
            zoomAlarmOut_p.text(" ");
            zoomAlarmOut_range.attr({"min" : -1});
            zoomAlarmOut_range.css("background-size","0.0%");
        }
        else
        {
            zoomAlarmOut_range.css("background","-webkit-linear-gradient(var(--theme_color),var(--theme_color)) no-repeat #ececec");
            zoomAlarmOut_p.css("color","var(--theme_text_color)");
            document.getElementById("algConfig-zoomConf-alarmOutInterval").disabled = false;
            zoomAlarmOut_range.val(2000);
            zoomAlarmOut_p.text(2000);
            zoomAlarmOut_range.attr({"min" : 0});
            zoomAlarmOut_range.css("background-size","20%");
        }
    },

    selectRecordMode: function(event){
        var value = $(event.target).val(); 
        $recCrtl0 = $("#systemConfig-recCrtl0");
        console.log("selectRecordMode:"+value);
        if (value == 0) {
            $recCrtl0.css("display", "none");
        } else {
            $recCrtl0.css("display", "block");
        }
    },
    

    checkRecChn: function (event) {
        var jpegId = "#"+$(event.target).jqmData("attach-id");
        $(jpegId)[0].checked = event.target.checked;
    },

    btnRefresh: function () {
        toggle=1;
        var btnTitle = $(event.target).jqmData("btn-title");
        btnTitle = btnTitle || "refresh-"+this.page+"-info";
        console.log(btnTitle);
        switch (btnTitle) {
            case "refresh-config-info":
                this.refreshConfigsEvent.notify();
                break;
            case "refresh-device-info":
                this.refreshDevInfoEvent.notify({"device":this.$btnCurTab.jqmData("goto-id").substring(4)});
                break;
            case "refresh-query-info":
                this.refreshRecordEvent.notify();
                break;
        }
    },

    inputCharNormal: function (event) {
        $(event.target).val($(event.target).val().replace(/[^\u00C0-\u017F\d\w\~\@\#\$\^\~\!\@\#\$\%\^\&\*\(\)\_\-\+\=\{\[\}\]\:\;\"\'\|\\\<\,\>\.\?\/]/g,''));
    },

    pageIsSelectmenuDialog: function( page ) {
        var isDialog = false,
            id = page && page.attr( "id" );

        $( ".filterable-select" ).each( function() {
            if ( $( this ).attr( "id" ) + "-dialog" === id ) {
                isDialog = true;
                return false;
            }
        });

        return isDialog;
    },

    onSelectmenucreate: function( event ) {
        var input,
            selectmenu = $( event.target ),
            list = $( "#" + selectmenu.attr( "id" ) + "-menu" ),
            form = list.jqmData( "filter-form" );

        // We store the generated form in a variable attached to the popup so we avoid creating a
        // second form/input field when the listview is destroyed/rebuilt during a refresh.
        if ( !form ) {
            input = $( "<input data-type='search'></input>" );
            form = $( "<form></form>" ).append( input );

            input.textinput();

            list
                .before( form )
                .jqmData( "filter-form", form )	;
            form.jqmData( "listview", list );
        }

        // Instantiate a filterable widget on the newly created selectmenu widget and indicate that
        // the generated input form element is to be used for the filtering.
        selectmenu
            .filterable({
                input: input,
                children: "> option[value]"
            })

            // Rebuild the custom select menu's list items to reflect the results of the filtering
            // done on the select menu.
            .on( "filterablefilter", function() {
                selectmenu.selectmenu("refresh");
            });
    },

    onPagecontainerbeforeshow: function( event, data ) {
        var listview, form;

        // We only handle the appearance of a dialog generated by a filterable selectmenu
        if ( !pageIsSelectmenuDialog( data.toPage ) ) {
            return;
        }

        listview = data.toPage.find( "ul" );
        if (!listview) {
            return;
        }
        form = listview.jqmData( "filter-form" );

        // Attach a reference to the listview as a data item to the dialog, because during the
        // pagecontainerhide handler below the selectmenu widget will already have returned the
        // listview to the popup, so we won't be able to find it inside the dialog with a selector.
        data.toPage.jqmData( "listview", listview );

        // Place the form before the listview in the dialog.
        listview.before( form );
    },

    onPagecontainerhide: function( event, data ) {
        var listview, form;

        // We only handle the disappearance of a dialog generated by a filterable selectmenu
        if ( !pageIsSelectmenuDialog( data.toPage ) ) {
            return;
        }

        listview = data.prevPage.jqmData( "listview" );
        if (!listview) {
            return;
        }
        form = listview.jqmData( "filter-form" );

        // Put the form back in the popup. It goes ahead of the listview.
        listview.before( form );
    }
};

