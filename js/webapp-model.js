/**
 * Created by <PERSON> on 2017/9/26.
 */
var Event = function (sender) {
	this._sender = sender;
	this._listeners = [];
};

Event.prototype = {
	attach: function (listener) {
		this._listeners.push(listener);
	},

	notify: function (args) {
		for (var i = 0; i < this._listeners.length; i += 1) {
			this._listeners[i](this._sender, args);
		}
	},
};

/* 构造函数形式 */
var WebappModel = function () {
	this.lang = general_lang;
	this.configJsonTmpl = json_tmpl = {
		mediaConfig: {
			flipEnable: "boolean",
			jpegResolution: "string",
			mainBitrate: "number",
			mainEncode: "string",
			mainFramerate: "number",
			mainIframeInt: "number",
			mainRcMode: "string",
			mainResolution: "string",
			mirrorEnable: "boolean",
			showGuiMask: "mask",
			subBitrate: "number",
			subEncode: "string",
			subFramerate: "number",
			subIframeInt: "number",
			subRcMode: "string",
			subResolution: "string",
			volume: "number",
			format: "string",
			voFormat: "string",
		},
		algConfig: {
			pd: {
				audioType: "number",
				bMosaic: "boolean",
				detectPart: "number",
				pdAlarmIn: "number",
				pdAlarmInTrigger: "number",
				shelterEnable: "boolean",
				pdAlarmOutShelter: "boolean",
				shelterAudio: "boolean",
				ShelterTimelimit: "number",
				pdAlarmOutEnable: {
					red: "boolean",
					yellow: "boolean",
					green: "boolean",
				},
				pdAlarmOutInterval: "number",
				pdAlarmOutTrigger: "number",
				pdBlkSize: "number",
				pdGreenSensitivity: ["number", "number", "number"],
				// pdHollow: "number",
				pdInterval: {
					red: "number",
					yellow: "number",
					green: "number",
				},
				pdOsdFontSize: "number",
				pdRectPerson: "boolean",
				pdRedSensitivity: ["number", "number", "number"],
				/* pdRoiBoard */
				/* pdRoiEllipseB */
				pdRoiEnable: {
					red: "boolean",
					yellow: "boolean",
					green: "boolean",
				},
				pdRoiGui: "number",
				/* pdRoiOutline */
				// pdRoiStyle: "number",
				pdSensitivity: "number",
				pdTestMode: "number",
				pdYellowSensitivity: ["number", "number", "number"],
				pdsModel: "number",
				// shelterEnable: "boolean",
			},
		},
		networkConfig: {
			dnsServer: "string",
			ethernet: {
				enableDHCP: "boolean",
				// dhcpTimeout: "number",
				ipAddress: "string",
				subnetMask: "string",
				gateway: "string",
				macAddress: "string",
			},
			wifi: {
				apMode: {
					authMode: "string",
					ssid: "string",
					apIpAddress: "string",
					password: "string",
					frequency: "string",
					countryCode: "string",
					// set2GChannel: "number",
					set5GChannel: "number",
				},
				staMode: {
					enable: "boolean",
					ssid: "string",
					password: "string",
				},
			},
			// "4g": {
			// 	apn: "string",
			// 	apnUser: "string",
			// 	apnPassword: "string",
			// },
			// protocol: {
			// 	rtspServPort: "number",
			// 	onvifServPort: "number",
			// 	discoverable: "boolean",
			// 	multicastEnable: "boolean",
			// 	multicastPort: "number",
			// 	avtpStreamID: "number",
			// 	avtpDestMAC: "string",
			// },
		},
		serverConfig: {
			enable: "boolean",
			// recordLevel: "number",
			// uploadLevel: "number",
			serverAddress: "string",
			deviceID: "string",
			// uploadFileOpts: "number",
			// netType: "number",
			// GB28181Enable: "boolean",
			// SIPServerId: "string",
			// SIPServerIp: "string",
			// DevSIPId: "string",
			SIPDOMAIN: "string",
			SIPPwd: "string",
			// SIPServerPort: "number",
			// DevSIPPort: "number",
			// KaPeriod: "number",
			// RegInterval: "number",
			// bb808Enable: "boolean",
			// bb808ServerIP: "string",
			// bb808ServerPort: "number",
			bb808DeviceID: "string",
		},
		systemConfig: {
			timeoutSleep: "number",
			enable4g: "boolean",
			enableSDAlarm: "boolean",
			enableSDLedAlarm: "boolean",
			enableStorage: "boolean",
			normalRecord: "number",
			alarmRecord: "number",
			loopOverwrite: "boolean",
			preRecDuration: "number",
			postRecDuration: "number",
			recFileLen: "number",
			recFileType: "string",
			recChn: "number",
			time: {
				UTChour: "number",
				UTCminute: "number",
				enableGPStime: "boolean",
				ntpEnable: "boolean",
				serverAddress: "string",
				interval: "number",
				daylightTime: "boolean",
			},
			dmmCanid: "string",
			frsCanid: "string",
			heartCanid: "string",
			dmsExtCanid: "string",
			pdsCanid: "string",
			pdsExtCanid: "string",
			apcCanid: "string",
			frameFormat: "number",
			baudrate: "number",
			language: "string",
		},
	};

	this.jsonPath = [];
	this.configJson = {};
	this.cellularJson = {};
	this.scanSsidJson = {};
	this.cameraJson = {};
};

WebappModel.prototype = {
	getKeyLang: function (key, lang) {
		lang = lang || "EN";
		if (lang == "INC" || lang == "TEL") lang = "EN";
		try {
			return this.lang[key][lang];
		} catch (error) {
			console.error(error + ":key[" + key + "]");
		}
	},

	updateKeyLang: function (key, lang, translation) {
		try {
			this.lang[key][lang] = translation;
		} catch (error) {
			console.log(error + ":key[" + key + "]");
		}
	},

	getConfigJsonTmpl: function () {
		return this.configJsonTmpl;
	},

	setConfigJson: function (configJson) {
		this.configJson = $.extend(true, {}, configJson);

		console.log("configJson", configJson);
		return this;
	},

	appendPasswordJson: function (passwordJson) {
		this.configJson.managementServerConfig.password =
			passwordJson.managementPassword;
		this.configJson.mediaDeliveryConfig.accessKey = passwordJson.s3AccessKey;
		this.configJson.mediaDeliveryConfig.secretKey = passwordJson.s3SecretKey;
		this.configJson.networkConfig.wifi.password = passwordJson.wifiPassword;
		for (var i = 0; i < passwordJson.extraWifiPasswordList.length; i++) {
			this.configJson.networkConfig.wifi.extraWifiList[i].password =
				passwordJson.extraWifiPasswordList[i];
		}
		return this;
	},

	appendFactoryStat: function (factoryJson) {
		this.configJson.factoryStat = factoryJson.factoryStat;
	},

	isConfigJsonComplete: function () {
		if (
			this.configJson.hasOwnProperty("dvrIdentification") &&
			this.configJson.managementServerConfig.hasOwnProperty("password") &&
			this.configJson.hasOwnProperty("factoryStat")
		) {
			return true;
		} else {
			return false;
		}
	},

	getConfigJson: function () {
		const {
			ipcIdentification,
			algConfig,
			mediaConfig,
			networkConfig,
			serverConfig,
			systemConfig,
		} = this.configJson;
		let configJson = {
			networkConfig,
			serverConfig,
			systemConfig,
			ipcIdentification,
		};

		if (!Object.keys(this.configJson).length) return configJson;

		if (window.ch === "ch0") {
			configJson = {
				...configJson,
				algConfig: algConfig.algChn0,
				mediaConfig: mediaConfig.chn0,
			};
		} else if (window.ch === "ch1") {
			configJson = {
				...configJson,
				algConfig: algConfig.algChn1,
				mediaConfig: mediaConfig.chn1,
			};
		}

		return configJson;
	},

	getMachineType: function () {
		if (this.configJson.hasOwnProperty("ipcIdentification")) {
			return this.configJson.ipcIdentification.hardware;
		} else {
			return null;
		}
	},

	getSVersion: function () {
		if (this.configJson.hasOwnProperty("ipcIdentification")) {
			return this.configJson.ipcIdentification.sversion;
		} else {
			return null;
		}
	},

	getDevLang: function () {
		var itemLang = window.location.host + "-lang";
		var lang = window.localStorage.getItem(itemLang) || "EN";
		return lang;
	},

	setDevLang: function (lang) {
		if (this.configJson.hasOwnProperty("systemConfig")) {
			this.configJson.systemConfig.language = lang;
		}
	},
	getIsSupportRecord: function () {
		if (!this.configJson.hasOwnProperty("ipcIdentification")) {
			return false;
		}
		var boardtype = this.configJson.ipcIdentification.board;
		if (
			boardtype == 11 ||
			boardtype == 12 ||
			boardtype == 13 ||
			boardtype == 20 ||
			boardtype == 22 ||
			boardtype == 23 ||
			boardtype == 24 ||
			boardtype == 27 ||
			boardtype == 29 ||
			boardtype == 36 ||
			boardtype == 37
		) {
			return true;
		} else {
			return false;
		}
	},

	traverseJson: function (o) {
		if (typeof o == "object") {
			for (var k in o) {
				this.jsonPath.push(k);
				if (typeof o[k] != "object") {
					var key = this.jsonPath.join("-");
					var dom = $("#" + key);
					console.log("正在处理字段:", key, "DOM元素:", dom, "当前值:", dom.val());

					switch (o[k]) {
						case "string":
							o[k] = dom.val();
							console.log("字符串类型处理结果:", key, "=", o[k]);
							break;
						case "number":
							if (dom.val() === "on") {
								o[k] = dom.is(":checked") ? 1 : 0;
							} else {
								o[k] = Number(dom.val());
							}
							console.log("数字类型处理结果:", key, "=", o[k]);
							break;
						case "boolean":
							o[k] = false;
							dom.is(":checked") && (o[k] = true);
							console.log("布尔类型处理结果:", key, "=", o[k]);
							break;
						case "text":
							if (dom.text()) {
								o[k] = dom.text();
							} else {
								delete o[k];
							}
							console.log("文本类型处理结果:", key, "=", o[k]);
							break;
						case "mask":
							var maskVal = 0;
							for (var j = 0; j < 4; j++) {
								var maskDom = $("#" + key + "-" + j);
								maskDom.is(":checked") && (maskVal |= 1 << j);
							}
							o[k] = maskVal;
							console.log("掩码类型处理结果:", key, "=", o[k]);
							break;
					}
				}
				this.traverseJson(o[k]);
			}
			this.jsonPath.pop();
		} else {
			this.jsonPath.pop();
		}
	},

	setCellularJson: function (cellularJson) {
		this.cellularJson = $.extend(true, {}, cellularJson);
		return this;
	},

	appendCellularInfo: function (infoJson) {
		this.cellularJson = $.extend(this.cellularJson, infoJson);
		return this;
	},

	appendCellularConf: function (configJson) {
		this.cellularJson.enable = configJson.networkConfig.cellular.enabled;
		return this;
	},

	appendCellularStat: function (statJson) {
		this.cellularJson.initStat = statJson.cellInitStat;
		this.cellularJson.speedTime = statJson["cellSpeedTime(ms)"];
		return this;
	},

	appendCellularOnline: function (onlineJson) {
		this.cellularJson.onlineFrom = onlineJson.onLine;
		return this;
	},

	isCellularJsonComplete: function () {
		if (
			this.cellularJson.hasOwnProperty("enable") &&
			this.cellularJson.hasOwnProperty("initStat") &&
			this.cellularJson.hasOwnProperty("onlineFrom") &&
			this.cellularJson.hasOwnProperty("moduleName")
		) {
			return true;
		} else {
			return false;
		}
	},

	getCellularJson: function () {
		return this.cellularJson;
	},

	setScanSsidJson: function (scanSsidJson) {
		this.scanSsidJson = $.extend(true, {}, scanSsidJson);
		return this;
	},

	getScanSsidJson: function () {
		return this.scanSsidJson;
	},

	getScanSsidRememberLenght: function () {
		return this.scanSsidJson.wifiRemember.length;
	},

	addScanSsid: function (json) {
		for (var i = 0; i < this.scanSsidJson.wifiNewScan.length; i++) {
			if (this.scanSsidJson.wifiNewScan[i].ssid == json.ssid) {
				this.scanSsidJson.wifiNewScan.splice(i, 1);
				break;
			}
		}
		this.scanSsidJson.wifiRemember.push(json);
		return this;
	},

	delScanSsid: function (json) {
		for (var i = 0; i < this.scanSsidJson.wifiRemember.length; i++) {
			if (
				this.scanSsidJson.wifiRemember[i].ssid == json.ssid &&
				this.scanSsidJson.wifiRemember[i].password == json.password
			) {
				this.scanSsidJson.wifiRemember.splice(i, 1);
				break;
			}
		}
		return this;
	},

	getScanSsidForConfig: function () {
		var json = {
			networkConfig: {
				wifi: {
					ssid: this.scanSsidJson.wifiRemember[0].ssid,
					password: this.scanSsidJson.wifiRemember[0].password,
					extraWifiList: [],
				},
			},
		};
		for (var i = 1; i < this.scanSsidJson.wifiRemember.length; i++) {
			json.networkConfig.wifi.extraWifiList.push({
				ssid: this.scanSsidJson.wifiRemember[i].ssid,
				password: this.scanSsidJson.wifiRemember[i].password,
			});
		}
		return json;
	},

	setSatusJson: function (statusJson) {
		this.statusJson = $.extend(true, {}, statusJson);
		return this;
	},

	getStatusJson: function () {
		return this.statusJson;
	},

	isStatusJsonComplete: function () {
		if (
			this.statusJson.hasOwnProperty("media") &&
			this.statusJson.hasOwnProperty("ethernet") &&
			this.statusJson.hasOwnProperty("clientList")
		) {
			return true;
		} else {
			return false;
		}
	},

	isStatusJsonHasStorage: function () {
		if (
			(this.statusJson.devInfo.hardware != "IPCR20S2" &&
				this.statusJson.devInfo.hardware != "IPCR20S4" &&
				this.statusJson.devInfo.hardware != "ADA42V1" &&
				this.statusJson.devInfo.hardware != "DMS885N" &&
				this.statusJson.devInfo.hardware != "DMS31V2") ||
			this.statusJson.hasOwnProperty("statusList")
		) {
			return true;
		} else {
			return false;
		}
	},

	appendBaseStatus: function (data) {
		this.statusJson = $.extend(true, this.statusJson, data);
		return this;
	},

	appendRtspStatus: function (data) {
		this.statusJson = $.extend(true, this.statusJson, data);
		return this;
	},

	appendStorageStatus: function (data) {
		this.statusJson = $.extend(true, this.statusJson, data);
		return this;
	},
	appendCellStatus: function (data) {
		this.statusJson = $.extend(true, this.statusJson, data);
		return this;
	},
	appendCmsServer: function (data) {
		this.statusJson = $.extend(true, this.statusJson, data);
		return this;
	},
};
